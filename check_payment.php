<?php
// Configurações de cabeçalho para API
header('Content-Type: application/json');

// Configurações de log
ini_set('display_errors', 0);
error_reporting(E_ALL);
$logFile = 'payment_check_log.txt';

// Função para log
function logMessage($message) {
    global $logFile;
    $date = date('[d-M-Y H:i:s e]');
    error_log("$date $message" . PHP_EOL, 3, $logFile);
}

try {
    // Verificar se o ID do pagamento foi fornecido
    if (!isset($_GET['payment_id'])) {
        throw new Exception('ID do pagamento não fornecido');
    }
    
    $paymentId = $_GET['payment_id'];
    logMessage("Verificando pagamento ID: $paymentId");
    
    // Carregar configurações
    if (!file_exists('admin/database/connection.php')) {
        throw new Exception("Arquivo não encontrado: admin/database/connection.php");
    }
    require_once 'admin/database/connection.php';
    
    if (!file_exists('admin/config/asaas.php')) {
        throw new Exception("Arquivo não encontrado: admin/config/asaas.php");
    }
    require_once 'admin/config/asaas.php';
    
    // Verificar se as constantes ASAAS_API_URL e ASAAS_API_KEY estão definidas
    if (!defined('ASAAS_API_URL') || !defined('ASAAS_API_KEY')) {
        throw new Exception('Configurações do Asaas não definidas');
    }
    
    // Inicializar cURL
    $ch = curl_init(ASAAS_API_URL . "/payments/{$paymentId}");
    
    // Configurar requisição
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // Verificar se a função getAsaasHeaders existe
    if (function_exists('getAsaasHeaders')) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
    } else {
        // Fallback para headers diretos
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'access_token: ' . ASAAS_API_KEY
        ]);
    }
    
    // Executar requisição
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    logMessage("Resposta Asaas - Status: $httpCode, Resposta: $response");
    
    // Processar resposta
    $responseData = json_decode($response, true);
    
    if ($httpCode >= 400 || !isset($responseData['status'])) {
        $errorMsg = isset($responseData['errors']) ? json_encode($responseData['errors']) : 'Erro desconhecido';
        throw new Exception("Erro ao verificar pagamento: {$errorMsg}");
    }
    
    // Mapear status da Asaas para nosso sistema
    $statusMap = [
        'PENDING' => 'pending',
        'RECEIVED' => 'approved',
        'CONFIRMED' => 'approved',
        'OVERDUE' => 'expired',
        'REFUNDED' => 'refunded',
        'RECEIVED_IN_CASH' => 'approved',
        'REFUND_REQUESTED' => 'refund_requested',
        'CHARGEBACK_REQUESTED' => 'chargeback',
        'CHARGEBACK_DISPUTE' => 'chargeback',
        'AWAITING_CHARGEBACK_REVERSAL' => 'chargeback',
        'DUNNING_REQUESTED' => 'pending',
        'DUNNING_RECEIVED' => 'approved',
        'AWAITING_RISK_ANALYSIS' => 'pending'
    ];
    
    $status = isset($statusMap[$responseData['status']]) ? $statusMap[$responseData['status']] : 'pending';
    logMessage("Status mapeado: $status");
    
    // Atualizar status no banco de dados
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        $stmt = $pdo->prepare("UPDATE orders SET status = ? WHERE payment_id = ?");
        $stmt->execute([$status, $paymentId]);
        logMessage("Status atualizado no banco de dados");
        
    } catch (PDOException $e) {
        logMessage("Erro de banco de dados: " . $e->getMessage());
        // Não lançar exceção aqui para não interromper o fluxo
    }
    
    // Preparar mensagem baseada no status
    $message = 'Aguardando pagamento...';
    
    if ($status === 'approved') {
        $message = 'Pagamento aprovado!';
    } else if ($status === 'expired') {
        $message = 'Pagamento expirado.';
    } else if ($status === 'refunded' || $status === 'refund_requested') {
        $message = 'Pagamento estornado.';
    } else if (strpos($status, 'chargeback') !== false) {
        $message = 'Pagamento contestado.';
    }
    
    // Retornar dados para o cliente
    echo json_encode([
        'success' => true,
        'status' => $status,
        'message' => $message,
        'order_id' => $responseData['externalReference'] ?? $paymentId
    ]);
    
} catch (Exception $e) {
    logMessage("Erro: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage()
    ]);
}
?>

