<?php
/**
 * <PERSON>ript para corrigir e criar todas as tabelas necessárias
 * Execute este arquivo para garantir que todas as tabelas estejam corretas
 */

require_once 'database/connection.php';

try {
    echo "<h2>🔧 Corrigindo Estrutura do Banco de Dados</h2>\n";
    
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h3>1. Criando tabela 'users'...</h3>";
    
    // Tabela users (principal para o sistema)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            balance DECIMAL(10,2) DEFAULT 0.00,
            is_admin TINYINT(1) DEFAULT 0,
            is_online TINYINT(1) DEFAULT 0,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'users' criada/verificada<br>";
    
    echo "<h3>2. Criando tabela 'chat_messages'...</h3>";
    
    // Tabela chat_messages
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL,
            is_admin TINYINT(1) DEFAULT 0,
            timestamp INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_timestamp (timestamp),
            INDEX idx_is_admin (is_admin),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'chat_messages' criada/verificada<br>";
    
    echo "<h3>3. Criando tabela 'admins'...</h3>";
    
    // Tabela admins
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'admin',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'admins' criada/verificada<br>";
    
    echo "<h3>4. Verificando se admin padrão existe...</h3>";
    
    // Verificar se admin padrão existe
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admins WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $adminExists = $stmt->fetchColumn();
    
    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'super_admin']);
        echo "✅ Admin padrão criado (login: admin, senha: admin123)<br>";
    } else {
        echo "✅ Admin padrão já existe<br>";
    }
    
    echo "<h3>5. Verificando estrutura das tabelas...</h3>";
    
    // Verificar se a coluna username existe na tabela users
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('username', $columns)) {
        echo "✅ Coluna 'username' existe na tabela 'users'<br>";
    } else {
        echo "❌ Coluna 'username' NÃO existe na tabela 'users'<br>";
        // Adicionar coluna username se não existir
        try {
            $pdo->exec("ALTER TABLE users ADD COLUMN username VARCHAR(50) NOT NULL UNIQUE AFTER id");
            echo "✅ Coluna 'username' adicionada à tabela 'users'<br>";
        } catch (Exception $e) {
            echo "⚠️ Erro ao adicionar coluna 'username': " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>6. Testando inserção de usuário...</h3>";
    
    // Testar inserção de usuário
    try {
        $testUsername = 'test_user_' . rand(1000, 9999);
        $testEmail = 'test_' . rand(1000, 9999) . '@test.com';
        $testPassword = password_hash('test123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, balance) VALUES (?, ?, ?, 0.00)");
        $stmt->execute([$testUsername, $testEmail, $testPassword]);
        
        $testUserId = $pdo->lastInsertId();
        echo "✅ Teste de inserção bem-sucedido (ID: $testUserId)<br>";
        
        // Remover usuário de teste
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$testUserId]);
        echo "✅ Usuário de teste removido<br>";
        
    } catch (Exception $e) {
        echo "❌ Erro no teste de inserção: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ Correção do banco de dados concluída!</h2>";
    echo "<p>Agora você pode testar o sistema de chat novamente.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erro durante a correção:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
