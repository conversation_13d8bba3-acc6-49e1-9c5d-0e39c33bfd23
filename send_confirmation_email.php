<?php
require_once 'admin/database/connection.php';
require_once 'vendor/autoload.php';
require_once 'includes/EmailManager.php';

header('Content-Type: application/json');

try {
    error_log("Iniciando processo de envio de email de confirmação");

    // Receber dados do POST
    $input = file_get_contents('php://input');
    error_log("Dados recebidos: " . $input);
    
    $data = json_decode($input, true);
    $paymentId = $data['payment_id'] ?? null;

    if (!$paymentId) {
        throw new Exception('ID do pagamento não fornecido');
    }

    error_log("Payment ID recebido: " . $paymentId);

    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Buscar informações do pagamento
    $stmt = $pdo->prepare("
        SELECT 
            o.*,
            c.name as customer_name,
            c.email as customer_email,
            p.name as product_name,
            p.price as product_price,
            p.id as product_id
        FROM orders o
        JOIN customers c ON o.customer_email = c.email
        JOIN products p ON o.product_id = p.id
        WHERE o.id = ?
    ");
    $stmt->execute([$paymentId]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$payment) {
        throw new Exception('Pedido não encontrado');
    }

    // Criar instância do EmailManager
    $emailManager = new EmailManager($pdo);

    // Enviar email de confirmação
    $emailManager->sendPaymentConfirmation(
        $payment['customer_email'],
        $payment['customer_name'],
        [
            'name' => $payment['product_name'],
            'price' => number_format($payment['price'], 2, ',', '.'),
            'access_url' => 'http://localhost/access.php?token=' . urlencode($payment['access_token'])
        ],
        [
            'id' => $payment['id'],
            'date' => date('d/m/Y H:i:s', strtotime($payment['created_at']))
        ]
    );

    // Atualizar status de envio do email
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET email_sent = 1, email_sent_at = CURRENT_TIMESTAMP 
        WHERE id = ?
    ");
    $stmt->execute([$paymentId]);

    echo json_encode(['success' => true, 'message' => 'Email de confirmação enviado com sucesso']);

} catch (Exception $e) {
    error_log("Erro ao enviar email de confirmação: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao enviar email de confirmação',
        'error' => $e->getMessage()
    ]);
}
