<?php
/**
 * Processamento de Pagamentos PIX - Sistema kesung-site
 * Versão Padronizada e Otimizada
 */

// Configurações de erro para desenvolvimento
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Headers para API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Incluir conexão padronizada
require_once 'database/connection.php';

// Incluir configurações PIX centralizadas
require_once 'config/pix_config.php';

// Função de log melhorada
function logMessage($message, $level = 'INFO') {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/payment_' . date('Y-m-d') . '.log';
    $timestamp = date('[Y-m-d H:i:s]');
    $logEntry = "$timestamp [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Função validateDocument está definida em pix_config.php

// Função para headers do Asaas
function getAsaasHeaders() {
    return getPixAsaasHeaders();
}

try {
    logMessage("Iniciando processamento de pagamento PIX");
    
    // Verificar método da requisição
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido. Use POST.');
    }
    
    // Obter dados do POST
    $postData = file_get_contents('php://input');
    if (empty($postData)) {
        throw new Exception('Dados não recebidos');
    }
    
    $data = json_decode($postData, true);
    if ($data === null) {
        throw new Exception('JSON inválido: ' . json_last_error_msg());
    }
    
    logMessage("Dados recebidos: " . json_encode($data, JSON_UNESCAPED_UNICODE));
    
    // Validar e extrair dados
    $productId = $data['product_id'] ?? null;
    $customerName = trim($data['customer_name'] ?? '');
    $customerEmail = trim($data['customer_email'] ?? '');
    $customerPhone = trim($data['customer_whatsapp'] ?? $data['customer_phone'] ?? '');
    $customerDocument = trim($data['customer_cpf_cnpj'] ?? $data['customer_document'] ?? '');
    
    // Validações
    if (empty($productId)) {
        throw new Exception('ID do produto é obrigatório');
    }
    
    if (empty($customerName)) {
        throw new Exception('Nome do cliente é obrigatório');
    }
    
    if (empty($customerEmail) || !filter_var($customerEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Email válido é obrigatório');
    }
    
    if (empty($customerPhone)) {
        throw new Exception('Telefone é obrigatório');
    }
    
    if (empty($customerDocument) || !validateDocument($customerDocument)) {
        throw new Exception('CPF/CNPJ válido é obrigatório');
    }
    
    // Limpar documento
    $customerDocument = preg_replace('/[^0-9]/', '', $customerDocument);
    
    // Conectar ao banco
    logMessage("Conectando ao banco de dados");
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar produto
    logMessage("Buscando produto ID: $productId");
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception('Produto não encontrado ou inativo');
    }
    
    logMessage("Produto encontrado: {$product['name']} - R$ {$product['price']}");
    
    // Verificar se cliente já existe
    $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
    $stmt->execute([$customerEmail]);
    $existingCustomer = $stmt->fetch();
    
    $customerId = null;
    if ($existingCustomer) {
        $customerId = $existingCustomer['id'];
        logMessage("Cliente existente encontrado: ID $customerId");
    } else {
        // Criar novo cliente
        $stmt = $pdo->prepare("
            INSERT INTO customers (name, email, whatsapp, document, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$customerName, $customerEmail, $customerPhone, $customerDocument]);
        $customerId = $pdo->lastInsertId();
        logMessage("Novo cliente criado: ID $customerId");
    }
    
    // Criar pedido
    logMessage("Criando pedido");
    $externalReference = generatePixReference($productId);
    $expiresAt = getPixExpirationDate();
    
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            customer_id, product_id, customer_name, customer_email,
            customer_phone, customer_document, amount, payment_method,
            payment_status, external_reference, expires_at, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pix', 'pending', ?, ?, NOW())
    ");

    $stmt->execute([
        $customerId, $productId, $customerName, $customerEmail,
        $customerPhone, $customerDocument, $product['price'],
        $externalReference, $expiresAt
    ]);
    
    $orderId = $pdo->lastInsertId();
    logMessage("Pedido criado: ID $orderId");
    
    // Criar cliente no Asaas
    logMessage("Criando cliente no Asaas");
    $customerData = [
        'name' => $customerName,
        'email' => $customerEmail,
        'phone' => preg_replace('/[^0-9]/', '', $customerPhone),
        'cpfCnpj' => $customerDocument,
        'notificationDisabled' => false
    ];
    
    $ch = curl_init(PIX_ASAAS_API_URL . '/customers');
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($customerData),
        CURLOPT_HTTPHEADER => getAsaasHeaders(),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $customerResponse = curl_exec($ch);
    $customerHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL ao criar cliente: ' . curl_error($ch));
    }
    curl_close($ch);
    
    $customerResponseData = json_decode($customerResponse, true);
    logMessage("Cliente Asaas - Status: $customerHttpCode");
    
    if ($customerHttpCode >= 400) {
        $errorMsg = $customerResponseData['errors'][0]['description'] ?? 'Erro desconhecido';
        throw new Exception("Erro ao criar cliente: $errorMsg");
    }
    
    $asaasCustomerId = $customerResponseData['id'];
    
    // Criar cobrança PIX
    logMessage("Criando cobrança PIX no Asaas");
    $paymentData = [
        'customer' => $asaasCustomerId,
        'billingType' => 'PIX',
        'value' => floatval($product['price']),
        'dueDate' => date('Y-m-d'),
        'description' => $product['name'],
        'externalReference' => $externalReference
    ];
    
    $ch = curl_init(PIX_ASAAS_API_URL . '/payments');
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($paymentData),
        CURLOPT_HTTPHEADER => getAsaasHeaders(),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $paymentResponse = curl_exec($ch);
    $paymentHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL ao criar cobrança: ' . curl_error($ch));
    }
    curl_close($ch);
    
    $paymentResponseData = json_decode($paymentResponse, true);
    logMessage("Cobrança PIX - Status: $paymentHttpCode");
    
    if ($paymentHttpCode >= 400) {
        $errorMsg = $paymentResponseData['errors'][0]['description'] ?? 'Erro desconhecido';
        throw new Exception("Erro ao criar cobrança: $errorMsg");
    }
    
    $asaasPaymentId = $paymentResponseData['id'];
    
    // Obter QR Code PIX
    logMessage("Obtendo QR Code PIX");
    $ch = curl_init(PIX_ASAAS_API_URL . "/payments/$asaasPaymentId/pixQrCode");
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => getAsaasHeaders(),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $qrResponse = curl_exec($ch);
    $qrHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL ao obter QR Code: ' . curl_error($ch));
    }
    curl_close($ch);
    
    $qrData = json_decode($qrResponse, true);
    logMessage("QR Code PIX - Status: $qrHttpCode, Response: " . $qrResponse);

    if ($qrHttpCode >= 400 || !isset($qrData['encodedImage'])) {
        $errorMsg = isset($qrData['errors']) ? $qrData['errors'][0]['description'] : 'Erro desconhecido ao gerar QR Code';
        throw new Exception("Erro ao gerar QR Code PIX: $errorMsg");
    }
    
    // Atualizar pedido com dados do pagamento
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET payment_id = ?, pix_qr_code = ?, pix_code = ? 
        WHERE id = ?
    ");
    $stmt->execute([
        $asaasPaymentId, 
        $qrData['encodedImage'], 
        $qrData['payload'], 
        $orderId
    ]);
    
    logMessage("Processamento concluído com sucesso - Order ID: $orderId, Payment ID: $asaasPaymentId");
    
    // Resposta de sucesso
    echo json_encode([
        'success' => true,
        'order_id' => $orderId,
        'payment_id' => $asaasPaymentId,
        'amount' => number_format($product['price'], 2, ',', '.'),
        'qr_code' => $qrData['encodedImage'],
        'pix_code' => $qrData['payload'],
        'expires_at' => $expiresAt
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    logMessage("ERRO: " . $e->getMessage(), 'ERROR');
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
