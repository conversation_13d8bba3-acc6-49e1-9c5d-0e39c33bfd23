<?php
/**
 * Verificação de Status de Pagamento PIX
 * Sistema kesung-site
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

require_once 'database/connection.php';

// Configurações Asaas - PRODUÇÃO
define('ASAAS_API_URL', 'https://www.asaas.com/api/v3');
define('ASAAS_API_KEY', '$aact_prod_000MzkwODA2MWY2OGM3MWRlMDU2NWM3MzJlNzZmNGZhZGY6OmZkZTI2NGIxLWM4ZmItNGI0NC04ZWUxLWI1ZjJjYTJiMmM0YTo6JGFhY2hfNjZmZjUxNGYtYWJkNC00NGQyLWJhOTctOTM5OTcyNzliODRi');

function logMessage($message) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/payment_check_' . date('Y-m-d') . '.log';
    $timestamp = date('[Y-m-d H:i:s]');
    file_put_contents($logFile, "$timestamp $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
}

function getAsaasHeaders() {
    return [
        'Content-Type: application/json',
        'access_token: ' . ASAAS_API_KEY
    ];
}

try {
    $paymentId = $_GET['payment_id'] ?? null;
    
    if (empty($paymentId)) {
        throw new Exception('Payment ID é obrigatório');
    }
    
    logMessage("Verificando status do pagamento: $paymentId");
    
    // Conectar ao banco
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar pedido no banco
    $stmt = $pdo->prepare("
        SELECT o.*, p.name as product_name 
        FROM orders o 
        LEFT JOIN products p ON o.product_id = p.id 
        WHERE o.payment_id = ?
    ");
    $stmt->execute([$paymentId]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Pedido não encontrado');
    }
    
    // Verificar se já está pago
    if ($order['payment_status'] === 'paid') {
        echo json_encode([
            'success' => true,
            'status' => 'paid',
            'order_id' => $order['id'],
            'message' => 'Pagamento já confirmado'
        ]);
        exit;
    }
    
    // Verificar se expirou
    if (strtotime($order['expires_at']) < time()) {
        // Atualizar status para expirado
        $stmt = $pdo->prepare("UPDATE orders SET payment_status = 'failed' WHERE id = ?");
        $stmt->execute([$order['id']]);
        
        echo json_encode([
            'success' => true,
            'status' => 'expired',
            'message' => 'Pagamento expirado'
        ]);
        exit;
    }
    
    // Consultar status no Asaas
    $ch = curl_init(ASAAS_API_URL . "/payments/$paymentId");
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => getAsaasHeaders(),
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        logMessage("Erro cURL: " . curl_error($ch));
        throw new Exception('Erro ao consultar status do pagamento');
    }
    curl_close($ch);
    
    if ($httpCode !== 200) {
        logMessage("Erro HTTP $httpCode: $response");
        throw new Exception('Erro ao consultar pagamento no Asaas');
    }
    
    $paymentData = json_decode($response, true);
    $asaasStatus = $paymentData['status'] ?? 'PENDING';
    
    logMessage("Status Asaas: $asaasStatus para pagamento $paymentId");
    
    // Mapear status do Asaas para nosso sistema
    $statusMap = [
        'PENDING' => 'pending',
        'RECEIVED' => 'paid',
        'CONFIRMED' => 'paid',
        'OVERDUE' => 'failed',
        'REFUNDED' => 'failed',
        'RECEIVED_IN_CASH' => 'paid'
    ];
    
    $newStatus = $statusMap[$asaasStatus] ?? 'pending';
    
    // Atualizar status no banco se mudou
    if ($newStatus !== $order['payment_status']) {
        $stmt = $pdo->prepare("
            UPDATE orders 
            SET payment_status = ?, paid_at = ? 
            WHERE id = ?
        ");
        
        $paidAt = ($newStatus === 'paid') ? date('Y-m-d H:i:s') : null;
        $stmt->execute([$newStatus, $paidAt, $order['id']]);
        
        logMessage("Status atualizado para $newStatus - Order ID: {$order['id']}");
        
        // Se foi pago, gerar token de download
        if ($newStatus === 'paid') {
            generateDownloadToken($pdo, $order['id'], $order['product_id']);
        }
    }
    
    echo json_encode([
        'success' => true,
        'status' => $newStatus,
        'order_id' => $order['id'],
        'asaas_status' => $asaasStatus,
        'message' => getStatusMessage($newStatus)
    ]);
    
} catch (Exception $e) {
    logMessage("ERRO: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function generateDownloadToken($pdo, $orderId, $productId) {
    try {
        // Verificar se já existe token
        $stmt = $pdo->prepare("SELECT id FROM download_tokens WHERE order_id = ?");
        $stmt->execute([$orderId]);
        
        if ($stmt->fetch()) {
            return; // Token já existe
        }
        
        // Gerar novo token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+7 days'));
        
        $stmt = $pdo->prepare("
            INSERT INTO download_tokens (
                order_id, product_id, token, expires_at, created_at
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$orderId, $productId, $token, $expiresAt]);
        
        logMessage("Token de download gerado: $token para order $orderId");
        
        // Aqui você pode enviar email com o link de download
        // sendDownloadEmail($orderId, $token);
        
    } catch (Exception $e) {
        logMessage("Erro ao gerar token de download: " . $e->getMessage());
    }
}

function getStatusMessage($status) {
    $messages = [
        'pending' => 'Aguardando pagamento',
        'processing' => 'Processando pagamento',
        'paid' => 'Pagamento confirmado! Acesso liberado.',
        'failed' => 'Pagamento falhou ou expirou',
        'cancelled' => 'Pagamento cancelado'
    ];
    
    return $messages[$status] ?? 'Status desconhecido';
}
?>
