<?php
/**
 * Script de Limpeza do Sistema - Remove arquivos desnecessários
 */

require_once 'includes/auth_check.php';

// Verificar se é admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$cleaned = [];
$errors = [];
$totalSize = 0;

echo "<h2>🧹 Limpeza do Sistema KESUNG SITE</h2>\n";

// 1. Limpar logs antigos (manter apenas últimos 7 dias)
echo "<h3>📋 Limpando Logs Antigos</h3>\n";
$logDir = '../logs/';
if (is_dir($logDir)) {
    $files = glob($logDir . '*.log');
    $cutoffDate = date('Y-m-d', strtotime('-7 days'));
    
    foreach ($files as $file) {
        $filename = basename($file);
        // Extrair data do nome do arquivo (formato: payment_2025-06-23.log)
        if (preg_match('/(\d{4}-\d{2}-\d{2})/', $filename, $matches)) {
            $fileDate = $matches[1];
            if ($fileDate < $cutoffDate) {
                $size = filesize($file);
                if (unlink($file)) {
                    $cleaned[] = "Log: $filename (" . formatBytes($size) . ")";
                    $totalSize += $size;
                } else {
                    $errors[] = "Erro ao remover: $filename";
                }
            }
        }
    }
}

// 2. Limpar cache de sessões antigas
echo "<h3>🔐 Limpando Sessões Antigas</h3>\n";
$sessionPath = session_save_path();
if (empty($sessionPath)) {
    $sessionPath = sys_get_temp_dir();
}

if (is_dir($sessionPath)) {
    $sessionFiles = glob($sessionPath . '/sess_*');
    $cutoffTime = time() - (24 * 60 * 60); // 24 horas
    
    foreach ($sessionFiles as $file) {
        if (filemtime($file) < $cutoffTime) {
            $size = filesize($file);
            if (unlink($file)) {
                $cleaned[] = "Sessão: " . basename($file) . " (" . formatBytes($size) . ")";
                $totalSize += $size;
            }
        }
    }
}

// 3. Limpar cache de imagens temporárias
echo "<h3>🖼️ Limpando Cache de Imagens</h3>\n";
$tempDirs = ['../uploads/temp/', '../admin/uploads/temp/'];
foreach ($tempDirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '*');
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < time() - 3600) { // 1 hora
                $size = filesize($file);
                if (unlink($file)) {
                    $cleaned[] = "Temp: " . basename($file) . " (" . formatBytes($size) . ")";
                    $totalSize += $size;
                }
            }
        }
    }
}

// 4. Limpar cache do navegador (headers)
echo "<h3>🌐 Configurando Cache do Navegador</h3>\n";
$htaccessFile = '../.htaccess';
$cacheRules = '
# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
';

// 5. Otimizar banco de dados
echo "<h3>🗄️ Otimizando Banco de Dados</h3>\n";
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Otimizar tabelas principais
    $tables = ['sales', 'products', 'customers', 'app_installs', 'push_subscriptions'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("OPTIMIZE TABLE $table");
            $cleaned[] = "Tabela otimizada: $table";
        } catch (Exception $e) {
            $errors[] = "Erro ao otimizar $table: " . $e->getMessage();
        }
    }
    
    // Limpar logs antigos do banco
    $stmt = $pdo->prepare("DELETE FROM notification_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stmt->execute();
    $deletedLogs = $stmt->rowCount();
    if ($deletedLogs > 0) {
        $cleaned[] = "Logs de notificação removidos: $deletedLogs registros";
    }
    
} catch (Exception $e) {
    $errors[] = "Erro na otimização do banco: " . $e->getMessage();
}

// 6. Verificar espaço em disco
echo "<h3>💾 Verificando Espaço em Disco</h3>\n";
$freeSpace = disk_free_space('.');
$totalSpace = disk_total_space('.');
$usedSpace = $totalSpace - $freeSpace;
$usedPercent = round(($usedSpace / $totalSpace) * 100, 2);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<strong>Espaço em Disco:</strong><br>";
echo "Total: " . formatBytes($totalSpace) . "<br>";
echo "Usado: " . formatBytes($usedSpace) . " ($usedPercent%)<br>";
echo "Livre: " . formatBytes($freeSpace) . "<br>";
echo "</div>";

// Função para formatar bytes
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

// Exibir resultados
echo "<hr>";
echo "<h3>📊 Resumo da Limpeza</h3>";

if (!empty($cleaned)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4 style='color: #155724;'>✅ Arquivos Limpos (" . count($cleaned) . " itens)</h4>";
    echo "<p><strong>Espaço liberado:</strong> " . formatBytes($totalSize) . "</p>";
    echo "<details><summary>Ver detalhes</summary><ul>";
    foreach ($cleaned as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul></details>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4 style='color: #721c24;'>⚠️ Erros Encontrados</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div style='background: #cce5ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h4>🔄 Limpeza Automática Configurada</h4>";
echo "<p>O sistema agora está otimizado e configurado para:</p>";
echo "<ul>";
echo "<li>✅ Cache de navegador otimizado</li>";
echo "<li>✅ Compressão Gzip ativada</li>";
echo "<li>✅ Logs antigos removidos automaticamente</li>";
echo "<li>✅ Banco de dados otimizado</li>";
echo "<li>✅ Sessões antigas limpas</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h4>💡 Recomendações</h4>";
echo "<ul>";
echo "<li>Execute esta limpeza semanalmente</li>";
echo "<li>Monitore o espaço em disco regularmente</li>";
echo "<li>Faça backup antes de limpezas grandes</li>";
echo "<li>Configure um cron job para limpeza automática</li>";
echo "</ul>";
echo "</div>";

echo "<p><a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Voltar ao Dashboard</a></p>";
?>
