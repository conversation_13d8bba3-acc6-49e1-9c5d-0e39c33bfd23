<?php
/**
 * Sistema de Download Padronizado - kesung-site
 * Controla downloads através de tokens seguros
 */

require_once 'database/connection.php';

// Função de log
function logDownload($message) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/downloads_' . date('Y-m-d') . '.log';
    $timestamp = date('[Y-m-d H:i:s]');
    file_put_contents($logFile, "$timestamp $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
}

// Função para mostrar erro responsivo
function showError($message, $code = 404) {
    http_response_code($code);
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Erro - Download</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                min-height: 100vh; 
            }
            .error-card { 
                box-shadow: 0 10px 30px rgba(0,0,0,0.3); 
                border-radius: 15px;
                overflow: hidden;
            }
            @media (max-width: 768px) {
                .container { padding: 1rem; }
            }
        </style>
    </head>
    <body class="d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card error-card border-0">
                        <div class="card-header bg-danger text-white text-center py-3">
                            <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Erro no Download</h4>
                        </div>
                        <div class="card-body text-center p-4">
                            <p class="card-text mb-4"><?= htmlspecialchars($message) ?></p>
                            <a href="/" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>Voltar ao Início
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

try {
    // Obter token da URL
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        logDownload("Tentativa de acesso sem token - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        showError('Token de download não fornecido.');
    }
    
    logDownload("Tentativa de download com token: $token - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    
    // Conectar ao banco padronizado
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar token válido com nova estrutura
    $stmt = $pdo->prepare("
        SELECT dt.*, o.customer_name, o.customer_email, p.name as product_name, p.file_path
        FROM download_tokens dt
        LEFT JOIN orders o ON dt.order_id = o.id
        LEFT JOIN products p ON dt.product_id = p.id
        WHERE dt.token = ? AND dt.expires_at > NOW()
    ");
    $stmt->execute([$token]);
    $downloadData = $stmt->fetch();
    
    if (!$downloadData) {
        logDownload("Token inválido ou expirado: $token");
        showError('Token de download inválido ou expirado.');
    }
    
    // Verificar limite de downloads
    if ($downloadData['downloads_count'] >= $downloadData['max_downloads']) {
        logDownload("Limite de downloads excedido para token: $token");
        showError('Limite de downloads excedido para este produto.');
    }
    
    // Verificar se arquivo existe
    $filePath = $downloadData['file_path'];
    if (empty($filePath) || !file_exists($filePath)) {
        logDownload("Arquivo não encontrado: $filePath para token: $token");
        showError('Arquivo não encontrado no servidor.');
    }
    
    // Atualizar contador de downloads
    $stmt = $pdo->prepare("
        UPDATE download_tokens 
        SET downloads_count = downloads_count + 1, 
            used_at = NOW(),
            ip_address = ?,
            user_agent = ?
        WHERE id = ?
    ");
    $stmt->execute([
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        $downloadData['id']
    ]);
    
    logDownload("Download iniciado: {$downloadData['product_name']} por {$downloadData['customer_email']} - Token: $token");
    
    // Preparar download seguro
    $fileName = basename($filePath);
    $fileSize = filesize($filePath);
    $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';
    
    // Headers para download seguro
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    header('X-Robots-Tag: noindex, nofollow');
    
    // Limpar buffer de saída
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Enviar arquivo
    readfile($filePath);
    
    logDownload("Download concluído: {$downloadData['product_name']} - Token: $token");
    
} catch (Exception $e) {
    logDownload("ERRO no download: " . $e->getMessage());
    showError('Erro interno do servidor. Tente novamente mais tarde.');
}
?>
