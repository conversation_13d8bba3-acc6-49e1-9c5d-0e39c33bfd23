<?php
session_start();
require_once 'database/connection.php';

// Simular login de admin para teste
if (!isset($_SESSION['admin_id'])) {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->query("SELECT id FROM admins LIMIT 1");
    $admin = $stmt->fetch();
    
    if ($admin) {
        $_SESSION['admin_id'] = $admin['id'];
        echo "<p style='color: green;'>Admin logado automaticamente para teste (ID: {$admin['id']})</p>";
    } else {
        echo "<p style='color: red;'>Nenhum admin encontrado!</p>";
        exit;
    }
}

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>Debug do Chat Admin</h2>";
    
    // Verificar sessão
    echo "<h3>1. Verificando Sessão:</h3>";
    echo "<p>Admin ID: " . ($_SESSION['admin_id'] ?? 'Não definido') . "</p>";
    echo "<p>Sessão ativa: " . (session_status() === PHP_SESSION_ACTIVE ? 'Sim' : 'Não') . "</p>";
    
    // Verificar usuários disponíveis
    echo "<h3>2. Usuários Disponíveis:</h3>";
    $stmt = $pdo->query("
        SELECT id, name, email, whatsapp, last_activity, is_admin
        FROM chat_users 
        WHERE is_admin = 0 
        ORDER BY last_activity DESC
    ");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: red;'>Nenhum usuário encontrado!</p>";
        echo "<p><a href='create_test_users.php'>Criar usuários de teste</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>WhatsApp</th><th>Última Atividade</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['whatsapp']}</td>";
            echo "<td>{$user['last_activity']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Teste de envio de mensagem
    if (!empty($users)) {
        $testUserId = $users[0]['id'];
        echo "<h3>3. Teste de Envio de Mensagem:</h3>";
        echo "<p>Testando envio para usuário: {$users[0]['name']} (ID: $testUserId)</p>";
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_message'])) {
            $message = trim($_POST['test_message']);
            $adminId = $_SESSION['admin_id'];
            
            echo "<p>Tentando enviar mensagem: '$message'</p>";
            echo "<p>Admin ID: $adminId</p>";
            echo "<p>User ID: $testUserId</p>";
            
            try {
                // Inserir mensagem
                $stmt = $pdo->prepare("
                    INSERT INTO chat_messages (user_id, message, is_admin, timestamp) 
                    VALUES (?, ?, 1, UNIX_TIMESTAMP())
                ");
                $result = $stmt->execute([$testUserId, $message]);
                
                if ($result) {
                    $messageId = $pdo->lastInsertId();
                    echo "<p style='color: green;'>✓ Mensagem enviada com sucesso! ID: $messageId</p>";
                    
                    // Atualizar última atividade
                    $stmt = $pdo->prepare("UPDATE chat_users SET last_activity = NOW() WHERE id = ?");
                    $stmt->execute([$testUserId]);
                    
                } else {
                    echo "<p style='color: red;'>✗ Erro ao inserir mensagem</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Erro: " . $e->getMessage() . "</p>";
            }
        }
        
        // Formulário de teste
        echo "<form method='POST'>";
        echo "<input type='text' name='test_message' placeholder='Digite uma mensagem de teste' required>";
        echo "<button type='submit'>Enviar Teste</button>";
        echo "</form>";
    }
    
    // Verificar mensagens existentes
    echo "<h3>4. Mensagens Existentes:</h3>";
    $stmt = $pdo->query("
        SELECT m.*, u.name as user_name 
        FROM chat_messages m 
        LEFT JOIN chat_users u ON m.user_id = u.id 
        ORDER BY m.timestamp DESC 
        LIMIT 10
    ");
    $messages = $stmt->fetchAll();
    
    if (empty($messages)) {
        echo "<p>Nenhuma mensagem encontrada</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Usuário</th><th>Mensagem</th><th>Admin?</th><th>Timestamp</th></tr>";
        foreach ($messages as $msg) {
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['user_name']}</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>" . ($msg['is_admin'] ? 'Sim' : 'Não') . "</td>";
            echo "<td>" . date('d/m/Y H:i:s', $msg['timestamp']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Teste AJAX
    echo "<h3>5. Teste AJAX:</h3>";
    echo "<div id='ajax-test'>";
    echo "<button onclick='testAjaxSend()'>Testar Envio AJAX</button>";
    echo "<div id='ajax-result'></div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}
?>

<script>
function testAjaxSend() {
    const testUserId = <?php echo !empty($users) ? $users[0]['id'] : 0; ?>;
    const testMessage = 'Teste AJAX - ' + new Date().toLocaleTimeString();
    
    console.log('Testando AJAX:', {testUserId, testMessage});
    
    const formData = new FormData();
    formData.append('action', 'send_message');
    formData.append('user_id', testUserId);
    formData.append('message', testMessage);
    
    fetch('admin/chat.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(data => {
        console.log('Response data:', data);
        document.getElementById('ajax-result').innerHTML = '<pre>' + data + '</pre>';
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('ajax-result').innerHTML = '<p style="color: red;">Erro: ' + error.message + '</p>';
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
button { padding: 10px 15px; margin: 5px; }
input[type="text"] { padding: 8px; width: 300px; margin-right: 10px; }
</style>
