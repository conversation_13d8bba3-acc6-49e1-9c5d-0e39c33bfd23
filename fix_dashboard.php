<?php
require_once 'database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>Corrigindo Dashboard Admin...</h2>";
    
    // 1. Verificar tabelas necessárias
    echo "<h3>1. Verificando tabelas necessárias...</h3>";
    
    $tables = ['orders', 'products', 'chat_users', 'chat_messages', 'chat_sessions'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✓ Tabela $table existe</p>";
        } else {
            echo "<p style='color: red;'>✗ Tabela $table não encontrada</p>";
            
            // Criar tabelas básicas se não existirem
            switch ($table) {
                case 'orders':
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS orders (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            customer_name VARCHAR(255) NOT NULL,
                            customer_email VARCHAR(255) NOT NULL,
                            product_id INT,
                            amount DECIMAL(10,2) NOT NULL,
                            payment_status ENUM('pending', 'paid', 'approved', 'rejected') DEFAULT 'pending',
                            payment_method VARCHAR(50),
                            external_reference VARCHAR(255),
                            payment_id VARCHAR(255),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ");
                    echo "<p>✓ Tabela orders criada</p>";
                    break;
                    
                case 'products':
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS products (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            description TEXT,
                            price DECIMAL(10,2) NOT NULL,
                            image VARCHAR(255),
                            status ENUM('active', 'inactive') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ");
                    echo "<p>✓ Tabela products criada</p>";
                    break;
            }
        }
    }
    
    // 2. Inserir dados de exemplo se as tabelas estiverem vazias
    echo "<h3>2. Verificando dados de exemplo...</h3>";
    
    // Produtos de exemplo
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $productCount = $stmt->fetch()['count'];
    
    if ($productCount == 0) {
        $sampleProducts = [
            ['Script PHP - Módulo Completo Monte Seu Próprio Site', 'Leve este script completo para criar seu próprio site, desde a instalação até o gerenciamento de conteúdo.', 197.77],
            ['Script PHP - Módulo China para Casa de Apostas', 'Leve este script completo para casas de apostas com módulo China integrado.', 197.77],
            ['Curso Completo - Monte Seu Próprio Site', 'Aprenda a criar seu próprio site do zero com este curso completo.', 97.77],
            ['Script PHP - Casa Chinese - Integração com Pix', 'Script completo para casa de apostas com integração Pix.', 350.00],
            ['Script PHP - Módulo China para Casa de Apostas Com Painel Administrativo', 'Sistema completo com painel administrativo para gerenciamento.', 271.00]
        ];
        
        foreach ($sampleProducts as $product) {
            $stmt = $pdo->prepare("INSERT INTO products (name, description, price) VALUES (?, ?, ?)");
            $stmt->execute($product);
        }
        echo "<p>✓ Produtos de exemplo inseridos</p>";
    } else {
        echo "<p>✓ Produtos já existem ($productCount produtos)</p>";
    }
    
    // Pedidos de exemplo
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $orderCount = $stmt->fetch()['count'];
    
    if ($orderCount == 0) {
        $sampleOrders = [
            ['João Silva', '<EMAIL>', 1, 197.77, 'approved', 'pix', 'REF001', 'PAY001'],
            ['Maria Santos', '<EMAIL>', 2, 197.77, 'pending', 'pix', 'REF002', 'PAY002'],
            ['Pedro Costa', '<EMAIL>', 3, 97.77, 'approved', 'pix', 'REF003', 'PAY003']
        ];
        
        foreach ($sampleOrders as $order) {
            $stmt = $pdo->prepare("
                INSERT INTO orders (customer_name, customer_email, product_id, amount, payment_status, payment_method, external_reference, payment_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($order);
        }
        echo "<p>✓ Pedidos de exemplo inseridos</p>";
    } else {
        echo "<p>✓ Pedidos já existem ($orderCount pedidos)</p>";
    }
    
    // 3. Verificar estatísticas
    echo "<h3>3. Estatísticas atuais:</h3>";
    
    // Vendas
    $stmt = $pdo->query("SELECT COUNT(*) as count, SUM(CASE WHEN payment_status = 'approved' THEN amount ELSE 0 END) as total FROM orders");
    $sales = $stmt->fetch();
    echo "<p>Total de vendas: {$sales['count']}</p>";
    echo "<p>Receita total: R$ " . number_format($sales['total'], 2, ',', '.') . "</p>";
    
    // Produtos
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $products = $stmt->fetch();
    echo "<p>Total de produtos: {$products['count']}</p>";
    
    // Clientes únicos
    $stmt = $pdo->query("SELECT COUNT(DISTINCT customer_email) as count FROM orders");
    $customers = $stmt->fetch();
    echo "<p>Total de clientes: {$customers['count']}</p>";
    
    // Chat
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_users WHERE is_admin = 0");
    $chatUsers = $stmt->fetch();
    echo "<p>Usuários de chat: {$chatUsers['count']}</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_messages");
    $chatMessages = $stmt->fetch();
    echo "<p>Mensagens de chat: {$chatMessages['count']}</p>";
    
    // 4. Verificar arquivos do dashboard
    echo "<h3>4. Verificando arquivos do dashboard...</h3>";
    
    $dashboardFiles = [
        'admin/dashboard.php',
        'admin/includes/sidebar.php',
        'admin/includes/navbar.php',
        'admin/css/style.css',
        'admin/css/standardized.css'
    ];
    
    foreach ($dashboardFiles as $file) {
        if (file_exists($file)) {
            echo "<p>✓ $file existe</p>";
        } else {
            echo "<p style='color: red;'>✗ $file não encontrado</p>";
        }
    }
    
    echo "<h3 style='color: green;'>✓ Dashboard corrigido com sucesso!</h3>";
    echo "<p><a href='admin/dashboard.php' class='btn btn-primary'>Acessar Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
    margin: 5px;
}
.btn:hover { background: #0056b3; }
</style>
