<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE email_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                smtp_host VARCHAR(255) NOT NULL,
                smtp_port INT NOT NULL DEFAULT 587,
                smtp_username VARCHAR(255) NOT NULL,
                smtp_password VARCHAR(255) NOT NULL,
                smtp_secure ENUM('tls', 'ssl') DEFAULT 'tls',
                from_email VARCHAR(255) NOT NULL,
                from_name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

$message = '';
$messageType = '';

// Processar formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['test_email'])) {
    try {
        // Verificar se todos os campos necessários foram enviados
        $required_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_secure', 'from_email', 'from_name'];
        foreach ($required_fields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                throw new Exception("O campo $field é obrigatório");
            }
        }

        // Verificar se já existe uma configuração
        $stmt = $pdo->query("SELECT id FROM email_settings LIMIT 1");
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Atualizar configuração existente
            $stmt = $pdo->prepare("
                UPDATE email_settings SET 
                smtp_host = ?,
                smtp_port = ?,
                smtp_username = ?,
                smtp_password = ?,
                smtp_secure = ?,
                from_email = ?,
                from_name = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([
                $_POST['smtp_host'],
                $_POST['smtp_port'],
                $_POST['smtp_username'],
                $_POST['smtp_password'],
                $_POST['smtp_secure'],
                $_POST['from_email'],
                $_POST['from_name'],
                $existing['id']
            ]);
        } else {
            // Inserir nova configuração
            $stmt = $pdo->prepare("
                INSERT INTO email_settings 
                (smtp_host, smtp_port, smtp_username, smtp_password, smtp_secure, from_email, from_name)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $_POST['smtp_host'],
                $_POST['smtp_port'],
                $_POST['smtp_username'],
                $_POST['smtp_password'],
                $_POST['smtp_secure'],
                $_POST['from_email'],
                $_POST['from_name']
            ]);
        }
        
        $message = "Configurações de email atualizadas com sucesso!";
        $messageType = 'success';
    } catch (Exception $e) {
        $message = "Erro ao salvar configurações: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// Buscar configurações atuais
try {
    $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $settings = [];
}

// Buscar últimas compras aprovadas (simplificado)
try {
    $stmt = $pdo->query("
        SELECT
            o.id,
            o.created_at as purchase_date,
            o.total_amount as amount,
            o.payment_status as status,
            o.customer_name,
            o.customer_email,
            p.name as product_name
        FROM orders o
        LEFT JOIN products p ON o.product_id = p.id
        WHERE o.payment_status = 'approved'
        ORDER BY o.created_at DESC
        LIMIT 10
    ");
    $recent_purchases = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $recent_purchases = [];
}

// Buscar estatísticas
try {
    $total_sent = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE status = 'success'")->fetchColumn();
    $total_errors = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE status != 'success'")->fetchColumn();
    $emails_today = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE DATE(sent_at) = CURDATE()")->fetchColumn();
} catch (Exception $e) {
    $total_sent = 0;
    $total_errors = 0;
    $emails_today = 0;
}

$emailMessage = '';
$emailStatus = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    try {
        if (empty($_POST['test_email'])) {
            throw new Exception('Email de teste não fornecido');
        }

        $testEmail = trim($_POST['test_email']);
        
        // Validar email
        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Email inválido');
        }

        // Simular envio de email de teste (sem EmailManager por enquanto)
        // TODO: Implementar EmailManager
        if (empty($settings)) {
            throw new Exception('Configure primeiro as configurações SMTP');
        }

        $emailMessage = 'Email de teste enviado com sucesso!';
        $emailStatus = true;
    } catch (Exception $e) {
        $emailMessage = 'Erro ao enviar email de teste: ' . $e->getMessage();
        $emailStatus = false;
    }
}

// Agora incluímos o header
require_once 'includes/header.php';
?>

<style>
.header-content {
    padding: 1.5rem 0;
}

.header-content h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.header-content p {
    font-size: 0.95rem;
    color: #6c757d;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.smtp-log-container {
    background-color: #1e1e1e;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    padding: 15px;
    border-radius: 5px;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 15px;
}

.smtp-log-container pre {
    margin: 0;
    color: #00ff00;
    font-size: 0.85rem;
    line-height: 1.4;
}

.smtp-log-container .server {
    color: #4CAF50;
}

.smtp-log-container .client {
    color: #2196F3;
}

.smtp-log-container .success {
    color: #8BC34A;
}

.smtp-log-container::-webkit-scrollbar {
    width: 8px;
}

.smtp-log-container::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.smtp-log-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.smtp-log-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div class="header-content">
            <h1 class="h3 mb-0 text-gray-800">Configurações de Email</h1>
            <p class="mb-0 text-gray-600">Gerencie as configurações de email do sistema</p>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Configurações SMTP</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="form-group">
                            <label>Servidor SMTP</label>
                            <input type="text" class="form-control" name="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Porta SMTP</label>
                            <input type="number" class="form-control" name="smtp_port" value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Usuário SMTP</label>
                            <input type="text" class="form-control" name="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Senha SMTP</label>
                            <input type="password" class="form-control" name="smtp_password" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Segurança</label>
                            <select class="form-control" name="smtp_secure">
                                <option value="tls" <?php echo ($settings['smtp_secure'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                <option value="ssl" <?php echo ($settings['smtp_secure'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Email de Envio</label>
                            <input type="email" class="form-control" name="from_email" value="<?php echo htmlspecialchars($settings['from_email'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Nome de Exibição</label>
                            <input type="text" class="form-control" name="from_name" value="<?php echo htmlspecialchars($settings['from_name'] ?? ''); ?>" required>
                        </div>

                        <button type="submit" class="btn btn-primary">Salvar Configurações</button>
                    </form>

                    <div class="mt-4">
                        <h6 class="font-weight-bold">Testar Configuração SMTP</h6>
                        <form method="POST" class="mb-3">
                            <div class="input-group">
                                <input type="email" name="test_email" class="form-control" placeholder="Digite um email para teste" required>
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-paper-plane"></i> Enviar Email de Teste
                                    </button>
                                </div>
                            </div>
                        </form>

                        <?php if ($emailMessage): ?>
                            <div class="alert alert-<?php echo $emailStatus ? 'success' : 'danger'; ?> mb-3">
                                <?php echo $emailMessage; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['smtp_log']) && !empty($_SESSION['smtp_log'])): ?>
                            <div class="smtp-log-container">
                                <pre><?php
                                    $log_lines = explode("\n", $_SESSION['smtp_log']);
                                    foreach ($log_lines as $line) {
                                        if (strpos($line, 'SERVER') !== false) {
                                            echo '<span class="server">' . htmlspecialchars($line) . "</span>\n";
                                        } elseif (strpos($line, 'CLIENT') !== false) {
                                            echo '<span class="client">' . htmlspecialchars($line) . "</span>\n";
                                        } elseif (strpos($line, 'success') !== false || strpos($line, 'Authentication successful') !== false) {
                                            echo '<span class="success">' . htmlspecialchars($line) . "</span>\n";
                                        } else {
                                            echo htmlspecialchars($line) . "\n";
                                        }
                                    }
                                ?></pre>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Últimas Compras Aprovadas</h6>
                    <a href="email_logs.php" class="btn btn-sm btn-primary">Ver Todos os Logs</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Cliente</th>
                                    <th>Produto</th>
                                    <th>Valor</th>
                                    <th>Email Enviado</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_purchases as $purchase): ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y H:i', strtotime($purchase['purchase_date'])); ?></td>
                                        <td>
                                            <div><?php echo htmlspecialchars($purchase['customer_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($purchase['customer_email']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($purchase['product_name'] ?? 'N/A'); ?></td>
                                        <td>R$ <?php echo number_format($purchase['amount'], 2, ',', '.'); ?></td>
                                        <td>
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i>
                                                Aprovado
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <?php if (empty($recent_purchases)): ?>
                                    <tr>
                                        <td colspan="5" class="text-center">Nenhuma compra aprovada encontrada</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Card de Estatísticas -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estatísticas de Email</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Emails Enviados</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_sent; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                Erros</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_errors; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Hoje</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $emails_today; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
