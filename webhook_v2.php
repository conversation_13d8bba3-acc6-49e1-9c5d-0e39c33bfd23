<?php
/**
 * Webhook Handler - Sistema kesung-site
 * Processamento de notificações de pagamento do Asaas
 * Versão Padronizada e Segura
 */

// Configurações de erro (desabilitar em produção)
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Headers de segurança
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// Incluir conexão padronizada
require_once 'database/connection.php';

// Configurações
define('WEBHOOK_TOKEN_FILE', __DIR__ . '/config/webhook_token.txt');
define('LOG_DIR', __DIR__ . '/logs');
define('MAX_LOG_SIZE', 10 * 1024 * 1024); // 10MB

// Função de log melhorada
function logMessage($message, $level = 'INFO') {
    if (!is_dir(LOG_DIR)) {
        mkdir(LOG_DIR, 0755, true);
    }
    
    $logFile = LOG_DIR . '/webhook_' . date('Y-m-d') . '.log';
    
    // Rotacionar log se muito grande
    if (file_exists($logFile) && filesize($logFile) > MAX_LOG_SIZE) {
        rename($logFile, $logFile . '.old');
    }
    
    $timestamp = date('[Y-m-d H:i:s]');
    $logEntry = "$timestamp [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Função para resposta JSON
function sendResponse($success, $message, $httpCode = 200, $data = null) {
    http_response_code($httpCode);
    
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('c')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// Função para mapear status do Asaas
function mapAsaasStatus($asaasStatus) {
    $statusMap = [
        'PENDING' => 'pending',
        'RECEIVED' => 'paid',
        'CONFIRMED' => 'paid',
        'OVERDUE' => 'failed',
        'REFUNDED' => 'failed',
        'RECEIVED_IN_CASH' => 'paid',
        'AWAITING_RISK_ANALYSIS' => 'processing'
    ];
    
    return $statusMap[$asaasStatus] ?? 'unknown';
}

// Função para validar token do webhook
function validateWebhookToken($receivedToken) {
    if (!file_exists(WEBHOOK_TOKEN_FILE)) {
        logMessage("Arquivo de token não encontrado", 'WARNING');
        return false;
    }
    
    $validToken = trim(file_get_contents(WEBHOOK_TOKEN_FILE));
    return hash_equals($validToken, $receivedToken);
}

// Função para gerar token de download
function generateDownloadToken($pdo, $orderId, $productId) {
    try {
        // Verificar se já existe token
        $stmt = $pdo->prepare("SELECT id FROM download_tokens WHERE order_id = ?");
        $stmt->execute([$orderId]);
        
        if ($stmt->fetch()) {
            logMessage("Token de download já existe para order $orderId");
            return;
        }
        
        // Gerar novo token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $stmt = $pdo->prepare("
            INSERT INTO download_tokens (
                order_id, product_id, token, expires_at, created_at
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$orderId, $productId, $token, $expiresAt]);
        
        logMessage("Token de download gerado: $token para order $orderId");
        
        // Enviar email com link de download
        sendDownloadEmail($pdo, $orderId, $token);
        
    } catch (Exception $e) {
        logMessage("Erro ao gerar token de download: " . $e->getMessage(), 'ERROR');
    }
}

// Função para enviar email de download
function sendDownloadEmail($pdo, $orderId, $token) {
    try {
        // Buscar dados do pedido
        $stmt = $pdo->prepare("
            SELECT o.*, p.name as product_name 
            FROM orders o 
            LEFT JOIN products p ON o.product_id = p.id 
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();
        
        if (!$order) {
            throw new Exception("Pedido não encontrado: $orderId");
        }
        
        $downloadLink = "https://" . $_SERVER['HTTP_HOST'] . "/download.php?token=$token";
        
        $subject = "Seu produto está pronto para download - " . $order['product_name'];
        $message = "
        <html>
        <head>
            <title>Download Liberado</title>
        </head>
        <body>
            <h2>Pagamento Confirmado!</h2>
            <p>Olá {$order['customer_name']},</p>
            <p>Seu pagamento foi confirmado e o download do produto <strong>{$order['product_name']}</strong> foi liberado.</p>
            <p><a href='$downloadLink' style='background: #4318FF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Baixar Produto</a></p>
            <p>Link direto: $downloadLink</p>
            <p>Este link é válido por 30 dias.</p>
            <hr>
            <p><small>Kesung Site - Sistema de Vendas Digitais</small></p>
        </body>
        </html>
        ";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: <EMAIL>',
            'Reply-To: <EMAIL>'
        ];
        
        if (mail($order['customer_email'], $subject, $message, implode("\r\n", $headers))) {
            logMessage("Email de download enviado para {$order['customer_email']}");
        } else {
            logMessage("Falha ao enviar email para {$order['customer_email']}", 'ERROR');
        }
        
    } catch (Exception $e) {
        logMessage("Erro ao enviar email de download: " . $e->getMessage(), 'ERROR');
    }
}

try {
    logMessage("Webhook recebido - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    
    // Verificar método HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, 'Método não permitido', 405);
    }
    
    // Obter headers
    $headers = getallheaders() ?: [];
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    $token = str_replace('Bearer ', '', $authHeader);
    
    logMessage("Headers recebidos: " . json_encode($headers, JSON_UNESCAPED_UNICODE));
    
    // Validar token (se configurado)
    if (file_exists(WEBHOOK_TOKEN_FILE) && !empty($token)) {
        if (!validateWebhookToken($token)) {
            logMessage("Token inválido recebido: $token", 'WARNING');
            sendResponse(false, 'Token inválido', 401);
        }
    }
    
    // Obter dados do webhook
    $rawData = file_get_contents('php://input');
    
    if (empty($rawData)) {
        logMessage("Dados vazios recebidos", 'WARNING');
        sendResponse(false, 'Dados não recebidos', 400);
    }
    
    logMessage("Dados brutos recebidos: " . substr($rawData, 0, 500) . (strlen($rawData) > 500 ? '...' : ''));
    
    // Decodificar JSON
    $data = json_decode($rawData, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("JSON inválido: " . json_last_error_msg(), 'ERROR');
        sendResponse(false, 'JSON inválido', 400);
    }
    
    // Validar estrutura do webhook
    if (!isset($data['event']) || !isset($data['payment'])) {
        logMessage("Estrutura de webhook inválida: " . json_encode($data, JSON_UNESCAPED_UNICODE), 'WARNING');
        sendResponse(false, 'Estrutura inválida', 400);
    }
    
    $event = $data['event'];
    $payment = $data['payment'];
    $paymentId = $payment['id'] ?? null;
    $asaasStatus = $payment['status'] ?? null;
    $externalReference = $payment['externalReference'] ?? null;
    
    logMessage("Evento: $event, Payment ID: $paymentId, Status: $asaasStatus, Ref: $externalReference");
    
    // Conectar ao banco
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar pedido
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE payment_id = ? OR external_reference = ?");
    $stmt->execute([$paymentId, $externalReference]);
    $order = $stmt->fetch();
    
    if (!$order) {
        logMessage("Pedido não encontrado para payment_id: $paymentId ou external_reference: $externalReference", 'WARNING');
        sendResponse(false, 'Pedido não encontrado', 404);
    }
    
    logMessage("Pedido encontrado: ID {$order['id']}, Status atual: {$order['payment_status']}");
    
    // Mapear status
    $newStatus = mapAsaasStatus($asaasStatus);
    
    if ($newStatus === 'unknown') {
        logMessage("Status desconhecido do Asaas: $asaasStatus", 'WARNING');
        sendResponse(true, 'Status desconhecido, mas webhook processado');
    }
    
    // Registrar log do webhook
    try {
        $stmt = $pdo->prepare("
            INSERT INTO payment_logs (order_id, payment_id, event_type, event_data, webhook_data, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $order['id'],
            $paymentId,
            $event,
            json_encode($payment),
            json_encode($data)
        ]);
    } catch (Exception $e) {
        logMessage("Erro ao salvar log: " . $e->getMessage(), 'WARNING');
    }

    // Atualizar status se mudou
    if ($newStatus !== $order['payment_status']) {
        // Verificar se campo paid_at existe
        $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'paid_at'");
        $hasPaidAt = $stmt->rowCount() > 0;

        if ($hasPaidAt) {
            $paidAt = ($newStatus === 'paid') ? date('Y-m-d H:i:s') : null;
            $stmt = $pdo->prepare("
                UPDATE orders
                SET payment_status = ?, paid_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$newStatus, $paidAt, $order['id']]);
        } else {
            $stmt = $pdo->prepare("
                UPDATE orders
                SET payment_status = ?
                WHERE id = ?
            ");
            $stmt->execute([$newStatus, $order['id']]);
        }

        logMessage("Status atualizado de '{$order['payment_status']}' para '$newStatus' - Order ID: {$order['id']}");

        // Se foi pago, gerar token de download (se tabela existir)
        if ($newStatus === 'paid') {
            $stmt = $pdo->query("SHOW TABLES LIKE 'download_tokens'");
            if ($stmt->rowCount() > 0) {
                generateDownloadToken($pdo, $order['id'], $order['product_id']);
            } else {
                logMessage("Tabela download_tokens não existe, pulando geração de token");
            }
        }
    } else {
        logMessage("Status não mudou, mantendo: {$order['payment_status']}");
    }
    
    sendResponse(true, 'Webhook processado com sucesso', 200, [
        'order_id' => $order['id'],
        'old_status' => $order['payment_status'],
        'new_status' => $newStatus
    ]);
    
} catch (Exception $e) {
    logMessage("ERRO no webhook: " . $e->getMessage(), 'ERROR');
    sendResponse(false, 'Erro interno do servidor', 500);
}
?>
