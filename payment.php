<?php
session_start();
require_once __DIR__ . '/admin/database/connection.php';
require_once __DIR__ . '/admin/config/asaas.php';

if (!isset($_GET['product_id'])) {
    header('Location: index.php');
    exit;
}

$product_id = $_GET['product_id'];

// Buscar informações do produto
$db = Database::getInstance();
$pdo = $db->getConnection();

$stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
$stmt->execute([$product_id]);
$product = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$product) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento - <?php echo htmlspecialchars($product['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        .payment-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .product-image {
            max-width: 200px;
            margin: 0 auto 20px;
        }
        .product-image img {
            width: 100%;
            height: auto;
        }
        .qr-code-container {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code {
            max-width: 200px;
            margin: 0 auto;
        }
        .pix-code {
            word-break: break-all;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
        }
        .copy-button {
            margin: 10px 0;
        }
        #payment-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="payment-container">
            <h2 class="text-center mb-4">Pagamento via PIX</h2>
            
            <div class="card">
                <div class="card-body">
                    <div class="product-image">
                        <img src="uploads/products/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-fluid" onerror="this.src='admin/images/no-image.png'">
                    </div>
                    
                    <h3 class="text-center"><?php echo htmlspecialchars($product['name']); ?></h3>
                    <p class="text-center">R$ <?php echo number_format($product['price'], 2, ',', '.'); ?></p>
                    
                    <div id="payment-form">
                        <div class="mb-3">
                            <label for="customer_name" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="customer_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="customer_email" class="form-label">E-mail</label>
                            <input type="email" class="form-control" id="customer_email" required>
                        </div>
                        <div class="mb-3">
                            <label for="customer_whatsapp" class="form-label">WhatsApp</label>
                            <input type="text" class="form-control" id="customer_whatsapp" placeholder="(99) 99999-9999" required>
                        </div>
                        <div class="mb-3">
                            <label for="customer_cpf_cnpj" class="form-label">CPF/CNPJ</label>
                            <input type="text" class="form-control" id="customer_cpf_cnpj" name="customer_cpf_cnpj" placeholder="000.000.000-00 ou 00.000.000/0000-00" required>
                            <small class="form-text text-muted">Obrigatório para processamento do pagamento</small>
                        </div>
                        <button type="button" class="btn btn-primary w-100" onclick="processPayment(<?php echo $product_id; ?>)">
                            Gerar PIX
                        </button>
                    </div>

                    <div id="payment-info" style="display: none;">
                        <div class="qr-code-container">
                            <img id="qr-code" class="qr-code" src="/placeholder.svg" alt="QR Code PIX">
                        </div>
                        
                        <div class="text-center mb-3">
                            <p class="mb-2">Código PIX:</p>
                            <div class="pix-code" id="pix-code"></div>
                            <button class="btn btn-outline-primary copy-button" onclick="copyPixCode()">
                                <i class="fas fa-copy"></i> Copiar código
                            </button>
                        </div>
                        
                        <div id="payment-status" class="status-pending">
                            Aguardando pagamento...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Carregamento -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <h5>Processando seu pagamento</h5>
                    <p class="text-muted">Por favor, aguarde enquanto preparamos seu pagamento...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script>
        // Função para processar o pagamento
        async function processPayment(productId) {
            // Validar campos
            const customerName = document.getElementById('customer_name').value.trim();
            const customerEmail = document.getElementById('customer_email').value.trim();
            const customerWhatsapp = document.getElementById('customer_whatsapp').value.trim();
            const customerCpfCnpj = document.getElementById('customer_cpf_cnpj').value.trim();
            
            if (!customerName || !customerEmail || !customerWhatsapp || !customerCpfCnpj) {
                Swal.fire({
                    icon: 'error',
                    title: 'Campos obrigatórios',
                    text: 'Por favor, preencha todos os campos do formulário.'
                });
                return;
            }
            
            // Validar CPF/CNPJ
            const cpfCnpjClean = customerCpfCnpj.replace(/\D/g, "");
            if (cpfCnpjClean.length !== 11 && cpfCnpjClean.length !== 14) {
                Swal.fire({
                    icon: 'error',
                    title: 'CPF/CNPJ inválido',
                    text: 'Por favor, insira um CPF (11 dígitos) ou CNPJ (14 dígitos) válido.'
                });
                return;
            }
            
            // Mostrar modal de carregamento
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();
            
            try {
                // Enviar dados para processamento
                const response = await fetch('process_payment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        customer_name: customerName,
                        customer_email: customerEmail,
                        customer_whatsapp: customerWhatsapp,
                        customer_cpf_cnpj: customerCpfCnpj,
                        customer_document: customerCpfCnpj // Adicionando campo alternativo para compatibilidade
                    })
                });
                
                const data = await response.json();
                
                // Esconder modal de carregamento
                loadingModal.hide();
                
                if (data.success) {
                    // Mostrar informações do PIX
                    document.getElementById('payment-form').style.display = 'none';
                    document.getElementById('payment-info').style.display = 'block';
                    
                    // Atualizar QR Code e código PIX
                    document.getElementById('qr-code').src = `data:image/png;base64,${data.qr_code}`;
                    document.getElementById('pix-code').textContent = data.pix_code;
                    
                    // Iniciar verificação de status
                    startCheckingPaymentStatus(data.payment_id);
                } else {
                    throw new Error(data.error || 'Erro ao processar pagamento');
                }
            } catch (error) {
                loadingModal.hide();
                Swal.fire({
                    icon: 'error',
                    title: 'Erro no processamento',
                    text: error.message || 'Não foi possível processar o pagamento.'
                });
            }
        }
        
        // Função para copiar código PIX
        function copyPixCode() {
            const pixCode = document.getElementById('pix-code').textContent;
            navigator.clipboard.writeText(pixCode)
                .then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Código copiado!',
                        text: 'O código PIX foi copiado para sua área de transferência.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                })
                .catch(err => {
                    console.error('Erro ao copiar:', err);
                    // Fallback para execCommand
                    const textArea = document.createElement('textarea');
                    textArea.value = pixCode;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Código copiado!',
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
        }
        
        // Função para verificar status do pagamento
        async function checkPaymentStatus(paymentId) {
            try {
                const response = await fetch(`check_payment.php?payment_id=${paymentId}`);
                const data = await response.json();
                
                if (data.success) {
                    const statusElement = document.getElementById('payment-status');
                    
                    if (data.status === 'approved') {
                        statusElement.textContent = 'Pagamento aprovado! Obrigado pela compra.';
                        statusElement.className = 'status-approved';
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'Pagamento Confirmado!',
                            text: 'O acesso ao seu produto foi liberado e enviado para seu email.',
                            confirmButtonColor: '#28a745'
                        }).then(() => {
                            window.location.href = 'index.php';
                        });
                        
                        return true;
                    } else {
                        statusElement.textContent = data.message || 'Aguardando confirmação do pagamento...';
                        return false;
                    }
                } else {
                    console.error('Erro ao verificar status:', data.error);
                    return false;
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
                return false;
            }
        }
        
        // Função para iniciar verificação periódica
        function startCheckingPaymentStatus(paymentId) {
            // Verificar imediatamente
            checkPaymentStatus(paymentId);
            
            // Continuar verificando a cada 5 segundos
            const interval = setInterval(() => {
                checkPaymentStatus(paymentId).then(result => {
                    if (result) {
                        clearInterval(interval);
                    }
                });
            }, 5000);
            
            // Parar de verificar após 10 minutos
            setTimeout(() => {
                clearInterval(interval);
            }, 600000);
        }
        
        // Adicionar máscara ao campo de WhatsApp
        document.getElementById('customer_whatsapp').addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                if (value.length > 2) {
                    value = '(' + value.substring(0, 2) + ') ' + value.substring(2);
                }
                if (value.length > 10) {
                    value = value.substring(0, 10) + '-' + value.substring(10);
                }
                e.target.value = value;
            }
        });
        
        // Adicionar máscara ao campo de CPF/CNPJ
        document.getElementById('customer_cpf_cnpj').addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                // Formatar como CPF: 000.000.000-00
                if (value.length > 9) {
                    value = value.substring(0, 3) + '.' + value.substring(3, 6) + '.' + value.substring(6, 9) + '-' + value.substring(9);
                } else if (value.length > 6) {
                    value = value.substring(0, 3) + '.' + value.substring(3, 6) + '.' + value.substring(6);
                } else if (value.length > 3) {
                    value = value.substring(0, 3) + '.' + value.substring(3);
                }
            } else {
                // Formatar como CNPJ: 00.000.000/0000-00
                if (value.length > 12) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + value.substring(5, 8) + '/' + value.substring(8, 12) + '-' + value.substring(12);
                } else if (value.length > 8) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + value.substring(5, 8) + '/' + value.substring(8);
                } else if (value.length > 5) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + value.substring(5);
                } else if (value.length > 2) {
                    value = value.substring(0, 2) + '.' + value.substring(2);
                }
            }
            e.target.value = value;
        });
    </script>
</body>
</html>