<?php
require_once 'session.php';
require_once 'database/connection.php';

// Verifica se está logado como admin
if (!isset($_SESSION['admin']) || $_SESSION['admin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Acesso não autorizado']);
    exit;
}

// Verifica se o ID foi fornecido
if (!isset($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'ID não fornecido']);
    exit;
}

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Busca os detalhes da venda
    $stmt = $pdo->prepare("
        SELECT o.*, p.name as product_name 
        FROM orders o 
        LEFT JOIN products p ON o.product_id = p.id 
        WHERE o.id = ?
    ");
    
    $stmt->execute([$_GET['id']]);
    $sale = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sale) {
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'sale' => $sale]);
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Venda não encontrada']);
    }
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Erro ao buscar detalhes: ' . $e->getMessage()]);
}
