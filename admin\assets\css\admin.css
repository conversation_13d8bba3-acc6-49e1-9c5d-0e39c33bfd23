:root {
    --main-bg-color: #f3f4f6;
    --primary-color: #4318FF;
    --secondary-color: #868dfb;
}

#wrapper {
    overflow-x: hidden;
    background-color: var(--main-bg-color);
}

#sidebar-wrapper {
    min-height: 100vh;
    margin-left: -15rem;
    transition: margin 0.25s ease-out;
}

#sidebar-wrapper .sidebar-heading {
    padding: 0.875rem 1.25rem;
    font-size: 1.2rem;
}

#sidebar-wrapper .list-group {
    width: 15rem;
}

#page-content-wrapper {
    min-width: 100vw;
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

.primary-text {
    color: var(--primary-color);
}

.second-text {
    color: #6c757d;
}

.secondary-bg {
    background-color: var(--secondary-color);
}

.rounded-full {
    border-radius: 100%;
}

#menu-toggle {
    cursor: pointer;
}

.list-group-item {
    border: none;
    padding: 20px 30px;
}

.list-group-item.active {
    background-color: transparent;
    color: var(--primary-color);
    font-weight: bold;
    border: none;
}

@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -15rem;
    }
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.table {
    border-radius: 10px;
    overflow: hidden;
}