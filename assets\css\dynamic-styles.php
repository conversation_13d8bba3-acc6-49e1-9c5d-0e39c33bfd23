<?php
header('Content-Type: text/css');
require_once '../../admin/database/connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->query("SELECT * FROM site_settings LIMIT 1");
    $settings = $stmt->fetch();

    $primary_color = $settings['primary_color'] ?? '#4169E1';
    $footer_color = $settings['footer_color'] ?? '#1B2838';
    $button_gradient_start = $settings['button_gradient_start'] ?? '#28a745';
    $button_gradient_end = $settings['button_gradient_end'] ?? '#218838';
    $header_gradient_start = $settings['header_gradient_start'] ?? '#28a745';
    $header_gradient_end = $settings['header_gradient_end'] ?? '#4169E1';
} catch (PDOException $e) {
    // Em caso de erro, usar cores padrão
    $primary_color = '#4169E1';
    $footer_color = '#1B2838';
    $button_gradient_start = '#28a745';
    $button_gradient_end = '#218838';
    $header_gradient_start = '#28a745';
    $header_gradient_end = '#4169E1';
}
?>

/* Cores e Estilos Globais */
:root {
    --primary-color: <?php echo $primary_color; ?>;
    --footer-color: <?php echo $footer_color; ?>;
    --button-gradient-start: <?php echo $button_gradient_start; ?>;
    --button-gradient-end: <?php echo $button_gradient_end; ?>;
    --header-gradient-start: <?php echo $header_gradient_start; ?>;
    --header-gradient-end: <?php echo $header_gradient_end; ?>;
}

/* Estilos do cabeçalho */
.header {
    background: var(--header-gradient-end);
    color: white;
    padding: 10px 0;
    text-align: center;
}

.header .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header img {
    vertical-align: middle;
    max-height: 40px;
    margin-right: 15px;
}

.header h1 {
    margin: 0;
    font-size: 22px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@media (max-width: 768px) {
    .header h1 {
        font-size: 18px;
    }
    .header {
        padding: 8px 0;
    }
    .header img {
        max-height: 30px;
        margin-right: 10px;
    }
}

/* Estilos dos botões */
.btn-primary {
    background: linear-gradient(135deg, var(--button-gradient-start), var(--button-gradient-end));
    border: none;
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--button-gradient-end), var(--button-gradient-start));
    color: white;
}

/* Estilos do footer */
.footer {
    background-color: var(--footer-color);
    color: white;
    padding: 8px 0;
    margin-top: auto;
}

.footer p {
    margin-bottom: 4px;
    font-size: 14px;
}

.footer .social-links {
    margin-top: 2px;
}

.footer .social-links a {
    color: white;
    margin: 0 8px;
    font-size: 18px;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.footer .social-links a:hover {
    opacity: 0.8;
}

.footer .social-links .fa-whatsapp {
    color: #25D366;
}

.footer .social-links .fa-facebook {
    color: #1877F2;
}

.footer .social-links .fa-instagram {
    color: #E4405F;
}

.footer .social-links .fa-youtube {
    color: #FF0000;
}

/* Estilos do rodapé */
footer {
    background-color: var(--footer-color);
    color: white;
    padding: 20px 0;
}

/* Links no rodapé */
footer a {
    color: white;
    text-decoration: none;
}

footer a:hover {
    color: var(--primary-color);
}

/* Cores de destaque */
.highlight {
    color: var(--primary-color);
}

/* Bordas e elementos de destaque */
.border-primary {
    border-color: var(--primary-color) !important;
}

/* Cores de fundo */
.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Gradientes adicionais */
.gradient-bg {
    background: linear-gradient(135deg, var(--button-gradient-start), var(--button-gradient-end));
}

/* Estilos para o chat */
.chat-widget {
    background: linear-gradient(135deg, var(--header-gradient-start), var(--header-gradient-end));
    color: white;
}
