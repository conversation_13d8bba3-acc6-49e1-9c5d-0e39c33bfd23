<?php
header('Content-Type: application/json');
require_once '../includes/auth_check.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Get sales data for chart
    $stmt = $pdo->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total_orders,
            SUM(total_amount) as revenue
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $salesData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get top products
    $stmt = $pdo->query("
        SELECT 
            p.name,
            COUNT(o.id) as total_sales,
            SUM(o.total_amount) as revenue
        FROM products p
        LEFT JOIN orders o ON p.id = o.product_id
        WHERE o.payment_status = 'approved'
        GROUP BY p.id
        ORDER BY total_sales DESC
        LIMIT 5
    ");
    $topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'sales' => $salesData,
            'top_products' => $topProducts
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erro ao buscar dados do dashboard'
    ]);
}