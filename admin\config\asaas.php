<?php
// Configuração do Asaas
require_once __DIR__ . '/../database/connection.php';

// Buscar credenciais do banco de dados
$db = Database::getInstance();
$pdo = $db->getConnection();
$stmt = $pdo->query("SELECT api_key, environment FROM payment_credentials LIMIT 1");
$credentials = $stmt->fetch(PDO::FETCH_ASSOC);

// Definir constantes
if ($credentials) {
    define('ASAAS_API_KEY', $credentials['api_key']);
    define('ASAAS_ENVIRONMENT', $credentials['environment'] ?? 'sandbox');
} else {
    // Valor padrão da chave de API fornecida no seu código
    define('ASAAS_API_KEY', 'aact_prod_000MzkwODA2MWY2OGM3MWRlMDU2NWM3MzJlNzZmNGZhZGY6OjM5YzEzNmY0LTJkODEtNDZlMC05ODBjLTM0NTc4ZmJlNmVhYTo6JGFhY2hfMjUwZjA2YWYtY2FkOS00YzQ0LWIxYzItN2YzMWViMzZjZjFk');
    define('ASAAS_ENVIRONMENT', 'production');
}

// Definir URL da API baseada no ambiente
define('ASAAS_API_URL', ASAAS_ENVIRONMENT === 'production' 
    ? 'https://api.asaas.com/v3' 
    : 'https://api-sandbox.asaas.com/v3');

// Função para obter os headers da API
function getAsaasHeaders() {
    return [
        'Content-Type: application/json',
        'access_token: ' . ASAAS_API_KEY,
        'User-Agent: Sistema de Pagamentos PIX (PHP/' . phpversion() . ')'
    ];
}

// Mapear status do Asaas para nosso sistema
function mapAsaasStatus($status) {
    $status_map = [
        'RECEIVED'   => 'approved',
        'CONFIRMED'  => 'approved',
        'PENDING'    => 'pending',
        'OVERDUE'    => 'pending',
        'REFUNDED'   => 'refunded',
        'CANCELLED'  => 'cancelled',
        'FAILED'     => 'cancelled'
    ];
    
    return $status_map[$status] ?? 'pending';
}
