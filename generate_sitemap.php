<?php
/**
 * Gerador Dinâmico de Sitemap - KESUNG SITE
 * Gera sitemap.xml automaticamente baseado nos produtos do banco
 */

header('Content-Type: application/xml; charset=utf-8');

try {
    // Conexão com banco
    $pdo = new PDO(
        "mysql:host=localhost;dbname=kesung-site;charset=utf8mb4",
        "root",
        "",
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Configurações do site
    $baseUrl = 'https://kesungsite.com';
    $currentDate = date('Y-m-d');
    
    // Buscar produtos ativos
    $stmt = $pdo->query("
        SELECT id, name, description, price, image, created_at, updated_at
        FROM products 
        WHERE status = 'active'
        ORDER BY created_at DESC
    ");
    $produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Função para criar slug SEO-friendly
    function createSlug($text) {
        $text = strtolower($text);
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        $text = preg_replace('/[\s-]+/', '-', $text);
        return trim($text, '-');
    }
    
    // Função para determinar prioridade baseada no produto
    function getProductPriority($productName) {
        $highPriority = ['curso', 'script', 'sistema', 'completo'];
        $name = strtolower($productName);
        
        foreach ($highPriority as $keyword) {
            if (strpos($name, $keyword) !== false) {
                return '0.9';
            }
        }
        return '0.8';
    }
    
    // Iniciar XML
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . "\n";
    echo '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . "\n";
    
    // Página principal
    echo "    <url>\n";
    echo "        <loc>{$baseUrl}/</loc>\n";
    echo "        <lastmod>{$currentDate}</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n\n";
    
    // Produtos dinâmicos
    foreach ($produtos as $produto) {
        $slug = createSlug($produto['name']);
        $priority = getProductPriority($produto['name']);
        $lastmod = $produto['updated_at'] ? date('Y-m-d', strtotime($produto['updated_at'])) : $currentDate;
        
        echo "    <url>\n";
        echo "        <loc>{$baseUrl}/produto/{$slug}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>weekly</changefreq>\n";
        echo "        <priority>{$priority}</priority>\n";
        
        // Adicionar imagem se existir
        if (!empty($produto['image'])) {
            echo "        <image:image>\n";
            echo "            <image:loc>{$baseUrl}/uploads/products/{$produto['image']}</image:loc>\n";
            echo "            <image:title>" . htmlspecialchars($produto['name']) . "</image:title>\n";
            echo "            <image:caption>" . htmlspecialchars($produto['description']) . "</image:caption>\n";
            echo "        </image:image>\n";
        }
        
        echo "    </url>\n\n";
    }
    
    // Páginas estáticas importantes
    $staticPages = [
        ['url' => '/cursos-programacao', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => '/scripts-php', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => '/casa-apostas', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => '/marketing-digital', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => '/sobre', 'priority' => '0.6', 'changefreq' => 'monthly'],
        ['url' => '/contato', 'priority' => '0.6', 'changefreq' => 'monthly'],
        ['url' => '/termos-uso', 'priority' => '0.5', 'changefreq' => 'yearly'],
        ['url' => '/politica-privacidade', 'priority' => '0.5', 'changefreq' => 'yearly']
    ];
    
    foreach ($staticPages as $page) {
        echo "    <url>\n";
        echo "        <loc>{$baseUrl}{$page['url']}</loc>\n";
        echo "        <lastmod>{$currentDate}</lastmod>\n";
        echo "        <changefreq>{$page['changefreq']}</changefreq>\n";
        echo "        <priority>{$page['priority']}</priority>\n";
        echo "    </url>\n\n";
    }
    
    // Blog/Artigos SEO
    $blogPosts = [
        ['slug' => 'como-criar-site-profissional-2024', 'title' => 'Como Criar um Site Profissional em 2024'],
        ['slug' => 'melhores-scripts-php-casa-apostas', 'title' => 'Melhores Scripts PHP para Casa de Apostas'],
        ['slug' => 'facebook-ads-para-iniciantes-guia-completo', 'title' => 'Facebook Ads para Iniciantes - Guia Completo'],
        ['slug' => 'como-montar-casa-apostas-online', 'title' => 'Como Montar uma Casa de Apostas Online'],
        ['slug' => 'curso-programacao-web-vale-pena', 'title' => 'Curso de Programação Web Vale a Pena?']
    ];
    
    foreach ($blogPosts as $post) {
        echo "    <url>\n";
        echo "        <loc>{$baseUrl}/blog/{$post['slug']}</loc>\n";
        echo "        <lastmod>{$currentDate}</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.7</priority>\n";
        echo "    </url>\n\n";
    }
    
    echo "</urlset>\n";
    
} catch (Exception $e) {
    // Em caso de erro, retornar sitemap básico
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    echo "    <url>\n";
    echo "        <loc>https://kesungsite.com/</loc>\n";
    echo "        <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";
    echo "</urlset>\n";
}
?>
