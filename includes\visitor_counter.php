<?php
/**
 * Contador de Visitantes Corrigido - Sistema kesung-site
 */

require_once __DIR__ . "/../admin/database/connection.php";

function detectVPN($ip) {
    // Verificação simplificada de VPN
    $vpnRanges = [
        "10.0.0.0/8",
        "**********/12", 
        "***********/16"
    ];
    
    foreach ($vpnRanges as $range) {
        if (ipInRange($ip, $range)) {
            return true;
        }
    }
    
    return false;
}

function ipInRange($ip, $range) {
    list($subnet, $bits) = explode("/", $range);
    $ip = ip2long($ip);
    $subnet = ip2long($subnet);
    $mask = -1 << (32 - $bits);
    $subnet &= $mask;
    return ($ip & $mask) == $subnet;
}

function logVisitor() {
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        $ip = $_SERVER["REMOTE_ADDR"] ?? "unknown";
        $userAgent = $_SERVER["HTTP_USER_AGENT"] ?? "unknown";
        
        // Verificar se é um admin
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION["admin_id"])) {
            return; // Não registra visitas de admin
        }
        
        // Gerar um ID único para o dispositivo
        $deviceId = md5($ip . $userAgent);
        $cookieName = "last_visit_" . $deviceId;
        
        // Verificar última visita do cookie
        if (isset($_COOKIE[$cookieName])) {
            $lastVisit = strtotime($_COOKIE[$cookieName]);
            $now = time();
            $hoursSinceLastVisit = ($now - $lastVisit) / 3600;
            
            // Se não passou 24 horas, não registra
            if ($hoursSinceLastVisit < 24) {
                return;
            }
        }
        
        // Detectar VPN
        $isVpn = detectVPN($ip);
        
        // Obter localização (simplificado)
        $country = "Brasil";
        $city = "Desconhecido";
        
        // Tentar obter localização via API (opcional)
        try {
            $ipInfo = @json_decode(file_get_contents("http://ip-api.com/json/{$ip}?timeout=2"), true);
            if ($ipInfo && $ipInfo["status"] == "success") {
                $country = $ipInfo["country"] ?? $country;
                $city = $ipInfo["city"] ?? $city;
            }
        } catch (Exception $e) {
            // Ignorar erro de geolocalização
        }
        
        // Registrar nova visita
        $stmt = $pdo->prepare("
            INSERT INTO visitors (ip, user_agent, country, city, is_vpn, visit_time) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $ip,
            $userAgent,
            $country,
            $city,
            $isVpn ? 1 : 0
        ]);

        // Se for VPN, registrar alerta
        if ($isVpn) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO alerts (type, message, created_at) 
                    VALUES (\"vpn_detected\", ?, NOW())
                ");
                $message = "Acesso via VPN detectado - IP: {$ip} - País: {$country}";
                $stmt->execute([$message]);
            } catch (Exception $e) {
                // Ignorar erro de alerta se tabela não existir
            }
        }
        
        // Definir cookie com validade de 24 horas
        $cookieOptions = [
            "expires" => time() + (24 * 3600),
            "path" => "/",
            "secure" => false, // HTTP local
            "httponly" => true,
            "samesite" => "Lax"
        ];
        
        setcookie($cookieName, date("Y-m-d H:i:s"), $cookieOptions);
        
    } catch (PDOException $e) {
        error_log("Erro ao registrar visitante: " . $e->getMessage());
    } catch (Exception $e) {
        error_log("Erro geral no visitor counter: " . $e->getMessage());
    }
}
?>