<?php
require_once 'admin/database/connection.php';

echo "<h2>🔧 Corrigindo Login do Admin</h2>";

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h3>1. Verificando tabela admins...</h3>";
    
    // Verificar se a tabela admins existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela 'admins' não existe. Criando...</p>";
        
        $pdo->exec("
            CREATE TABLE admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                role VARCHAR(50) DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p>✅ Tabela 'admins' criada com sucesso!</p>";
    } else {
        echo "<p>✅ Tabela 'admins' já existe</p>";
    }
    
    echo "<h3>2. Verificando estrutura da tabela...</h3>";
    
    // Verificar colunas da tabela
    $stmt = $pdo->query("DESCRIBE admins");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Colunas encontradas: " . implode(', ', $columns) . "</p>";
    
    echo "<h3>3. Verificando admins existentes...</h3>";
    
    // Listar todos os admins
    $stmt = $pdo->query("SELECT id, name, email, role, created_at FROM admins");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($admins)) {
        echo "<p>❌ Nenhum admin encontrado</p>";
    } else {
        echo "<p>✅ Admins encontrados:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Role</th><th>Criado em</th></tr>";
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td>{$admin['name']}</td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['role']}</td>";
            echo "<td>{$admin['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4. Criando/Atualizando admin padrão...</h3>";
    
    // Deletar admin existente se houver
    $pdo->exec("DELETE FROM admins WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    echo "<p>🗑️ Admins antigos removidos</p>";
    
    // Criar novo admin
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO admins (name, email, password, role) VALUES (?, ?, ?, ?)");
    $stmt->execute(['Administrador', '<EMAIL>', $adminPassword, 'admin']);
    
    echo "<p>✅ Novo admin criado com sucesso!</p>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🔑 Credenciais de Login:</h4>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Senha:</strong> admin123</p>";
    echo "</div>";
    
    echo "<h3>5. Testando login...</h3>";
    
    // Testar o login
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "<p>✅ Teste de login bem-sucedido!</p>";
        echo "<p>Hash da senha: " . substr($admin['password'], 0, 30) . "...</p>";
    } else {
        echo "<p>❌ Falha no teste de login</p>";
    }
    
    echo "<h3>6. Verificando arquivo de login...</h3>";
    
    // Verificar se o arquivo de login existe
    if (file_exists('admin/login.php')) {
        echo "<p>✅ Arquivo admin/login.php existe</p>";
    } else {
        echo "<p>❌ Arquivo admin/login.php não encontrado</p>";
    }
    
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🚀 Próximos Passos:</h4>";
    echo "<p>1. Acesse: <a href='admin/login.php' target='_blank'>http://localhost/admin/login.php</a></p>";
    echo "<p>2. Use as credenciais: <EMAIL> / admin123</p>";
    echo "<p>3. Se ainda não funcionar, verifique os logs de erro do PHP</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
    echo "<p>Detalhes: " . $e->getTraceAsString() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
table {
    background: white;
    margin: 10px 0;
}
th, td {
    padding: 8px 12px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
</style>
