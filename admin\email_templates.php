<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE email_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                body TEXT NOT NULL,
                description TEXT,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);

        // Inserir templates padrão
        $defaultTemplates = [
            [
                'name' => 'Confirmação de Compra',
                'subject' => 'Confirmação da sua compra - {product_name}',
                'body' => '<h2>Obrigado pela sua compra!</h2><p>Olá {customer_name},</p><p>Sua compra de <strong>{product_name}</strong> no valor de <strong>R$ {amount}</strong> foi confirmada em {purchase_date}.</p><p>Link para download: <a href="{download_link}">Clique aqui</a></p><p>Obrigado!</p>',
                'description' => 'Email enviado após confirmação do pagamento'
            ],
            [
                'name' => 'Pagamento Pendente',
                'subject' => 'Pagamento pendente - {product_name}',
                'body' => '<h2>Pagamento Pendente</h2><p>Olá {customer_name},</p><p>Seu pagamento para <strong>{product_name}</strong> ainda está pendente.</p><p>Assim que o pagamento for confirmado, você receberá o link para download.</p>',
                'description' => 'Email enviado quando o pagamento está pendente'
            ]
        ];

        foreach ($defaultTemplates as $template) {
            $stmt = $pdo->prepare("INSERT INTO email_templates (name, subject, body, description) VALUES (?, ?, ?, ?)");
            $stmt->execute([$template['name'], $template['subject'], $template['body'], $template['description']]);
        }
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'create':
                    $stmt = $pdo->prepare("
                        INSERT INTO email_templates (name, subject, body, description, is_active)
                        VALUES (?, ?, ?, ?, 1)
                    ");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['subject'],
                        $_POST['body'],
                        $_POST['description']
                    ]);
                    $_SESSION['success'] = 'Template criado com sucesso!';
                    break;

                case 'update':
                    $stmt = $pdo->prepare("
                        UPDATE email_templates
                        SET name = ?, subject = ?, body = ?, description = ?, is_active = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['subject'],
                        $_POST['body'],
                        $_POST['description'],
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['id']
                    ]);
                    $_SESSION['success'] = 'Template atualizado com sucesso!';
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM email_templates WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $_SESSION['success'] = 'Template excluído com sucesso!';
                    break;
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Erro: ' . $e->getMessage();
        }
        header('Location: email_templates.php');
        exit;
    }
}

// Buscar template específico se ID fornecido
$template = null;
if (isset($_GET['id'])) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $_SESSION['error'] = 'Erro ao buscar template: ' . $e->getMessage();
    }
}

// Buscar todos os templates
try {
    $stmt = $pdo->query("SELECT * FROM email_templates ORDER BY name");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $templates = [];
    $_SESSION['error'] = 'Erro ao buscar templates: ' . $e->getMessage();
}

// Variáveis disponíveis para os templates
$available_variables = [
    'customer_name' => 'Nome do cliente',
    'product_name' => 'Nome do produto',
    'amount' => 'Valor da compra',
    'purchase_date' => 'Data da compra',
    'download_link' => 'Link para download'
];

include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Templates de Email</h1>
    
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['success'] ?>
            <?php unset($_SESSION['success']) ?>
        </div>
    <?php endif ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <?= $_SESSION['error'] ?>
            <?php unset($_SESSION['error']) ?>
        </div>
    <?php endif ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-envelope me-1"></i>
                    <?= $template ? 'Editar Template' : 'Novo Template' ?>
                </div>
                <div class="card-body">
                    <form method="post">
                        <input type="hidden" name="action" value="<?= $template ? 'update' : 'create' ?>">
                        <?php if ($template): ?>
                            <input type="hidden" name="id" value="<?= $template['id'] ?>">
                        <?php endif ?>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nome do Template</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   value="<?= $template['name'] ?? '' ?>">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control" id="description" name="description" rows="2"><?= $template['description'] ?? '' ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Assunto do Email</label>
                            <input type="text" class="form-control" id="subject" name="subject" required
                                   value="<?= $template['subject'] ?? '' ?>">
                        </div>

                        <div class="mb-3">
                            <label for="body" class="form-label">Corpo do Email</label>
                            <textarea class="form-control tinymce" id="body" name="body" rows="15" required><?= $template['body'] ?? '' ?></textarea>
                        </div>

                        <?php if ($template): ?>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                       <?= $template['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">Template Ativo</label>
                            </div>
                        <?php endif ?>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <?= $template ? 'Atualizar' : 'Criar' ?> Template
                            </button>
                            <?php if ($template): ?>
                                <a href="email_templates.php" class="btn btn-secondary">Cancelar</a>
                            <?php endif ?>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-code me-1"></i>
                    Variáveis Disponíveis
                </div>
                <div class="card-body">
                    <p class="small text-muted">Use estas variáveis no assunto ou corpo do email:</p>
                    <ul class="list-unstyled mb-0">
                        <?php foreach ($available_variables as $var => $desc): ?>
                            <li><code>{<?= $var ?>}</code> - <?= $desc ?></li>
                        <?php endforeach ?>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table me-1"></i>
                    Templates Existentes
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Descrição</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($templates as $t): ?>
                                <tr>
                                    <td><?= htmlspecialchars($t['name']) ?></td>
                                    <td><?= htmlspecialchars($t['description'] ?? 'Sem descrição') ?></td>
                                    <td>
                                        <span class="badge bg-<?= $t['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $t['is_active'] ? 'Ativo' : 'Inativo' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="?id=<?= $t['id'] ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="deleteTemplate(<?= $t['id'] ?>, '<?= htmlspecialchars($t['name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Exclusão</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o template "<span id="templateName"></span>"?</p>
            </div>
            <div class="modal-footer">
                <form method="post" id="deleteForm">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteTemplate(id, name) {
    document.getElementById('deleteId').value = id;
    document.getElementById('templateName').textContent = name;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Inicializar editor rich text
if (typeof tinymce !== 'undefined') {
    tinymce.init({
        selector: '.tinymce',
        height: 400,
        plugins: 'link lists table code',
        toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link table | code',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; font-size: 14px; }',
    });
}
</script>

<?php include 'includes/footer.php'; ?>
