document.addEventListener('DOMContentLoaded', function() {
    var el = document.getElementById("wrapper");
    var toggleButton = document.getElementById("menu-toggle");

    toggleButton.onclick = function () {
        el.classList.toggle("toggled");
    };
    
    // Marca o item atual do menu como ativo
    const currentPath = window.location.pathname;
    const menuItems = document.querySelectorAll('.list-group-item');
    
    menuItems.forEach(item => {
        if (currentPath.includes(item.getAttribute('href'))) {
            item.classList.add('active');
        }
    });
});
