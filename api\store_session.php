<?php
session_start();
require_once '../database/connection.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método inválido');
    }

    $userId = $_POST['user_id'] ?? null;

    if (!$userId) {
        throw new Exception('ID do usuário é obrigatório');
    }

    // Store user ID in session
    $_SESSION['user_id'] = $userId;

    echo json_encode([
        'success' => true,
        'message' => 'Sessão armazenada com sucesso'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
