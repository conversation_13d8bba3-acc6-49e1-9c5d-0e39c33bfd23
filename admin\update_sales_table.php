<?php
require_once '../database/connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Adiciona as colunas necessárias se não existirem
    $pdo->exec("
        ALTER TABLE sales
        ADD COLUMN IF NOT EXISTS payment_id VARCHAR(100) NULL AFTER total_amount,
        ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50) DEFAULT 'pending' AFTER payment_id,
        ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50) DEFAULT 'pix' AFTER payment_status,
        ADD COLUMN IF NOT EXISTS external_reference VARCHAR(100) NULL AFTER payment_method,
        ADD COLUMN IF NOT EXISTS customer_name VARCHAR(100) NULL AFTER external_reference,
        ADD COLUMN IF NOT EXISTS customer_lastname VARCHAR(100) NULL AFTER customer_name,
        ADD COLUMN IF NOT EXISTS customer_email VARCHAR(100) NULL AFTER customer_lastname,
        ADD COLUMN IF NOT EXISTS customer_document VARCHAR(20) NULL AFTER customer_email,
        ADD COLUMN IF NOT EXISTS customer_phone VARCHAR(20) NULL AFTER customer_document
    ");
    
    echo "Tabela sales atualizada com sucesso!<br>";
    echo "<a href='index.php' class='btn btn-primary'>Voltar</a>";
    
} catch (PDOException $e) {
    die("Erro ao atualizar tabela: " . $e->getMessage());
}
