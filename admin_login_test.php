<?php
session_start();

// Simular login de admin para teste
$_SESSION['admin_id'] = 1;
$_SESSION['admin_name'] = 'Admin Teste';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h2>🔐 Login de Admin Simulado</h2>";
echo "<p>✅ Admin ID: " . $_SESSION['admin_id'] . "</p>";
echo "<p>✅ Admin Nome: " . $_SESSION['admin_name'] . "</p>";
echo "<p>✅ Admin Email: " . $_SESSION['admin_email'] . "</p>";

echo "<hr>";
echo "<h3>📋 Dados da Sessão:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<hr>";
echo "<p><a href='admin/chat.php' class='btn btn-primary'>🚀 Ir para o Chat Admin</a></p>";
echo "<p><a href='chat.php' class='btn btn-secondary'>💬 Ir para o Chat Cliente</a></p>";

// Criar admin na tabela se não existir
require_once 'database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    // Criar tabela admins se não existir
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // Inserir admin de teste
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO admins (id, name, email, password) 
        VALUES (1, 'Admin Teste', '<EMAIL>', ?)
    ");
    $stmt->execute([password_hash('admin123', PASSWORD_DEFAULT)]);
    
    echo "<p>✅ Admin criado/atualizado na tabela admins</p>";
    
    // Verificar se admin existe em chat_users
    $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE id = 1 AND is_admin = 1");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        // Inserir admin em chat_users
        $stmt = $pdo->prepare("
            INSERT INTO chat_users (id, name, email, is_admin, created_at, last_activity) 
            VALUES (1, 'Admin Teste', '<EMAIL>', 1, NOW(), NOW())
            ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                email = VALUES(email), 
                is_admin = 1,
                last_activity = NOW()
        ");
        $stmt->execute();
        echo "<p>✅ Admin criado/atualizado na tabela chat_users</p>";
    } else {
        echo "<p>✅ Admin já existe na tabela chat_users</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    color: white;
}

.btn-primary {
    background: #007bff;
}

.btn-secondary {
    background: #6c757d;
}

.btn:hover {
    opacity: 0.8;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}
</style>
