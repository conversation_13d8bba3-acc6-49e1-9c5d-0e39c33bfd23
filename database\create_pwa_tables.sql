-- Tabelas para PWA e Sistema de Notificações

-- Tabel<PERSON> para rastrear instalações do app
CREATE TABLE IF NOT EXISTS app_installs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    install_date DATETIME NOT NULL,
    platform VARCHAR(50),
    device_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_install_date (install_date),
    INDEX idx_ip_address (ip_address),
    INDEX idx_platform (platform)
);

-- Tabela para subscriptions de push notifications
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint TEXT NOT NULL,
    keys_json JSON NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE KEY unique_endpoint (endpoint(255))
);

-- Tabela para logs de notificações enviadas
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    body TEXT,
    url VARCHAR(500),
    image_url VARCHAR(500),
    sent_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_created_at (created_at)
);

-- Tabela para campanhas de notificação
CREATE TABLE IF NOT EXISTS notification_campaigns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    url VARCHAR(500),
    image_url VARCHAR(500),
    scheduled_at DATETIME,
    sent_at DATETIME NULL,
    status ENUM('draft', 'scheduled', 'sent', 'cancelled') DEFAULT 'draft',
    target_audience JSON, -- Critérios de segmentação
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at)
);

-- Tabela para usuários que instalaram o app (com mais detalhes)
CREATE TABLE IF NOT EXISTS app_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    first_install DATETIME NOT NULL,
    last_seen DATETIME,
    install_count INT DEFAULT 1,
    platform VARCHAR(50),
    device_info JSON,
    preferences JSON, -- Preferências de notificação
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_ip_user_agent (ip_address, user_agent(255)),
    INDEX idx_platform (platform),
    INDEX idx_last_seen (last_seen),
    INDEX idx_is_active (is_active)
);

-- Tabela para rastrear interações com notificações
CREATE TABLE IF NOT EXISTS notification_interactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notification_log_id INT,
    subscription_id INT,
    action ENUM('delivered', 'clicked', 'dismissed', 'failed') NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_agent TEXT,
    ip_address VARCHAR(45),
    FOREIGN KEY (notification_log_id) REFERENCES notification_logs(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES push_subscriptions(id) ON DELETE CASCADE,
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp)
);



-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_app_installs_date_platform ON app_installs(install_date, platform);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON push_subscriptions(is_active, created_at);
CREATE INDEX IF NOT EXISTS idx_notification_logs_date ON notification_logs(created_at DESC);

-- Views úteis para relatórios (sem DEFINER para evitar problemas de privilégio)
DROP VIEW IF EXISTS v_app_install_stats;
CREATE VIEW v_app_install_stats AS
SELECT
    DATE(install_date) as install_date,
    platform,
    COUNT(*) as installs,
    COUNT(DISTINCT ip_address) as unique_users
FROM app_installs
GROUP BY DATE(install_date), platform
ORDER BY install_date DESC;

DROP VIEW IF EXISTS v_notification_performance;
CREATE VIEW v_notification_performance AS
SELECT
    nl.id,
    nl.title,
    nl.created_at,
    nl.sent_count,
    nl.failed_count,
    COALESCE(SUM(CASE WHEN ni.action = 'clicked' THEN 1 ELSE 0 END), 0) as clicks,
    COALESCE(SUM(CASE WHEN ni.action = 'dismissed' THEN 1 ELSE 0 END), 0) as dismissals,
    CASE
        WHEN nl.sent_count > 0 THEN
            ROUND((COALESCE(SUM(CASE WHEN ni.action = 'clicked' THEN 1 ELSE 0 END), 0) / nl.sent_count) * 100, 2)
        ELSE 0
    END as click_rate
FROM notification_logs nl
LEFT JOIN notification_interactions ni ON nl.id = ni.notification_log_id
GROUP BY nl.id, nl.title, nl.created_at, nl.sent_count, nl.failed_count
ORDER BY nl.created_at DESC;

-- Dados de exemplo para campanhas (inserir apenas se não existirem)
INSERT IGNORE INTO notification_campaigns (name, title, body, url, status) VALUES
('Boas-vindas', 'Bem-vindo ao KESUNG SITE!', 'Obrigado por instalar nosso app! Fique por dentro das melhores promoções.', '/', 'draft'),
('Promoção Semanal', 'Promoção Especial!', 'Não perca! Descontos de até 50% em produtos selecionados.', '/#promocoes', 'draft'),
('Novo Produto', 'Novo Produto Disponível!', 'Confira nosso mais novo lançamento com preço especial.', '/#novidades', 'draft'),
('Carrinho Abandonado', 'Finalize sua Compra', 'Você esqueceu alguns itens no seu carrinho. Finalize agora!', '/#carrinho', 'draft'),
('Chat Atendimento', 'Mensagem do Atendimento', 'Nossa equipe respondeu sua mensagem. Clique para ver.', '/#chat', 'draft');
