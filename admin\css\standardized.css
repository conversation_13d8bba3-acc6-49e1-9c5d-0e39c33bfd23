/* CSS Padronizado para Admin - Todos os Elementos do Mesmo Tamanho */

/* ========== BOTÕES PADRONIZADOS ========== */

/* Botões Primários */
.btn, .btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-secondary {
    height: 45px !important;
    min-width: 120px !important;
    padding: 10px 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    border: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    line-height: 1.2 !important;
}

/* Botões Pequenos */
.btn-sm {
    height: 32px !important;
    min-width: 60px !important;
    padding: 4px 12px !important;
    font-size: 12px !important;
}

.btn-sm i {
    font-size: 12px !important;
}

/* Botões Grandes */
.btn-lg {
    height: 55px !important;
    min-width: 150px !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
}

/* ========== CARDS PADRONIZADOS ========== */

.card, .dashboard-card {
    border-radius: 12px !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
    background: white !important;
}

.card-header {
    height: 60px !important;
    padding: 15px 20px !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #e2e8f0 !important;
    border-radius: 12px 12px 0 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.card-body {
    padding: 20px !important;
}

.card-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: #1e293b !important;
}

/* ========== FORMULÁRIOS PADRONIZADOS ========== */

.form-control, .form-select {
    height: 45px !important;
    padding: 10px 15px !important;
    font-size: 14px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    background: white !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.form-label {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 8px !important;
}

textarea.form-control {
    height: auto !important;
    min-height: 100px !important;
    resize: vertical !important;
}

/* ========== TABELAS PADRONIZADAS ========== */

.table {
    font-size: 14px;
    width: 100%;
    table-layout: auto;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.table th {
    min-height: 50px;
    padding: 12px 8px;
    font-weight: 600;
    background: #f8f9fa;
    border-bottom: 2px solid #e2e8f0;
    color: #374151;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.table td {
    min-height: 45px;
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
    word-wrap: break-word;
    max-width: 200px;
}

/* ========== RESPONSIVIDADE PARA TABELAS ========== */

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 992px) {
    .table th, .table td {
        padding: 8px 4px;
        font-size: 12px;
        max-width: 120px;
    }

    .table th {
        font-size: 11px;
    }
}

@media (max-width: 576px) {
    .table {
        font-size: 11px;
    }

    .table th, .table td {
        padding: 6px 3px;
        font-size: 10px;
        max-width: 80px;
    }

    .table th {
        font-size: 9px;
        padding: 6px 2px;
    }
}

/* ========== BADGES PADRONIZADOS ========== */

.badge {
    height: 25px !important;
    min-width: 60px !important;
    padding: 5px 10px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ========== SIDEBAR PADRONIZADO ========== */

.list-group-item {
    height: 50px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    border: none !important;
    border-radius: 0 !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.3s ease !important;
}

.list-group-item i {
    width: 20px !important;
    margin-right: 12px !important;
    font-size: 16px !important;
}

/* ========== DASHBOARD CARDS PADRONIZADOS ========== */

.dashboard-card .card-body {
    height: 120px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.dashboard-card .card-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 24px !important;
}

.dashboard-card .number {
    font-size: 28px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    margin: 5px 0 !important;
}

.dashboard-card .label {
    font-size: 14px !important;
    color: #6b7280 !important;
    margin: 0 !important;
}

/* ========== MODAIS PADRONIZADOS ========== */

.modal-header {
    height: 70px !important;
    padding: 20px 25px !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
}

.modal-body {
    padding: 25px !important;
}

.modal-footer {
    height: 80px !important;
    padding: 20px 25px !important;
    border-top: 1px solid #e2e8f0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

/* ========== ALERTAS PADRONIZADOS ========== */

.alert {
    padding: 15px 20px !important;
    border-radius: 8px !important;
    border: none !important;
    font-size: 14px !important;
    margin-bottom: 20px !important;
}

/* ========== PAGINAÇÃO PADRONIZADA ========== */

.pagination .page-link {
    height: 40px !important;
    min-width: 40px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    margin: 0 2px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ========== RESPONSIVIDADE ========== */

@media (max-width: 768px) {
    .btn {
        height: 40px !important;
        min-width: 100px !important;
        padding: 8px 15px !important;
        font-size: 13px !important;
    }
    
    .card-header {
        height: 55px !important;
        padding: 12px 15px !important;
    }
    
    .form-control, .form-select {
        height: 40px !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
    }
    
    .table th, .table td {
        min-height: 40px;
        padding: 8px 6px;
        font-size: 12px;
    }

    .table th {
        font-size: 11px;
        padding: 8px 4px;
    }

    .table td {
        max-width: 150px;
        font-size: 11px;
    }
    
    .list-group-item {
        height: 45px !important;
        padding: 10px 15px !important;
        font-size: 13px !important;
    }
    
    .dashboard-card .card-body {
        height: 100px !important;
    }
    
    .dashboard-card .number {
        font-size: 24px !important;
    }
}

@media (max-width: 480px) {
    .btn {
        height: 38px !important;
        min-width: 80px !important;
        padding: 6px 12px !important;
        font-size: 12px !important;
    }
    
    .card-header {
        height: 50px !important;
        padding: 10px 12px !important;
    }
    
    .form-control, .form-select {
        height: 38px !important;
        padding: 6px 10px !important;
        font-size: 12px !important;
    }
}

/* ========== CORES PADRONIZADAS ========== */

.btn-primary { background: #3b82f6 !important; color: white !important; }
.btn-success { background: #10b981 !important; color: white !important; }
.btn-danger { background: #ef4444 !important; color: white !important; }
.btn-warning { background: #f59e0b !important; color: white !important; }
.btn-info { background: #06b6d4 !important; color: white !important; }
.btn-secondary { background: #6b7280 !important; color: white !important; }

.btn-primary:hover { background: #2563eb !important; }
.btn-success:hover { background: #059669 !important; }
.btn-danger:hover { background: #dc2626 !important; }
.btn-warning:hover { background: #d97706 !important; }
.btn-info:hover { background: #0891b2 !important; }
.btn-secondary:hover { background: #4b5563 !important; }

/* ========== MELHORIAS ESPECÍFICAS PARA TABELAS DE CLIENTES ========== */

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0,0,0,.02);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(0,0,0,.05);
}

/* Ajustes para colunas específicas */
.table td:nth-child(1), .table th:nth-child(1) { /* Nome */
    min-width: 120px;
    max-width: 150px;
}

.table td:nth-child(2), .table th:nth-child(2) { /* Email */
    min-width: 150px;
    max-width: 200px;
}

.table td:nth-child(3), .table th:nth-child(3) { /* WhatsApp */
    min-width: 100px;
    max-width: 120px;
}

.table td:nth-child(4), .table th:nth-child(4) { /* Total Pedidos */
    min-width: 80px;
    max-width: 100px;
    text-align: center;
}

.table td:nth-child(5), .table th:nth-child(5) { /* Total Gasto */
    min-width: 100px;
    max-width: 120px;
    text-align: right;
}

.table td:nth-child(6), .table th:nth-child(6) { /* Último Pedido */
    min-width: 120px;
    max-width: 140px;
}

.table td:nth-child(7), .table th:nth-child(7) { /* Ações */
    min-width: 60px;
    max-width: 80px;
    text-align: center;
}

/* Responsividade específica para tabela de clientes */
@media (max-width: 1200px) {
    .table td:nth-child(3), .table th:nth-child(3) { /* WhatsApp */
        display: none;
    }
}

@media (max-width: 992px) {
    .table td:nth-child(6), .table th:nth-child(6) { /* Último Pedido */
        display: none;
    }
}

@media (max-width: 768px) {
    .table td:nth-child(4), .table th:nth-child(4) { /* Total Pedidos */
        display: none;
    }

    .table td:nth-child(1), .table th:nth-child(1) { /* Nome */
        max-width: 100px;
    }

    .table td:nth-child(2), .table th:nth-child(2) { /* Email */
        max-width: 120px;
    }
}
