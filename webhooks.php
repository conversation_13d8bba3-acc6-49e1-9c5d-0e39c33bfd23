<?php
/**
 * Webhook Handler - Sistema kesung-site
 * Processamento de notificações de pagamento do Asaas
 * Versão Padronizada e Segura
 */

// Configurações de erro (desabilitar em produção)
error_reporting(E_ALL);
ini_set('display_errors', 0); // Desabilitado para produção

// Headers de segurança
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// Incluir conexão padronizada
require_once 'database/connection.php';

// Configurações
define('WEBHOOK_TOKEN_FILE', __DIR__ . '/config/webhook_token.txt');
define('LOG_DIR', __DIR__ . '/logs');
define('MAX_LOG_SIZE', 10 * 1024 * 1024); // 10MB

// Function to log messages
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Function to send response
function sendResponse($success, $message, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message
    ]);
    exit;
}

// Function to connect to database
function connectDB() {
    global $dbHost, $dbName, $dbUser, $dbPass;
    
    try {
        $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $db;
    } catch (PDOException $e) {
        logMessage("Database connection error: " . $e->getMessage());
        return null;
    }
}

// Get or create webhook token
function getWebhookToken() {
    global $tokenFile;
    
    if (file_exists($tokenFile)) {
        return trim(file_get_contents($tokenFile));
    } else {
        // Generate a new token
        $token = md5(uniqid(rand(), true));
        file_put_contents($tokenFile, $token);
        logMessage("Arquivo de token criado com valor padrão: $token");
        return $token;
    }
}

// Function to map Asaas status to our system status
function mapAsaasStatus($asaasStatus) {
    $statusMap = [
        'PENDING' => 'pending',
        'RECEIVED' => 'approved',
        'CONFIRMED' => 'approved',
        'OVERDUE' => 'overdue',
        'REFUNDED' => 'refunded',
        'RECEIVED_IN_CASH' => 'approved',
        'REFUND_REQUESTED' => 'refund_requested',
        'CHARGEBACK_REQUESTED' => 'chargeback',
        'CHARGEBACK_DISPUTE' => 'chargeback',
        'AWAITING_CHARGEBACK_REVERSAL' => 'chargeback',
        'DUNNING_REQUESTED' => 'dunning',
        'DUNNING_RECEIVED' => 'approved',
        'AWAITING_RISK_ANALYSIS' => 'pending'
    ];
    
    return isset($statusMap[$asaasStatus]) ? $statusMap[$asaasStatus] : 'unknown';
}

// Function to send confirmation email
function sendConfirmationEmail($paymentId, $db) {
    try {
        // Get payment and customer information
        $stmt = $db->prepare("
            SELECT 
                p.id, p.customer_id, p.product_id, p.amount, p.created_at, p.status,
                c.name as customer_name, c.email as customer_email,
                pr.name as product_name
            FROM payments p
            JOIN customers c ON p.customer_id = c.id
            JOIN products pr ON p.product_id = pr.id
            WHERE p.id = :payment_id
        ");
        
        $stmt->execute([':payment_id' => $paymentId]);
        $paymentInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$paymentInfo) {
            logMessage("Payment not found for email: $paymentId");
            return false;
        }
        
        // Get email template
        $stmt = $db->prepare("
            SELECT subject, body FROM email_templates 
            WHERE name = 'payment_confirmation' AND is_active = 1
        ");
        
        $stmt->execute();
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            logMessage("Email template not found");
            return false;
        }
        
        // Get email settings
        $stmt = $db->prepare("
            SELECT * FROM email_settings LIMIT 1
        ");
        
        $stmt->execute();
        $emailSettings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$emailSettings) {
            logMessage("Email settings not found");
            return false;
        }
        
        // Create download token
        $token = md5(uniqid(rand(), true));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        $stmt = $db->prepare("
            INSERT INTO download_tokens (payment_id, product_id, token, expires_at)
            VALUES (:payment_id, :product_id, :token, :expires_at)
        ");
        
        $stmt->execute([
            ':payment_id' => $paymentId,
            ':product_id' => $paymentInfo['product_id'],
            ':token' => $token,
            ':expires_at' => $expiresAt
        ]);
        
        // Generate access URL
        $accessUrl = "https://" . $_SERVER['HTTP_HOST'] . "/download.php?token=$token";
        
        // Replace variables in template
        $subject = str_replace(
            ['{product_name}'],
            [$paymentInfo['product_name']],
            $template['subject']
        );
        
        $body = str_replace(
            [
                '{customer_name}',
                '{product_name}',
                '{product_price}',
                '{payment_id}',
                '{payment_date}',
                '{access_url}'
            ],
            [
                $paymentInfo['customer_name'],
                $paymentInfo['product_name'],
                number_format($paymentInfo['amount'], 2, ',', '.'),
                $paymentId,
                date('d/m/Y H:i', strtotime($paymentInfo['created_at'])),
                $accessUrl
            ],
            $template['body']
        );
        
        // Send email using PHPMailer (you need to implement this)
        // For now, just log that we would send an email
        logMessage("Would send email to: " . $paymentInfo['customer_email'] . " with subject: " . $subject);
        
        // Log the email
        $stmt = $db->prepare("
            INSERT INTO email_logs (payment_id, customer_email, subject, message, status)
            VALUES (:payment_id, :customer_email, :subject, :message, 'success')
        ");
        
        $stmt->execute([
            ':payment_id' => $paymentId,
            ':customer_email' => $paymentInfo['customer_email'],
            ':subject' => $subject,
            ':message' => $body
        ]);
        
        // Update payment record
        $stmt = $db->prepare("
            UPDATE payments 
            SET email_sent = 1, email_sent_at = NOW() 
            WHERE id = :payment_id
        ");
        
        $stmt->execute([':payment_id' => $paymentId]);
        
        return true;
    } catch (PDOException $e) {
        logMessage("Error sending confirmation email: " . $e->getMessage());
        return false;
    }
}

// Log the start of webhook processing with request method
logMessage("Webhook request received - Method: " . $_SERVER['REQUEST_METHOD']);

// For GET requests, show the token (useful for setup)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // ✅ Somente mostrar o token se o IP for permitido
    // 🚨 ATENÇÃO: mantenha o IP fixo e privado. Não use IPs públicos ou genéricos.
    // ✅ Altere '**************' para o seu IP pessoal ou IP do servidor autorizado.
    $allowedIp = '**************'; // Exemplo de IP permitido
    
    // Verifica se o IP da requisição é o IP autorizado
    if ($_SERVER['REMOTE_ADDR'] === $allowedIp) {
        // Recupera ou gera o token salvo em webhook_token.txt
        $token = getWebhookToken();
        
        // Exibe o token apenas para esse IP específico
        echo "Seu token é: $token";
    } else {
        // Requisições de outros IPs apenas veem uma mensagem genérica
        echo "Webhook endpoint is active.";
    }
    exit;
}


// For POST requests, process the webhook
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logMessage("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    sendResponse(false, 'Invalid request method', 405);
}

// Get the authorization header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $authHeader);

// Log all headers for debugging
logMessage("Headers: " . json_encode($headers));

// Validate the token
$validToken = getWebhookToken();
if (empty($token) || $token !== $validToken) {
    logMessage("Token de webhook inválido: $token");
    // For now, we'll continue processing but log the invalid token
    // Uncomment the line below to enforce token validation
    // sendResponse(false, 'Token de webhook inválido', 401);
}

// Get the raw POST data
$rawData = file_get_contents('php://input');
logMessage("Received webhook data: " . $rawData);

// If raw data is empty, check if it's in $_POST
if (empty($rawData) && !empty($_POST)) {
    logMessage("Raw data empty, using POST data: " . json_encode($_POST));
    $data = $_POST;
} else {
    // Decode the JSON payload
    $data = json_decode($rawData, true);
    
    // Check if JSON is valid
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("Invalid JSON: " . json_last_error_msg());
        
        // Try to handle potential encoding issues
        $cleanData = utf8_encode($rawData);
        $data = json_decode($cleanData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            // If still invalid, check if it might be URL encoded
            if (strpos($rawData, '=') !== false) {
                parse_str($rawData, $parsedData);
                logMessage("Trying to parse as URL encoded data: " . json_encode($parsedData));
                
                // Check if there's a JSON string in a parameter
                if (!empty($parsedData['payload'])) {
                    $data = json_decode($parsedData['payload'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        logMessage("Failed to parse payload parameter as JSON");
                        sendResponse(false, 'Invalid JSON payload format', 400);
                    }
                } else {
                    $data = $parsedData;
                }
            } else {
                // Log a sample of the data to help debug
                logMessage("Sample of invalid data (first 100 chars): " . substr($rawData, 0, 100));
                sendResponse(false, 'Invalid JSON payload', 400);
            }
        }
    }
}

// For debugging, log the parsed data
logMessage("Parsed data: " . json_encode($data));

// Check if this is a ping/test request
if (isset($data['ping']) && $data['ping'] === true) {
    logMessage("Received ping test from Asaas");
    sendResponse(true, "Ping received successfully");
}

// Check if required fields exist
if (!isset($data['event']) || !isset($data['payment'])) {
    logMessage("Missing required fields in webhook data");
    sendResponse(false, 'Missing required fields', 400);
}

// Extract event type and payment data
$event = $data['event'];
$payment = $data['payment'];
$paymentId = $payment['id'] ?? '';
$status = $payment['status'] ?? '';
$value = $payment['value'] ?? 0;
$description = $payment['description'] ?? '';
$externalReference = $payment['externalReference'] ?? '';
$customer = $payment['customer'] ?? '';

// Log the event details
logMessage("Processing event: $event for payment: $paymentId, status: $status");

// Connect to database
$db = connectDB();
if (!$db) {
    sendResponse(false, 'Database connection error', 500);
}

// Map Asaas status to our system status
$systemStatus = mapAsaasStatus($status);
logMessage("Status mapeado: $systemStatus");

try {
    // Begin transaction
    $db->beginTransaction();
    
    // Check if payment exists in our system
    $stmt = $db->prepare("
        SELECT id FROM payments 
        WHERE payment_gateway_id = :payment_id OR external_reference = :external_reference
    ");
    
    $stmt->execute([
        ':payment_id' => $paymentId,
        ':external_reference' => $externalReference
    ]);
    
    $paymentRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($paymentRecord) {
        // Update existing payment
        $stmt = $db->prepare("
            UPDATE payments 
            SET status = :status, 
                payment_gateway_id = :payment_id,
                updated_at = NOW(),
                paid_at = CASE WHEN :status = 'approved' AND paid_at IS NULL THEN NOW() ELSE paid_at END
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':status' => $systemStatus,
            ':payment_id' => $paymentId,
            ':id' => $paymentRecord['id']
        ]);
        
        logMessage("Status atualizado no banco de dados");
        
        // If payment is approved, send confirmation email
        if ($systemStatus === 'approved') {
            $emailSent = sendConfirmationEmail($paymentRecord['id'], $db);
            logMessage("Email de confirmação " . ($emailSent ? "enviado" : "falhou"));
        }
    } else {
        // Log that we couldn't find the payment
        logMessage("Pagamento não encontrado no sistema: $paymentId, referência: $externalReference");
        
        // Insert into webhook_logs table
        $stmt = $db->prepare("
            INSERT INTO webhook_logs (event, payment_id, status, raw_data, processed)
            VALUES (:event, :payment_id, 'payment_not_found', :raw_data, 0)
        ");
        
        $stmt->execute([
            ':event' => $event,
            ':payment_id' => $paymentId,
            ':raw_data' => json_encode([
                'external_reference' => $externalReference,
                'status' => $status,
                'value' => $value
            ])
        ]);
    }
    
    // Log the webhook event
    $stmt = $db->prepare("
        INSERT INTO webhook_logs (event, payment_id, status, raw_data, processed)
        VALUES (:event, :payment_id, :status, :raw_data, 1)
    ");
    
    $stmt->execute([
        ':event' => $event,
        ':payment_id' => $paymentId,
        ':status' => $status,
        ':raw_data' => $rawData
    ]);
    
    // Commit transaction
    $db->commit();
    
    // Return success response
    sendResponse(true, "Webhook processed successfully");
} catch (PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    
    logMessage("Database error: " . $e->getMessage());
    sendResponse(false, 'Database error', 500);
}