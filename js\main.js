// Configurações de notificações
const notifications = {
    names: [
        "<PERSON> completou a compra do Curso Completo: Monte Seu Próprio Site!",
        // ... (manter a lista existente)
    ],
    usersOnline: [
        "<PERSON> está online agora!",
        // ... (manter a lista existente)
    ],
    index: 0,
    onlineIndex: 0,
    toggle: true,
    element: document.getElementById("notification"),

    show() {
        this.element.textContent = this.toggle ? this.names[this.index] : this.usersOnline[this.onlineIndex];
        this.element.classList.add(this.toggle ? "purchase" : "online");
        this.element.classList.add("active");

        setTimeout(() => {
            this.element.classList.remove("active");
            this.element.classList.remove(this.toggle ? "purchase" : "online");
        }, 5000);

        if (this.toggle) {
            this.index = (this.index + 1) % this.names.length;
        } else {
            this.onlineIndex = (this.onlineIndex + 1) % this.usersOnline.length;
        }
        this.toggle = !this.toggle;
    }
};

// Gerenciador de Modal de Imagem
const imageModal = {
    modal: document.getElementById('imageModal'),
    image: document.getElementById('modalImage'),

    init() {
        // Adiciona eventos aos cards de imagem
        document.querySelectorAll('.card img').forEach(img => {
            img.addEventListener('click', () => this.open(img.src));
        });

        // Fecha modal ao clicar fora
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.close();
        });

        // Fecha modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.style.display === 'flex') this.close();
        });
    },

    open(src) {
        this.image.src = src;
        this.modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    },

    close() {
        this.modal.style.display = 'none';
        document.body.style.overflow = '';
    }
};

// Gerenciador de Cookies
const cookieManager = {
    banner: document.getElementById("cookieBanner"),

    init() {
        if (localStorage.getItem("cookiesAccepted") !== null) {
            this.banner.style.display = "none";
            return;
        }

        document.getElementById("accept-cookies").addEventListener("click", () => this.accept());
        document.getElementById("decline-cookies").addEventListener("click", () => this.decline());
    },

    accept() {
        localStorage.setItem("cookiesAccepted", "true");
        this.banner.style.display = "none";
    },

    decline() {
        localStorage.setItem("cookiesAccepted", "false");
        this.banner.style.display = "none";
    }
};

// Gerenciador de Pagamento PIX
const pixPayment = {
    interval: null,
    currentId: null,

    show(amount, qrCodeImage, pixCode, paymentId) {
        this.currentId = paymentId;
        
        // Reset modal state
        document.getElementById('payment-pending').style.display = 'block';
        document.getElementById('payment-success').style.display = 'none';
        
        // Set payment info
        document.getElementById('qr-code').src = qrCodeImage;
        document.getElementById('pix-code').value = pixCode;
        
        // Show modal and start checking
        const pixModal = new bootstrap.Modal(document.getElementById('pixModal'));
        pixModal.show();
        this.startChecking(paymentId);
        
        // Clear interval when modal closes
        document.getElementById('pixModal').addEventListener('hidden.bs.modal', () => {
            clearInterval(this.interval);
        });
    },

    async checkStatus(paymentId) {
        try {
            const response = await fetch(`check_payment.php?payment_id=${paymentId}`);
            const data = await response.json();
            
            if (data.success && data.approved) {
                document.getElementById('payment-pending').style.display = 'none';
                document.getElementById('payment-success').style.display = 'block';
                clearInterval(this.interval);
                
                const audio = new Audio('assets/success.mp3');
                audio.play().catch(() => {});
            }
        } catch (error) {
            console.error('Erro ao verificar status:', error);
        }
    },

    startChecking(paymentId) {
        if (this.interval) clearInterval(this.interval);
        this.checkStatus(paymentId);
        this.interval = setInterval(() => this.checkStatus(paymentId), 5000);
    },

    copyCode() {
        const pixCode = document.getElementById('pix-code');
        pixCode.select();
        document.execCommand('copy');
        
        Swal.fire({
            icon: 'success',
            title: 'Código Copiado!',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000
        });
    }
};

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
    // Inicia notificações
    setInterval(() => notifications.show(), 8000);

    // Inicia modal de imagem
    imageModal.init();

    // Inicia gerenciador de cookies
    cookieManager.init();

    // Configura chat
    document.getElementById('openChat')?.addEventListener('click', () => {
        window.open('chat.php', '_blank', 'width=400,height=600');
    });

    // Configura links de política de privacidade
    document.querySelectorAll('a[onclick*="openPrivacyPolicy"]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const privacyModal = new bootstrap.Modal(document.getElementById('privacyPolicyModal'));
            privacyModal.show();
        });
    });
});
