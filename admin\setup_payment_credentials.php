<?php
require_once 'database/connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Criar tabela se não existir
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS payment_credentials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            api_key VARCHAR(255) NOT NULL,
            environment VARCHAR(20) NOT NULL DEFAULT 'sandbox',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Verifica se já existem credenciais
    $stmt = $pdo->query("SELECT COUNT(*) FROM payment_credentials");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insere credenciais de teste do Asaas
        $stmt = $pdo->prepare("
            INSERT INTO payment_credentials (api_key, environment)
            VALUES (?, ?)
        ");
        
        $stmt->execute([
            '$aact_YWM0MDRjM2M2MGFkZGVkYWFmZDI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDo6VEVTVEU=',  // API Key de teste
            'sandbox'  // Ambiente
        ]);
        
        echo "Credenciais de teste inseridas com sucesso!\n";
    } else {
        echo "Credenciais já existem na tabela.\n";
    }
    
} catch (PDOException $e) {
    die("Erro ao configurar credenciais: " . $e->getMessage());
}

