:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --background-color: #f1f5f9;
    --border-color: #e2e8f0;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
    min-height: 100vh;
}

/* Melhorias gerais */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    color: #333;
    user-select: none; /* Impede a seleção de texto */
}

/* Header */
.header {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-hover));
    color: white;
    text-align: center;
    padding: 25px 15px;
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    box-shadow: var(--shadow);
}

/* Container e Cards */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px 10px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    justify-content: center;
    align-items: start;
}

.cards-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    justify-content: center;
    padding: 0 10px;
}

.card {
    background: linear-gradient(135deg, #ffffff, var(--background-color));
    border-radius: 12px;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 300px;
    min-width: 250px;
    text-align: center;
    padding: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    height: fit-content;
}

.card:hover {
    transform: scale(1.03);
    box-shadow: var(--shadow-lg);
}

.card img {
    width: 100%;
    max-width: 280px;
    height: auto;
    border-radius: 12px;
    margin-bottom: 20px;
    transition: transform 0.3s;
    cursor: pointer;
}

.card img:hover {
    transform: scale(1.05);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    width: 100%;
}

.card-description {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 12px;
    line-height: 1.4;
    width: 100%;
}

.card-price {
    font-size: 1.3rem;
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 15px;
    width: 100%;
}

.buy-button {
    display: inline-block;
    background: linear-gradient(to right, var(--success-color), var(--primary-color));
    color: white;
    padding: 10px 20px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 100%;
}

.buy-button:hover {
    background: linear-gradient(to right, var(--success-color), var(--primary-hover));
    box-shadow: var(--shadow);
    transform: translateY(-3px);
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 25px;
    margin-top: 30px;
    font-size: 16px;
    letter-spacing: 0.5px;
    box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.2);
}

/* Pop-up ajustes */
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #ffffff;
    border: 1px solid #ccc;
    padding: 25px;
    z-index: 1000;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
}

.popup button {
    margin: 5px;
    padding: 10px 20px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
}

.popup .confirm {
    background-color: #4CAF50;
    color: white;
    transition: background-color 0.3s;
}

.popup .confirm:hover {
    background-color: #43A047;
}

.popup .cancel {
    background-color: #dc3545;
    color: white;
    transition: background-color 0.3s;
}

.popup .cancel:hover {
    background-color: #c82333;
}

/* Notificações */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 25px;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.notification.active {
    opacity: 1;
    transform: translateY(0);
}

.notification.purchase {
    background: linear-gradient(to right, #007BFF, #0056b3);
}

.notification.online {
    background: linear-gradient(to right, #43A047, #388E3C);
}

/* Notification Styles */
.alert {
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.alert-success {
    color: #155724;
    background-color: #fff;
}

.alert-error, .alert-danger {
    color: #721c24;
    background-color: #fff;
}

.alert-warning {
    color: #856404;
    background-color: #fff;
}

.alert-info {
    color: #0c5460;
    background-color: #fff;
}

.alert .notification-message {
    font-size: 0.95rem;
    line-height: 1.4;
}

.alert i {
    font-size: 1.2rem;
}

/* Animation for notifications */
.alert.fade {
    opacity: 0;
    transition: opacity 0.15s linear;
}

.alert.fade.show {
    opacity: 1;
}

/* Auto-dismiss animation */
@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.alert.dismissing {
    animation: slideOut 0.3s ease forwards;
}

/* Responsividade */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
    }

    .card {
        max-width: 100%;
    }

    .header {
        font-size: 24px;
        padding: 20px 10px;
    }
}
    
    .card {
        padding: 12px;
        min-width: 200px;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    .card-description {
        font-size: 0.85rem;
    }
    
    .card-price {
        font-size: 1.2rem;
    }
    
    .buy-button {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container {
        grid-template-columns: 1fr;
        padding: 10px 5px;
    }
    
    .card {
        max-width: 100%;
    }
}

/* Estilos do Modal de Imagem */
.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
}

.image-modal .modal-content {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 30px auto;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    min-height: 200px;
    border: 10px solid #1a237e;
}

.modal-header {
    position: relative;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

#customerForm {
    width: 90%;
    margin: 0 auto;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 15px;
    box-sizing: border-box;
}

.mb-3 {
    margin-bottom: 15px;
    width: 100%;
}

.terms-content {
    width: 90%;
    margin: 20px auto 0;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #fff;
}

.modal img {
    width: 100%;
    height: auto;
    display: block;
}

.modal-body {
    padding: 20px;
    max-height: calc(100vh - 210px);
    overflow-y: auto;
}

.form-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
}

.terms-content {
    margin-top: 20px;
}

.terms-content h6 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    text-align: center;
}

.terms-content p {
    margin-bottom: 10px;
    font-size: 14px;
    color: #555;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #fff;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
}

.btn-close {
    opacity: 0.5;
    padding: 0;
    margin: -8px -8px -8px auto;
}

.image-modal img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: calc(100vh - 200px);
    object-fit: contain;
    display: block;
    margin: auto;
    user-select: none;
}

.image-modal .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    color: #666;
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 0;
    margin: 0;
    opacity: 0.7;
    z-index: 1;
}

.image-modal .close-btn:hover {
    opacity: 1;
    color: #333;
}

#imageModal .modal-dialog {
    max-width: 90%;
    max-height: 85vh;
    margin: 10px auto;
}

#imageModal .modal-content {
    height: 85vh;
}

#imageModal .modal-body {
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(85vh - 60px);
    position: relative;
}

#imageModal img {
    max-width: 100%;
    max-height: calc(100% - 40px);
    object-fit: contain;
    display: block;
}

#imageModal .modal-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 10px;
    background: transparent;
    border: none;
}

#imageModal .text-muted {
    font-size: 12px;
    text-align: center;
}

/* Cookie Política de privacidade */
.cookie-banner {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background: #222;
    color: #fff;
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.cookie-banner p {
    margin: 0 0 10px;
    font-size: 14px;
    line-height: 1.5;
}

.cookie-banner a {
    color: #4caf50;
    text-decoration: underline;
}

.cookie-banner button {
    margin: 5px;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    background-color: #4caf50;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

.cookie-banner button:hover {
    background-color: #45a049;
}

.cookie-banner button#decline-cookies {
    background-color: #f44336;
}

.cookie-banner button#decline-cookies:hover {
    background-color: #d32f2f;
}

/* Bootstrap Modal Styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

.modal.fade {
    display: none;
}

.modal.fade.show {
    display: block;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
    margin-top: auto;
    margin-bottom: auto;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    outline: 0;
    height: auto;
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #fff;
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.modal-body {
    padding: 20px 30px;
    overflow: visible;
}

.form-label {
    font-weight: 500;
    color: #333;
    display: inline-block;
    width: 110px;
    margin-bottom: 0;
    vertical-align: middle;
}

.form-control {
    display: inline-block;
    width: calc(100% - 120px);
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    vertical-align: middle;
}

.mb-3 {
    margin-bottom: 15px;
}

.terms-content {
    margin-top: 20px;
    padding: 0;
}

.terms-content h6 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    text-align: center;
}

.terms-content p {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
}

/* Responsivo */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .form-label {
        display: block;
        width: 100%;
        margin-bottom: 5px;
    }
    
    .form-control {
        width: 100%;
    }
}

.modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
}

.modal-content {
    border-radius: 8px;
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 15px;
}

.mb-3 {
    margin-bottom: 15px;
}

.terms-content {
    margin-top: 20px;
}

.terms-content h6 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
}
