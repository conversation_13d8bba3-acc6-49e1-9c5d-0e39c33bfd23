/**
 * PWA - Progressive Web App
 * Sistema de notificações e instalação do app
 */

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.notificationPermission = 'default';
        this.init();
    }

    async init() {
        // Registrar Service Worker
        await this.registerServiceWorker();

        // Configurar eventos de instalação
        this.setupInstallPrompt();

        // Verificar se já está instalado
        this.checkIfInstalled();

        // NÃO solicitar permissão automaticamente - apenas quando usuário clicar
        // await this.requestNotificationPermission();

        // Mostrar banner de instalação se necessário
        this.showInstallBanner();
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registrado:', registration);
                
                // Verificar atualizações
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
                return registration;
            } catch (error) {
                console.error('Erro ao registrar Service Worker:', error);
            }
        }
    }

    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Prompt de instalação disponível');
            e.preventDefault();
            this.deferredPrompt = e;
            // Não mostrar botão - usando banner compacto
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA: App instalado');
            this.isInstalled = true;
            this.hideInstallButton();
            this.trackAppInstall();
            this.showWelcomeMessage();
        });
    }

    checkIfInstalled() {
        // Verificar se está rodando como PWA
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('PWA: App rodando como standalone');
        }
    }

    async requestNotificationPermission() {
        if ('Notification' in window) {
            // Verificar se já está bloqueado
            if (Notification.permission === 'denied') {
                this.showUnblockInstructions();
                return;
            }

            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            console.log('Permissão de notificação:', permission);

            if (permission === 'granted') {
                this.showNotificationWelcome();
            } else if (permission === 'denied') {
                this.showUnblockInstructions();
            }
        }
    }

    async setupPushNotifications() {
        if ('serviceWorker' in navigator && 'PushManager' in window && this.notificationPermission === 'granted') {
            try {
                const registration = await navigator.serviceWorker.ready;

                // Verificar se já tem subscription
                let subscription = await registration.pushManager.getSubscription();

                if (!subscription) {
                    // Criar nova subscription sem VAPID (modo simplificado)
                    subscription = await registration.pushManager.subscribe({
                        userVisibleOnly: true
                    });
                }

                // Enviar subscription para o servidor
                await this.sendSubscriptionToServer(subscription);
                console.log('Push notifications configuradas com sucesso');

            } catch (error) {
                console.error('Erro ao configurar push notifications:', error);
                // Não quebrar o PWA se push notifications falharem
                console.log('PWA continuará funcionando sem notificações push');
            }
        } else {
            console.log('Push notifications não disponíveis ou permissão negada');
        }
    }

    getVapidPublicKey() {
        // Chave pública VAPID válida para KESUNG SITE
        // Esta é uma chave de teste válida - substitua por sua própria chave em produção
        return 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxazjqAKVXTdtkoTrsMuIiTqn_PjQUaq9k7a0ey65fac7cWzSsTw';
    }

    urlBase64ToUint8Array(base64String) {
        try {
            // Remover caracteres inválidos e adicionar padding se necessário
            const cleanBase64 = base64String.replace(/[^A-Za-z0-9+/\-_]/g, '');
            const padding = '='.repeat((4 - cleanBase64.length % 4) % 4);
            const base64 = (cleanBase64 + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }

            // Verificar se tem o tamanho correto (65 bytes para chave P-256)
            if (outputArray.length !== 65) {
                throw new Error(`Chave VAPID deve ter 65 bytes, mas tem ${outputArray.length} bytes`);
            }

            return outputArray;
        } catch (error) {
            console.error('Erro ao converter chave VAPID:', error);
            throw error;
        }
    }

    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/app_installs.php?action=push-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    subscription: subscription,
                    user_agent: navigator.userAgent,
                    timestamp: Date.now()
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Subscription enviada para o servidor:', result.message);
            } else {
                console.error('Erro ao enviar subscription:', response.status);
            }
        } catch (error) {
            console.error('Erro ao enviar subscription:', error);
        }
    }

    showInstallButton() {
        // Botão de instalação desabilitado - usando banner compacto no index.php
        console.log('PWA: Install button disabled - using compact banner instead');
        return;
    }

    hideInstallButton() {
        const installBtn = document.getElementById('pwa-install-btn');
        if (installBtn) {
            installBtn.remove();
        }
    }

    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log('Resultado da instalação:', outcome);
            this.deferredPrompt = null;
        }
    }

    showInstallBanner() {
        // Banner agora é controlado pelo index.php
        // Não criar banner automático aqui para evitar conflitos
        console.log('PWA: Banner management delegated to main page');
    }

    createInstallBanner() {
        const banner = document.createElement('div');
        banner.id = 'pwa-banner';
        banner.className = 'pwa-install-banner';
        banner.innerHTML = `
            <div class="pwa-banner-content">
                <div class="pwa-banner-icon">
                    📱
                </div>
                <div class="pwa-banner-text">
                    <span>Instale nosso App!</span>
                    <small>Acesso rápido e notificações de promoções</small>
                </div>
                <div class="pwa-banner-actions">
                    <button onclick="pwaManager.installApp()" class="pwa-install-btn">
                        Instalar
                    </button>
                    <button onclick="pwaManager.dismissBanner()" class="pwa-dismiss-btn">
                        Agora não
                    </button>
                </div>
            </div>
        `;

        document.body.insertBefore(banner, document.body.firstChild);

        // Mostrar banner com animação
        setTimeout(() => {
            banner.classList.add('show');
        }, 100);
    }

    dismissBanner() {
        const banner = document.getElementById('pwa-banner');
        if (banner) {
            banner.classList.remove('show');
            setTimeout(() => banner.remove(), 300);
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    showUpdateAvailable() {
        if ('Notification' in window && this.notificationPermission === 'granted') {
            new Notification('Atualização Disponível', {
                body: 'Uma nova versão do app está disponível!',
                icon: '/assets/icons/icon-192x192.png',
                tag: 'update-available'
            });
        }
    }

    showNotificationWelcome() {
        if ('Notification' in window && this.notificationPermission === 'granted') {
            new Notification('Notificações Ativadas!', {
                body: 'Você receberá alertas de promoções e mensagens',
                icon: '/assets/icons/icon-192x192.png',
                tag: 'welcome-notification'
            });
        }
    }

    showUnblockInstructions() {
        // Criar modal com instruções para desbloquear notificações
        const modal = document.createElement('div');
        modal.className = 'notification-unblock-modal';
        modal.innerHTML = `
            <div class="unblock-modal-content">
                <div class="unblock-header">
                    <i class="fas fa-bell-slash unblock-icon"></i>
                    <h3>Notificações Bloqueadas</h3>
                    <button class="unblock-close" onclick="this.closest('.notification-unblock-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="unblock-body">
                    <p>Para receber notificações de promoções e ofertas especiais, você precisa desbloquear as notificações:</p>

                    <div class="unblock-steps">
                        <div class="unblock-step">
                            <span class="step-number">1</span>
                            <div class="step-content">
                                <strong>Clique no ícone de cadeado</strong> na barra de endereços
                            </div>
                        </div>

                        <div class="unblock-step">
                            <span class="step-number">2</span>
                            <div class="step-content">
                                <strong>Encontre "Notificações"</strong> na lista de permissões
                            </div>
                        </div>

                        <div class="unblock-step">
                            <span class="step-number">3</span>
                            <div class="step-content">
                                <strong>Altere para "Permitir"</strong> e recarregue a página
                            </div>
                        </div>
                    </div>

                    <div class="unblock-visual">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDIwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjZjhmOWZhIiByeD0iOCIvPgo8cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSIxODAiIGhlaWdodD0iMzAiIGZpbGw9IndoaXRlIiByeD0iNCIvPgo8Y2lyY2xlIGN4PSIyNSIgY3k9IjI1IiByPSI4IiBmaWxsPSIjZmY2MzQ3Ii8+Cjx0ZXh0IHg9IjQwIiB5PSIzMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjMzMzIj5Ob3RpZmljYcOnw7VlczogQmxvcXVlYWRhczwvdGV4dD4KPHN2ZyB4PSIxNTAiIHk9IjE1IiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiPgo8cGF0aCBkPSJNMTAgNUw1IDEwTDE1IDEwWiIgZmlsbD0iIzMzMyIvPgo8L3N2Zz4KPHRleHQgeD0iMjAiIHk9IjY1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM2NjYiPkNsaXF1ZSBubyBjYWRlYWRvIGUgYWx0ZXJlIHBhcmEgIlBlcm1pdGlyIjwvdGV4dD4KPC9zdmc+" alt="Como desbloquear notificações" class="unblock-image">
                    </div>
                </div>
                <div class="unblock-footer">
                    <button class="unblock-btn-secondary" onclick="this.closest('.notification-unblock-modal').remove()">
                        Agora Não
                    </button>
                    <button class="unblock-btn-primary" onclick="this.tryAgain()">
                        <i class="fas fa-sync"></i>
                        Tentar Novamente
                    </button>
                </div>
            </div>
        `;

        // Adicionar evento para tentar novamente
        modal.querySelector('.unblock-btn-primary').onclick = () => {
            modal.remove();
            this.requestNotificationPermission();
        };

        document.body.appendChild(modal);

        // Mostrar modal com animação
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    }

    showWelcomeMessage() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'App Instalado!',
                text: 'Agora você pode acessar rapidamente e receber notificações de promoções!',
                icon: 'success',
                confirmButtonText: 'Ótimo!',
                timer: 5000
            });
        }
    }

    async trackAppInstall() {
        try {
            await fetch('/api/track-install', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: 'app_installed',
                    timestamp: Date.now(),
                    user_agent: navigator.userAgent
                })
            });
        } catch (error) {
            console.error('Erro ao rastrear instalação:', error);
        }
    }

    // Método para enviar notificação de teste
    async sendTestNotification() {
        if ('serviceWorker' in navigator && this.notificationPermission === 'granted') {
            const registration = await navigator.serviceWorker.ready;
            registration.showNotification('Teste de Notificação', {
                body: 'Esta é uma notificação de teste do KESUNG SITE!',
                icon: '/assets/icons/icon-192x192.png',
                vibrate: [200, 100, 200],
                tag: 'test-notification'
            });
        }
    }
}

// CSS para PWA
const pwaStyles = `
<style>
/* Botão de instalação removido - usando banner compacto */

/* Modal de Desbloqueio de Notificações */
.notification-unblock-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-unblock-modal.show {
    opacity: 1;
}

.unblock-modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.notification-unblock-modal.show .unblock-modal-content {
    transform: scale(1);
}

.unblock-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    text-align: center;
    position: relative;
}

.unblock-icon {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.unblock-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.unblock-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.unblock-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.unblock-body {
    padding: 25px;
}

.unblock-body p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

.unblock-steps {
    margin: 20px 0;
}

.unblock-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #4318FF;
}

.step-number {
    background: #4318FF;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content strong {
    color: #333;
    display: block;
    margin-bottom: 5px;
}

.unblock-visual {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.unblock-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.unblock-footer {
    padding: 20px 25px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.unblock-btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.unblock-btn-secondary:hover {
    background: #5a6268;
}

.unblock-btn-primary {
    background: #4318FF;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.unblock-btn-primary:hover {
    background: #3612d4;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .unblock-modal-content {
        width: 95%;
        margin: 10px;
    }

    .unblock-header {
        padding: 15px;
    }

    .unblock-body {
        padding: 20px;
    }

    .unblock-footer {
        flex-direction: column;
        gap: 10px;
    }

    .unblock-btn-secondary,
    .unblock-btn-primary {
        width: 100%;
        justify-content: center;
    }
}

.pwa-install-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #4318FF, #9333EA);
    color: white;
    z-index: 1001;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    font-size: 14px;
}

.pwa-install-banner.show {
    transform: translateY(0);
}

.pwa-banner-content {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    gap: 10px;
    max-width: 1200px;
    margin: 0 auto;
}

.pwa-banner-icon {
    font-size: 18px;
}

.pwa-banner-text {
    flex: 1;
}

.pwa-banner-text span {
    font-weight: 600;
    margin-right: 8px;
}

.pwa-banner-text small {
    opacity: 0.9;
}

.pwa-banner-actions {
    display: flex;
    gap: 8px;
}

.pwa-install-btn, .pwa-dismiss-btn {
    padding: 4px 12px;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pwa-install-btn:hover {
    background: rgba(255,255,255,0.2);
}

.pwa-dismiss-btn:hover {
    background: rgba(255,255,255,0.1);
    opacity: 0.8;
}

/* Animação pulse removida */

@media (max-width: 768px) {
    .pwa-banner-content {
        padding: 6px 10px;
        gap: 8px;
    }

    .pwa-banner-text span {
        font-size: 13px;
    }

    .pwa-banner-text small {
        display: none;
    }

    .pwa-install-btn, .pwa-dismiss-btn {
        padding: 3px 8px;
        font-size: 11px;
    }
}
</style>
`;

// Adicionar estilos ao head
document.head.insertAdjacentHTML('beforeend', pwaStyles);

// Inicializar PWA Manager
const pwaManager = new PWAManager();

// Exportar para uso global
window.pwaManager = pwaManager;
