<?php
$showChat = isset($hideChat) ? !$hideChat : true;
?>

<!-- Footer -->
<footer class="main-footer">
    <div class="footer-content">
        <p>&copy; <?php echo date('Y'); ?> Sistema de Vendas. Todos os direitos reservados.</p>
    </div>

    <?php if ($showChat): ?>
    <!-- Chat Widget -->
    <div class="chat-widget">
        <div class="chat-widget-toggle">
            <button id="chat-toggle" class="chat-toggle-btn">
                <i class="fas fa-comments"></i>
                <span class="chat-label">Chat</span>
            </button>
        </div>
        
        <div id="chat-popup" class="chat-popup">
            <div class="chat-header">
                <h3>Chat Online</h3>
                <button id="chat-minimize" class="chat-minimize-btn">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
            <div class="chat-messages" id="chat-messages"></div>
            <div class="chat-input-area">
                <input type="text" id="message-input" placeholder="Digite sua mensagem...">
                <button id="send-message" class="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>
</footer>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatToggle = document.getElementById('chat-toggle');
    const chatPopup = document.getElementById('chat-popup');
    const chatMinimize = document.getElementById('chat-minimize');
    const messageInput = document.getElementById('message-input');
    const sendMessage = document.getElementById('send-message');

    // Toggle chat popup
    chatToggle.addEventListener('click', () => {
        chatPopup.classList.toggle('active');
        chatToggle.classList.toggle('active');
    });

    // Minimize chat
    chatMinimize.addEventListener('click', () => {
        chatPopup.classList.remove('active');
        chatToggle.classList.remove('active');
    });

    // Send message on Enter
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessageHandler();
        }
    });

    // Send message on button click
    sendMessage.addEventListener('click', sendMessageHandler);

    function sendMessageHandler() {
        const message = messageInput.value.trim();
        if (!message) return;

        // Add message to chat
        const messagesContainer = document.getElementById('chat-messages');
        const messageElement = document.createElement('div');
        messageElement.className = 'message user-message';
        messageElement.textContent = message;
        messagesContainer.appendChild(messageElement);
        
        // Clear input
        messageInput.value = '';
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});
</script>
