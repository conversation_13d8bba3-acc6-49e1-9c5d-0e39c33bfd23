<?php
/**
 * API para buscar dados do produto - Sistema kesung-site
 * Retorna informações do produto em formato JSON
 */

error_reporting(E_ALL);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

require_once 'database/connection.php';

function sendResponse($success, $message, $data = null, $httpCode = 200) {
    http_response_code($httpCode);
    
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('c')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // Obter ID do produto
    $productId = $_GET['id'] ?? $_POST['id'] ?? null;
    
    if (empty($productId)) {
        sendResponse(false, 'ID do produto é obrigatório', null, 400);
    }
    
    // Validar se é um número
    if (!is_numeric($productId)) {
        sendResponse(false, 'ID do produto deve ser um número', null, 400);
    }
    
    // Conectar ao banco
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar produto
    $stmt = $pdo->prepare("
        SELECT 
            id,
            name,
            description,
            price,
            image,
            status,
            created_at
        FROM products 
        WHERE id = ? AND status = 'active'
    ");
    
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        sendResponse(false, 'Produto não encontrado ou inativo', null, 404);
    }
    
    // Formatar dados do produto
    $productData = [
        'id' => (int)$product['id'],
        'name' => $product['name'],
        'description' => $product['description'] ?? '',
        'price' => (float)$product['price'],
        'price_formatted' => 'R$ ' . number_format($product['price'], 2, ',', '.'),
        'image' => $product['image'],
        'status' => $product['status'],
        'created_at' => $product['created_at']
    ];
    
    // Adicionar URL da imagem se existir
    if (!empty($product['image'])) {
        $productData['image_url'] = 'uploads/products/' . $product['image'];
    } else {
        $productData['image_url'] = null;
    }
    
    // Log da consulta
    error_log("Produto consultado: ID {$productId} - {$product['name']}");
    
    sendResponse(true, 'Produto encontrado', $productData);
    
} catch (PDOException $e) {
    error_log("Erro de banco ao buscar produto: " . $e->getMessage());
    sendResponse(false, 'Erro interno do servidor', null, 500);
} catch (Exception $e) {
    error_log("Erro geral ao buscar produto: " . $e->getMessage());
    sendResponse(false, 'Erro interno do servidor', null, 500);
}
?>
