<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../database/connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($method) {
        case 'POST':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'track-install':
                        trackAppInstall($pdo, $input);
                        break;
                    case 'push-subscription':
                        savePushSubscription($pdo, $input);
                        break;
                    case 'send-notification':
                        sendPushNotification($pdo, $input);
                        break;
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Ação inválida']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Ação não especificada']);
            }
            break;
            
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'list-installs':
                        listAppInstalls($pdo);
                        break;
                    case 'list-subscriptions':
                        listPushSubscriptions($pdo);
                        break;
                    case 'stats':
                        getInstallStats($pdo);
                        break;
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Ação inválida']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Ação não especificada']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Método não permitido']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function trackAppInstall($pdo, $data) {
    try {
        // Verificar se a tabela app_installs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'app_installs'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('Tabela app_installs não existe. Execute o setup do banco de dados primeiro.');
        }

        // Obter IP do usuário
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        $stmt = $pdo->prepare("
            INSERT INTO app_installs (ip_address, user_agent, install_date, platform, device_info)
            VALUES (?, ?, NOW(), ?, ?)
        ");
        
        $platform = detectPlatform($data['user_agent'] ?? '');
        $deviceInfo = json_encode([
            'user_agent' => $data['user_agent'] ?? '',
            'timestamp' => $data['timestamp'] ?? time(),
            'screen_width' => $data['screen_width'] ?? null,
            'screen_height' => $data['screen_height'] ?? null
        ]);
        
        $stmt->execute([
            $ip,
            $data['user_agent'] ?? '',
            $platform,
            $deviceInfo
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Instalação rastreada com sucesso',
            'install_id' => $pdo->lastInsertId()
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao rastrear instalação: ' . $e->getMessage());
    }
}

function savePushSubscription($pdo, $data) {
    try {
        // Verificar se a tabela push_subscriptions existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'push_subscriptions'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('Tabela push_subscriptions não existe. Execute o setup do banco de dados primeiro.');
        }

        $subscription = $data['subscription'];
        $endpoint = $subscription['endpoint'];
        $keys = json_encode($subscription['keys']);

        // Obter IP do usuário
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // Verificar se já existe
        $stmt = $pdo->prepare("SELECT id FROM push_subscriptions WHERE endpoint = ?");
        $stmt->execute([$endpoint]);
        
        if ($stmt->fetch()) {
            // Atualizar existente
            $stmt = $pdo->prepare("
                UPDATE push_subscriptions 
                SET keys_json = ?, last_updated = NOW(), is_active = 1
                WHERE endpoint = ?
            ");
            $stmt->execute([$keys, $endpoint]);
            $message = 'Subscription atualizada';
        } else {
            // Criar nova
            $stmt = $pdo->prepare("
                INSERT INTO push_subscriptions (endpoint, keys_json, ip_address, user_agent, created_at, is_active)
                VALUES (?, ?, ?, ?, NOW(), 1)
            ");
            $stmt->execute([
                $endpoint,
                $keys,
                $ip,
                $data['user_agent'] ?? ''
            ]);
            $message = 'Subscription criada';
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao salvar subscription: ' . $e->getMessage());
    }
}

function sendPushNotification($pdo, $data) {
    try {
        $title = $data['title'] ?? 'KESUNG SITE';
        $body = $data['body'] ?? 'Nova mensagem!';
        $url = $data['url'] ?? null; // URL opcional
        $image = $data['image'] ?? null;
        $type = $data['type'] ?? 'default'; // Tipo de notificação para som

        // Verificar se a tabela push_subscriptions existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'push_subscriptions'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('Tabela push_subscriptions não existe. Execute o setup do banco de dados primeiro.');
        }

        // Buscar todas as subscriptions ativas
        $stmt = $pdo->query("SELECT * FROM push_subscriptions WHERE is_active = 1");
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $sent = 0;
        $failed = 0;
        
        foreach ($subscriptions as $subscription) {
            $payload = [
                'title' => $title,
                'body' => $body,
                'type' => $type,
                'tag' => 'kesung-' . time()
            ];

            // Adicionar URL apenas se fornecida
            if ($url && $url !== '/') {
                $payload['url'] = $url;
            }

            // Adicionar imagem se fornecida
            if ($image) {
                $payload['image'] = $image;
            }

            $result = sendNotificationToSubscription(
                $subscription['endpoint'],
                json_decode($subscription['keys_json'], true),
                $payload
            );
            
            if ($result) {
                $sent++;
            } else {
                $failed++;
                // Marcar subscription como inativa se falhou
                $updateStmt = $pdo->prepare("UPDATE push_subscriptions SET is_active = 0 WHERE id = ?");
                $updateStmt->execute([$subscription['id']]);
            }
        }
        
        // Salvar log da notificação (se tabela existir)
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE 'notification_logs'");
            if ($stmt->rowCount() > 0) {
                $logStmt = $pdo->prepare("
                    INSERT INTO notification_logs (title, body, url, sent_count, failed_count, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $logStmt->execute([$title, $body, $url, $sent, $failed]);
            }
        } catch (Exception $e) {
            // Ignorar erro de log se tabela não existir
            error_log("Erro ao salvar log de notificação: " . $e->getMessage());
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Notificações enviadas',
            'sent' => $sent,
            'failed' => $failed
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao enviar notificações: ' . $e->getMessage());
    }
}

function sendNotificationToSubscription($endpoint, $keys, $payload) {
    // Implementação simplificada - em produção usar biblioteca como web-push
    try {
        $headers = [
            'Content-Type: application/json',
            'TTL: 86400'
        ];
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => implode("\r\n", $headers),
                'content' => json_encode($payload)
            ]
        ]);
        
        $result = @file_get_contents($endpoint, false, $context);
        return $result !== false;
        
    } catch (Exception $e) {
        return false;
    }
}

function listAppInstalls($pdo) {
    try {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $offset = ($page - 1) * $limit;
        
        $stmt = $pdo->prepare("
            SELECT * FROM app_installs 
            ORDER BY install_date DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        $installs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Contar total
        $countStmt = $pdo->query("SELECT COUNT(*) FROM app_installs");
        $total = $countStmt->fetchColumn();
        
        echo json_encode([
            'success' => true,
            'data' => $installs,
            'pagination' => [
                'page' => (int)$page,
                'limit' => (int)$limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao listar instalações: ' . $e->getMessage());
    }
}

function listPushSubscriptions($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT id, ip_address, user_agent, created_at, last_updated, is_active
            FROM push_subscriptions 
            ORDER BY created_at DESC
        ");
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $subscriptions
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao listar subscriptions: ' . $e->getMessage());
    }
}

function getInstallStats($pdo) {
    try {
        // Total de instalações
        $totalStmt = $pdo->query("SELECT COUNT(*) FROM app_installs");
        $total = $totalStmt->fetchColumn();
        
        // Instalações hoje
        $todayStmt = $pdo->query("SELECT COUNT(*) FROM app_installs WHERE DATE(install_date) = CURDATE()");
        $today = $todayStmt->fetchColumn();
        
        // Instalações esta semana
        $weekStmt = $pdo->query("SELECT COUNT(*) FROM app_installs WHERE install_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $week = $weekStmt->fetchColumn();
        
        // Por plataforma
        $platformStmt = $pdo->query("
            SELECT platform, COUNT(*) as count 
            FROM app_installs 
            GROUP BY platform 
            ORDER BY count DESC
        ");
        $platforms = $platformStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Subscriptions ativas
        $activeSubsStmt = $pdo->query("SELECT COUNT(*) FROM push_subscriptions WHERE is_active = 1");
        $activeSubs = $activeSubsStmt->fetchColumn();
        
        echo json_encode([
            'success' => true,
            'stats' => [
                'total_installs' => (int)$total,
                'installs_today' => (int)$today,
                'installs_week' => (int)$week,
                'active_subscriptions' => (int)$activeSubs,
                'platforms' => $platforms
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Erro ao obter estatísticas: ' . $e->getMessage());
    }
}

function detectPlatform($userAgent) {
    if (stripos($userAgent, 'android') !== false) {
        return 'Android';
    } elseif (stripos($userAgent, 'iphone') !== false || stripos($userAgent, 'ipad') !== false) {
        return 'iOS';
    } elseif (stripos($userAgent, 'windows') !== false) {
        return 'Windows';
    } elseif (stripos($userAgent, 'mac') !== false) {
        return 'macOS';
    } elseif (stripos($userAgent, 'linux') !== false) {
        return 'Linux';
    } else {
        return 'Unknown';
    }
}
?>
