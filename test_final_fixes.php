<?php
require_once 'database/connection.php';

echo "<h1>🧪 Teste Final das Correções</h1>";

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>1. 💰 Teste do Sistema de Pagamento</h2>";
    
    // Verificar se as colunas necessárias existem na tabela orders
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['customer_phone', 'expires_at', 'pix_qr_code', 'pix_code'];
    $missingColumns = array_diff($requiredColumns, $columnNames);
    
    if (empty($missingColumns)) {
        echo "<p>✅ Todas as colunas necessárias existem na tabela orders</p>";
    } else {
        echo "<p>❌ Colunas faltantes: " . implode(', ', $missingColumns) . "</p>";
    }
    
    // Verificar tabela customers
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("DESCRIBE customers");
        $customerColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $customerColumnNames = array_column($customerColumns, 'Field');
        
        if (in_array('whatsapp', $customerColumnNames)) {
            echo "<p>✅ Tabela customers tem coluna 'whatsapp'</p>";
        } else {
            echo "<p>❌ Tabela customers não tem coluna 'whatsapp'</p>";
        }
    } else {
        echo "<p>❌ Tabela customers não existe</p>";
    }
    
    echo "<h2>2. 💬 Teste do Sistema de Chat</h2>";
    
    // Verificar tabelas de chat
    $chatTables = ['chat_users', 'chat_messages'];
    foreach ($chatTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>✅ Tabela <strong>$table</strong>: $count registros</p>";
        } else {
            echo "<p>❌ Tabela <strong>$table</strong> não existe</p>";
        }
    }
    
    // Verificar se há usuários de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users");
    $chatUsersCount = $stmt->fetchColumn();
    
    if ($chatUsersCount == 0) {
        echo "<p>⚠️ Nenhum usuário de chat encontrado. Criando usuários de teste...</p>";
        
        $testUsers = [
            ['João Silva', '<EMAIL>', '11987654321'],
            ['Maria Santos', '<EMAIL>', '11976543210'],
            ['Pedro Costa', '<EMAIL>', '11965432109']
        ];
        
        foreach ($testUsers as $user) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) 
                VALUES (?, ?, ?, 0, NOW(), NOW())
            ");
            $stmt->execute($user);
        }
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users");
        $newCount = $stmt->fetchColumn();
        echo "<p>✅ $newCount usuários de chat criados</p>";
    }
    
    echo "<h2>3. 🧪 Teste de Funcionalidades</h2>";
    
    // Teste 1: Criar usuário de chat via API
    echo "<h3>Teste 1: Criar usuário via API</h3>";
    
    $testData = [
        'name' => 'Usuário Teste ' . date('H:i:s'),
        'email' => 'teste' . time() . '@email.com',
        'whatsapp' => '11999999999'
    ];
    
    $ch = curl_init('http://localhost/api/create_user.php');
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => http_build_query($testData),
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $testUserId = $data['user_id'];
            echo "<p>✅ Usuário criado via API: ID $testUserId</p>";
            
            // Teste 2: Enviar mensagem via API
            echo "<h3>Teste 2: Enviar mensagem via API</h3>";
            
            $messageData = [
                'user_id' => $testUserId,
                'message' => 'Mensagem de teste - ' . date('H:i:s')
            ];
            
            $ch = curl_init('http://localhost/api/send_message.php');
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($messageData),
                CURLOPT_TIMEOUT => 10
            ]);
            
            $messageResponse = curl_exec($ch);
            $messageHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($messageHttpCode == 200) {
                $messageData = json_decode($messageResponse, true);
                if ($messageData && $messageData['success']) {
                    echo "<p>✅ Mensagem enviada via API</p>";
                } else {
                    echo "<p>❌ Erro ao enviar mensagem: " . ($messageData['message'] ?? 'Erro desconhecido') . "</p>";
                }
            } else {
                echo "<p>❌ Erro HTTP ao enviar mensagem: $messageHttpCode</p>";
                echo "<p>Resposta: $messageResponse</p>";
            }
            
            // Teste 3: Buscar mensagens via API
            echo "<h3>Teste 3: Buscar mensagens via API</h3>";
            
            $ch = curl_init("http://localhost/api/get_messages.php?user_id=$testUserId&last_time=0");
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10
            ]);
            
            $getResponse = curl_exec($ch);
            $getHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($getHttpCode == 200) {
                $getData = json_decode($getResponse, true);
                if ($getData && $getData['success']) {
                    $messageCount = count($getData['messages']);
                    echo "<p>✅ Mensagens recuperadas via API: $messageCount mensagens</p>";
                } else {
                    echo "<p>❌ Erro ao buscar mensagens: " . ($getData['message'] ?? 'Erro desconhecido') . "</p>";
                }
            } else {
                echo "<p>❌ Erro HTTP ao buscar mensagens: $getHttpCode</p>";
            }
            
            // Limpar dados de teste
            $pdo->prepare("DELETE FROM chat_messages WHERE user_id = ?")->execute([$testUserId]);
            $pdo->prepare("DELETE FROM chat_users WHERE id = ?")->execute([$testUserId]);
            echo "<p>🧹 Dados de teste removidos</p>";
            
        } else {
            echo "<p>❌ Erro ao criar usuário: " . ($data['message'] ?? 'Erro desconhecido') . "</p>";
        }
    } else {
        echo "<p>❌ Erro HTTP ao criar usuário: $httpCode</p>";
        echo "<p>Resposta: $response</p>";
    }
    
    echo "<h2>4. 🔗 Links de Teste</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='check_orders_table.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>🔧 Verificar Tabela Orders</a>";
    echo "<a href='fix_chat_foreign_keys.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>🔧 Corrigir Chat FK</a>";
    echo "<a href='admin_login_test.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px;'>🔐 Login Admin</a>";
    echo "<a href='chat.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px;'>💬 Chat Cliente</a>";
    echo "<a href='admin/chat.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px;'>👨‍💼 Chat Admin</a>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724; margin-top: 20px;'>";
    echo "<h4>✅ Teste Final Concluído!</h4>";
    echo "<p>Se todos os testes acima passaram, os sistemas devem estar funcionando corretamente.</p>";
    echo "<p><strong>Próximos passos:</strong></p>";
    echo "<ol>";
    echo "<li>Teste o pagamento em uma compra real</li>";
    echo "<li>Teste o chat cliente e admin</li>";
    echo "<li>Verifique se não há mais erros no console</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante o teste:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #495057;
}

h1 {
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
    margin-top: 30px;
}

h3 {
    color: #6c757d;
    margin-top: 20px;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
