<?php
require_once 'admin/database/connection.php';

// Verificar se há um pedido na sessão
session_start();
if (!isset($_SESSION['last_order_id'])) {
    header('Location: index.php');
    exit;
}

$order_id = $_SESSION['last_order_id'];

// Buscar informações do pedido
$db = Database::getInstance();
$pdo = $db->getConnection();
$stmt = $pdo->prepare("SELECT s.*, p.name as product_name FROM sales s JOIN products p ON s.product_id = p.id WHERE s.id = ?");
$stmt->execute([$order_id]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

// Limpar a sessão
unset($_SESSION['last_order_id']);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento Aprovado</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <div class="card">
                    <div class="card-body">
                        <h1 class="card-title text-success mb-4">
                            <i class="fas fa-check-circle"></i> Pagamento Aprovado!
                        </h1>
                        <p class="card-text">Seu pagamento foi processado com sucesso!</p>
                        <p class="card-text">Obrigado por sua compra.</p>
                        <hr>
                        <?php if ($order): ?>
                        <div class="order-details">
                            <h3>Detalhes do Pedido</h3>
                            <p><strong>Produto:</strong> <?php echo htmlspecialchars($order['product_name']); ?></p>
                            <p><strong>Valor:</strong> R$ <?php echo number_format($order['amount'], 2, ',', '.'); ?></p>
                            <p><strong>Data:</strong> <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></p>
                        </div>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-primary">Voltar para a Loja</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>
