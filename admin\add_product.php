<?php
require_once 'includes/auth_check.php';
require_once 'includes/header.php';

$success = false;
$error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        $name = $_POST['name'];
        $description = $_POST['description'];
        $price = $_POST['price'];
        $image = '';
        
        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['image']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $newName = uniqid() . '.' . $ext;
                $uploadDir = '../uploads/products/';
                
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $newName)) {
                    $image = $newName;
                }
            }
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO products (name, description, price, image, status, created_at)
            VALUES (?, ?, ?, ?, 'active', NOW())
        ");

        if ($stmt->execute([$name, $description, $price, $image])) {
            echo "<script>
                Swal.fire({
                    title: 'Produto adicionado com sucesso!',
                    icon: 'success',
                    confirmButtonColor: '#0d6efd'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = 'products.php';
                    }
                });
            </script>";
        }
        
    } catch (Exception $e) {
        echo "<script>
            Swal.fire({
                title: 'Erro ao adicionar produto',
                text: '" . htmlspecialchars($e->getMessage()) . "',
                icon: 'error',
                confirmButtonColor: '#0d6efd'
            });
        </script>";
    }
}
?>

<!-- Add SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fs-2 m-0">Adicionar Produto</h2>
        <a href="products.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Voltar
        </a>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">Nome do Produto</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Descrição</label>
                            <textarea name="description" class="form-control" rows="4"></textarea>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Preço</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="number" name="price" class="form-control" step="0.01" required>
                            </div>
                        </div>
                        

                        
                        <div class="mb-3">
                            <label class="form-label">Imagem</label>
                            <input type="file" name="image" class="form-control" accept="image/*">
                        </div>
                    </div>
                </div>
                
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Produto
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        Swal.fire({
            title: "Produto Criado com sucesso!",
            icon: "success",
            draggable: true
        }).then((result) => {
            if (result.isConfirmed) {
                this.submit();
            }
        });
    });
</script>
</body>
</html>
