<?php
/**
 * API para gerenciar assinaturas de push notifications
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    // Criar tabela se não existir
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS push_subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            endpoint TEXT NOT NULL,
            keys_json JSON NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            UNIQUE KEY unique_endpoint (endpoint(255))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Registrar nova assinatura
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['endpoint']) || !isset($input['keys'])) {
            throw new Exception('Dados de assinatura inválidos');
        }
        
        $endpoint = $input['endpoint'];
        $keys = json_encode($input['keys']);
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Inserir ou atualizar assinatura
        $stmt = $pdo->prepare("
            INSERT INTO push_subscriptions (endpoint, keys_json, ip_address, user_agent, is_active) 
            VALUES (?, ?, ?, ?, 1)
            ON DUPLICATE KEY UPDATE 
                keys_json = VALUES(keys_json),
                ip_address = VALUES(ip_address),
                user_agent = VALUES(user_agent),
                is_active = 1,
                last_updated = CURRENT_TIMESTAMP
        ");
        
        $stmt->execute([$endpoint, $keys, $ip_address, $user_agent]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Assinatura registrada com sucesso',
            'subscription_id' => $pdo->lastInsertId() ?: $pdo->query("SELECT id FROM push_subscriptions WHERE endpoint = " . $pdo->quote($endpoint))->fetchColumn()
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Listar assinaturas ativas
        $stmt = $pdo->query("
            SELECT 
                id,
                LEFT(endpoint, 50) as endpoint_preview,
                ip_address,
                created_at,
                last_updated,
                is_active
            FROM push_subscriptions 
            WHERE is_active = 1 
            ORDER BY created_at DESC 
            LIMIT 100
        ");
        
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'subscriptions' => $subscriptions,
            'total' => count($subscriptions)
        ]);
        
    } else {
        throw new Exception('Método não permitido');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
