<?php
/**
 * Script para criar tabelas PWA no banco de dados
 * Execute este arquivo uma vez para configurar as tabelas necessárias
 */

require_once 'database/connection.php';

try {
    echo "<h2>🔧 Configurando Banco de Dados PWA - KESUNG SITE</h2>\n";
    
    // Ler arquivo SQL
    $sqlFile = 'database/create_pwa_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Arquivo SQL não encontrado: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("Erro ao ler arquivo SQL");
    }
    
    // Dividir em comandos individuais
    $commands = array_filter(array_map('trim', explode(';', $sql)));
    
    $success = 0;
    $errors = 0;
    
    foreach ($commands as $command) {
        if (empty($command) || strpos($command, '--') === 0) {
            continue;
        }
        
        try {
            $conn->exec($command);
            $success++;
            
            // Identificar tipo de comando
            if (stripos($command, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $command, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✅ Tabela criada: $tableName<br>\n";
            } elseif (stripos($command, 'CREATE VIEW') !== false) {
                preg_match('/CREATE VIEW.*?`?(\w+)`?/i', $command, $matches);
                $viewName = $matches[1] ?? 'unknown';
                echo "✅ View criada: $viewName<br>\n";
            } elseif (stripos($command, 'CREATE INDEX') !== false) {
                echo "✅ Índice criado<br>\n";
            } elseif (stripos($command, 'INSERT') !== false) {
                echo "✅ Dados inseridos<br>\n";
            }
            
        } catch (PDOException $e) {
            $errors++;
            echo "❌ Erro: " . $e->getMessage() . "<br>\n";
        }
    }
    
    echo "<br><h3>📊 Resumo:</h3>\n";
    echo "✅ Comandos executados com sucesso: $success<br>\n";
    echo "❌ Erros: $errors<br>\n";
    
    // Verificar tabelas criadas
    echo "<br><h3>📋 Verificando Tabelas:</h3>\n";
    $tables = ['app_installs', 'push_subscriptions', 'notification_logs', 'notification_campaigns', 'app_users'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // Contar registros
                $countStmt = $conn->query("SELECT COUNT(*) FROM $table");
                $count = $countStmt->fetchColumn();
                echo "✅ $table - $count registros<br>\n";
            } else {
                echo "❌ $table - não existe<br>\n";
            }
        } catch (PDOException $e) {
            echo "❌ $table - erro: " . $e->getMessage() . "<br>\n";
        }
    }
    
    // Verificar subscriptions ativas
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM push_subscriptions WHERE is_active = 1");
        $activeCount = $stmt->fetchColumn();
        echo "<br><h3>📱 Push Subscriptions Ativas: $activeCount</h3>\n";
        
        if ($activeCount == 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
            echo "<strong>⚠️ Nenhuma subscription ativa encontrada!</strong><br>\n";
            echo "Para receber notificações push:<br>\n";
            echo "1. Acesse o site no celular<br>\n";
            echo "2. Instale o app PWA<br>\n";
            echo "3. Permita notificações quando solicitado<br>\n";
            echo "4. O sistema registrará automaticamente a subscription<br>\n";
            echo "</div>\n";
        }
        
    } catch (PDOException $e) {
        echo "❌ Erro ao verificar subscriptions: " . $e->getMessage() . "<br>\n";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border-radius: 8px;'>\n";
    echo "<h3>✅ Configuração Concluída!</h3>\n";
    echo "O sistema PWA está configurado e pronto para uso.<br>\n";
    echo "As notificações push funcionarão assim que os usuários instalarem o app e permitirem notificações.\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px;'>\n";
    echo "<h3>❌ Erro na Configuração</h3>\n";
    echo "Erro: " . $e->getMessage() . "<br>\n";
    echo "Verifique se o arquivo database/create_pwa_tables.sql existe e se a conexão com o banco está funcionando.\n";
    echo "</div>\n";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #4318FF;
    text-align: center;
}

h3 {
    color: #333;
    margin-top: 20px;
}
</style>

<script>
// Auto-refresh para mostrar atualizações em tempo real
setTimeout(() => {
    if (confirm('Configuração concluída! Deseja recarregar para ver o status atualizado?')) {
        window.location.reload();
    }
}, 5000);
</script>
