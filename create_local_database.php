<?php
/**
 * Script para criar banco local como alternativa
 */

echo "<h2>Criando Banco Local como Alternativa</h2>";

try {
    // Conectar ao MySQL local sem especificar banco
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Conectado ao MySQL local<br>";
    
    // Criar banco se não existir
    $dbName = 'u276254152_banco_loja';
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Banco '$dbName' criado/verificado<br>";
    
    // Conectar ao banco específico
    $pdo = new PDO("mysql:host=localhost;dbname=$dbName;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Conectado ao banco '$dbName'<br>";
    
    // Criar tabelas
    echo "<h3>Criando Tabelas...</h3>";
    
    // Tabela users
    $pdo->exec("
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        balance DECIMAL(10,2) DEFAULT 0.00,
        is_admin TINYINT(1) DEFAULT 0,
        is_online TINYINT(1) DEFAULT 0,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'users' criada<br>";
    
    // Tabela chat_messages
    $pdo->exec("
    CREATE TABLE IF NOT EXISTS chat_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        message TEXT NOT NULL,
        is_admin TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'chat_messages' criada<br>";
    
    // Tabela admins
    $pdo->exec("
    CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'admin',
        is_active TINYINT(1) DEFAULT 1,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Tabela 'admins' criada<br>";
    
    // Inserir dados de teste
    echo "<h3>Inserindo Dados de Teste...</h3>";
    
    // Usuários de teste
    $testUsers = [
        ['username' => 'paula_helena', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'casa_jordan', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test1', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test2', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test3', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)]
    ];
    
    foreach ($testUsers as $user) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, balance) VALUES (?, ?, ?, ?)");
            $stmt->execute([$user['username'], $user['email'], $user['password'], rand(100, 1000)]);
            echo "✅ Usuário '{$user['username']}' inserido<br>";
        } catch (Exception $e) {
            echo "⚠️ Usuário '{$user['username']}' já existe<br>";
        }
    }
    
    // Admin de teste
    try {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT IGNORE INTO admins (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'super_admin']);
        echo "✅ Admin inserido (login: admin, senha: admin123)<br>";
    } catch (Exception $e) {
        echo "⚠️ Admin já existe<br>";
    }
    
    // Mensagens de teste
    $users = $pdo->query("SELECT id, username FROM users LIMIT 5")->fetchAll();
    $testMessages = [
        'Olá, preciso de ajuda com meu pedido',
        'Quando será processado meu saque?',
        'Tenho uma dúvida sobre o sistema',
        'Obrigado pelo atendimento!',
        'Gostaria de saber sobre promoções'
    ];
    
    foreach ($users as $index => $user) {
        if (isset($testMessages[$index])) {
            try {
                $stmt = $pdo->prepare("INSERT INTO chat_messages (user_id, message, is_admin) VALUES (?, ?, 0)");
                $stmt->execute([$user['id'], $testMessages[$index]]);
                
                $adminResponse = "Olá {$user['username']}, como posso ajudá-lo?";
                $stmt = $pdo->prepare("INSERT INTO chat_messages (user_id, message, is_admin) VALUES (?, ?, 1)");
                $stmt->execute([$user['id'], $adminResponse]);
                
                echo "✅ Mensagens inseridas para {$user['username']}<br>";
            } catch (Exception $e) {
                echo "⚠️ Erro ao inserir mensagem: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    // Atualizar configuração para usar banco local
    echo "<h3>Atualizando Configuração...</h3>";
    
    $configFile = 'database/connection.php';
    $content = file_get_contents($configFile);
    
    $content = preg_replace(
        "/private const DB_HOST = '[^']*';/",
        "private const DB_HOST = 'localhost';",
        $content
    );
    
    $content = preg_replace(
        "/private const DB_USER = '[^']*';/",
        "private const DB_USER = 'root';",
        $content
    );
    
    $content = preg_replace(
        "/private const DB_PASS = '[^']*';/",
        "private const DB_PASS = '';",
        $content
    );
    
    file_put_contents($configFile, $content);
    echo "✅ Configuração atualizada para banco local<br>";
    
    // Verificação final
    $userCount = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $messageCount = $pdo->query("SELECT COUNT(*) as count FROM chat_messages")->fetch()['count'];
    $adminCount = $pdo->query("SELECT COUNT(*) as count FROM admins")->fetch()['count'];
    
    echo "<h3>✅ Banco Local Criado com Sucesso!</h3>";
    echo "<p><strong>Estatísticas:</strong></p>";
    echo "<ul>";
    echo "<li>👥 Usuários: $userCount</li>";
    echo "<li>💬 Mensagens: $messageCount</li>";
    echo "<li>👨‍💼 Admins: $adminCount</li>";
    echo "</ul>";
    
    echo "<p><strong>Configuração Atual:</strong></p>";
    echo "<ul>";
    echo "<li>Host: localhost</li>";
    echo "<li>Database: $dbName</li>";
    echo "<li>Username: root</li>";
    echo "<li>Password: (vazia)</li>";
    echo "</ul>";
    
    echo "<p><strong>Próximos Passos:</strong></p>";
    echo "<ul>";
    echo "<li><a href='chat.php'>Testar Chat Público</a></li>";
    echo "<li><a href='admin/chat.php'>Testar Chat Admin</a></li>";
    echo "<li><a href='test_db_connection.php'>Verificar Conexão</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>Possíveis soluções:</strong></p>";
    echo "<ul>";
    echo "<li>Verifique se o XAMPP está rodando</li>";
    echo "<li>Verifique se o MySQL está ativo</li>";
    echo "<li>Confirme as credenciais do MySQL local</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criação de Banco Local</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #101010; 
            color: #fff; 
            padding: 20px; 
        }
        h2, h3 { color: #4CAF50; }
        a { color: #4CAF50; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
