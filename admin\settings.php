<?php
require_once 'includes/auth_check.php';
require_once 'includes/header.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Verificar se a tabela existe, se não, criar
    $stmt = $pdo->query("SHOW TABLES LIKE 'site_settings'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE site_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) DEFAULT 'Sistema de Vendas',
                description TEXT,
                logo_path VARCHAR(255),
                favicon_path VARCHAR(255),
                primary_color VARCHAR(7) DEFAULT '#4169E1',
                footer_color VARCHAR(7) DEFAULT '#1B2838',
                button_gradient_start VARCHAR(7) DEFAULT '#28a745',
                button_gradient_end VARCHAR(7) DEFAULT '#218838',
                header_gradient_start VARCHAR(7) DEFAULT '#28a745',
                header_gradient_end VARCHAR(7) DEFAULT '#4169E1',
                contact_email VARCHAR(255),
                whatsapp_number VARCHAR(20),
                instagram_url VARCHAR(255),
                facebook_url VARCHAR(255),
                youtube_url VARCHAR(255),
                analytics_code TEXT,
                footer_text VARCHAR(255) DEFAULT '© 2024 Sistema de Vendas',
                tab_title VARCHAR(255) DEFAULT 'SISTEMA DE VENDAS',
                show_logo TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);

        // Inserir configurações padrão
        $stmt = $pdo->prepare("INSERT INTO site_settings (title, description) VALUES (?, ?)");
        $stmt->execute(['Sistema de Vendas', 'Sistema completo de vendas online']);
    }

    // Get current settings
    $stmt = $pdo->query("SELECT * FROM site_settings LIMIT 1");
    $settings = $stmt->fetch();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $logo = $settings['logo_path'] ?? '';
        $favicon = $settings['favicon_path'] ?? '';
        $errors = [];
        $success = '';
        
        // Validação dos campos obrigatórios
        if (empty($_POST['title'])) {
            $errors[] = "O Título do Site é obrigatório.";
        }
        if (empty($_POST['description'])) {
            $errors[] = "A Descrição é obrigatória.";
        }
        
        // Upload do logo
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
            $allowed = ['jpg', 'jpeg', 'png'];
            $filename = $_FILES['logo']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (!in_array($ext, $allowed)) {
                $errors[] = "Formato de logo inválido. Use apenas JPG, JPEG ou PNG.";
            } else {
                $newName = 'logo.' . $ext;
                $uploadDir = '../uploads/';
                
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                if (move_uploaded_file($_FILES['logo']['tmp_name'], $uploadDir . $newName)) {
                    $logo = $newName;
                } else {
                    $errors[] = "Erro ao fazer upload do logo.";
                }
            }
        }
        
        // Upload do favicon
        if (isset($_FILES['favicon']) && $_FILES['favicon']['error'] === 0) {
            $allowed = ['ico', 'png', 'gif'];
            $filename = $_FILES['favicon']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (!in_array($ext, $allowed)) {
                $errors[] = "Formato de favicon inválido. Use apenas ICO, PNG ou GIF.";
            } else {
                $newName = 'favicon.' . $ext;
                $uploadDir = '../uploads/';
                
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                if (move_uploaded_file($_FILES['favicon']['tmp_name'], $uploadDir . $newName)) {
                    $favicon = $newName;
                } else {
                    $errors[] = "Erro ao fazer upload do favicon.";
                }
            }
        }
        
        if (empty($errors)) {
            if ($settings) {
                $stmt = $pdo->prepare("
                    UPDATE site_settings 
                    SET title = ?, description = ?, logo_path = ?, 
                        primary_color = ?, footer_color = ?,
                        button_gradient_start = ?, button_gradient_end = ?,
                        header_gradient_start = ?, header_gradient_end = ?,
                        contact_email = ?, whatsapp_number = ?, 
                        instagram_url = ?, facebook_url = ?, youtube_url = ?,
                        analytics_code = ?, footer_text = ?, tab_title = ?,
                        favicon_path = ?, show_logo = ?
                    WHERE id = ?
                ");
                
                $result = $stmt->execute([
                    $_POST['title'],
                    $_POST['description'],
                    $logo,
                    $_POST['primary_color'] ?? '#4169E1',
                    $_POST['footer_color'] ?? '#1B2838',
                    $_POST['button_gradient_start'] ?? '#28a745',
                    $_POST['button_gradient_end'] ?? '#218838',
                    $_POST['header_gradient_start'] ?? '#28a745',
                    $_POST['header_gradient_end'] ?? '#4169E1',
                    $_POST['contact_email'],
                    $_POST['whatsapp_number'],
                    $_POST['instagram_url'],
                    $_POST['facebook_url'],
                    $_POST['youtube_url'] ?? '',
                    $_POST['analytics_code'],
                    $_POST['footer_text'] ?? ' 2024 Sistema de Vendas',
                    $_POST['tab_title'] ?? 'SISTEMA DE VENDAS',
                    $favicon,
                    isset($_POST['show_logo']) ? 1 : 0,
                    $settings['id']
                ]);
                
                if ($result) {
                    $success = "Configurações atualizadas com sucesso!";
                    // Atualiza os settings para mostrar os novos valores
                    $stmt = $pdo->query("SELECT * FROM site_settings LIMIT 1");
                    $settings = $stmt->fetch();
                } else {
                    $errors[] = "Erro ao salvar as configurações.";
                }
            }
        }
    }
} catch (Exception $e) {
    $errors[] = $e->getMessage();
}
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Configurações do Site</h2>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul>
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Título do Site</label>
                        <input type="text" name="title" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['title'] ?? ''); ?>" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Título da Aba do Navegador</label>
                        <input type="text" name="tab_title" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['tab_title'] ?? 'SISTEMA DE VENDAS'); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold mb-3">Logo do Site</label>
                        
                        <div class="card p-3">
                            <?php if (!empty($settings['logo_path'])): ?>
                                <div class="mb-3">
                                    <label class="form-label text-muted">Logo atual:</label>
                                    <div>
                                        <img src="../uploads/<?php echo htmlspecialchars($settings['logo_path']); ?>" 
                                             alt="Logo atual" class="img-thumbnail" style="max-height: 50px;">
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label class="form-label text-muted">Upload de novo logo:</label>
                                <input type="file" name="logo" class="form-control" accept="image/jpeg,image/png">
                                <div class="form-text">Formatos aceitos: JPG, JPEG, PNG</div>
                            </div>

                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="show_logo" name="show_logo" value="1" 
                                       <?php echo (!isset($settings['show_logo']) || $settings['show_logo']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_logo">
                                    <i class="fas fa-eye" id="logoIcon"></i>
                                    <span id="logoText">Logo visível no site</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Favicon (ícone da aba)</label>
                        <input type="file" name="favicon" class="form-control" accept="image/x-icon,image/png,image/gif">
                        <?php if (!empty($settings['favicon_path'])): ?>
                            <img src="../uploads/<?php echo htmlspecialchars($settings['favicon_path']); ?>" 
                                 alt="Favicon atual" class="mt-2" style="max-height: 32px;">
                        <?php endif; ?>
                        <small class="form-text text-muted">Recomendado: arquivo .ico, .png ou .gif de 32x32 pixels</small>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Descrição</label>
                    <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['description'] ?? ''); ?></textarea>
                </div>

                <h4 class="mb-3">Cores e Estilos</h4>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Cor Principal</label>
                        <input type="color" name="primary_color" class="form-control form-control-color w-100" 
                               value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#4169E1'); ?>">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label class="form-label">Cor do Rodapé</label>
                        <input type="color" name="footer_color" class="form-control form-control-color w-100" 
                               value="<?php echo htmlspecialchars($settings['footer_color'] ?? '#1B2838'); ?>">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label class="form-label">Cor do Cabeçalho</label>
                        <input type="color" name="header_gradient_end" class="form-control form-control-color w-100" 
                               value="<?php echo htmlspecialchars($settings['header_gradient_end'] ?? '#4169E1'); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Gradiente Botão (Início)</label>
                        <input type="color" name="button_gradient_start" class="form-control form-control-color w-100" 
                               value="<?php echo htmlspecialchars($settings['button_gradient_start'] ?? '#28a745'); ?>">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Gradiente Botão (Fim)</label>
                        <input type="color" name="button_gradient_end" class="form-control form-control-color w-100" 
                               value="<?php echo htmlspecialchars($settings['button_gradient_end'] ?? '#218838'); ?>">
                    </div>
                </div>

                <h4 class="mb-3">Contato e Redes Sociais</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email de Contato</label>
                        <input type="email" name="contact_email" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">WhatsApp</label>
                        <input type="text" name="whatsapp_number" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['whatsapp_number'] ?? ''); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">URL do Instagram</label>
                        <input type="url" name="instagram_url" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['instagram_url'] ?? ''); ?>">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label class="form-label">URL do Facebook</label>
                        <input type="url" name="facebook_url" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['facebook_url'] ?? ''); ?>">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label class="form-label">URL do YouTube</label>
                        <input type="url" name="youtube_url" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['youtube_url'] ?? ''); ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Texto do Rodapé</label>
                    <input type="text" name="footer_text" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['footer_text'] ?? ' 2024 Sistema de Vendas'); ?>">
                </div>

                <div class="mb-3">
                    <label class="form-label">Código do Analytics</label>
                    <textarea name="analytics_code" class="form-control" rows="4"><?php echo htmlspecialchars($settings['analytics_code'] ?? ''); ?></textarea>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Salvar Configurações
                </button>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const showLogoCheckbox = document.getElementById('show_logo');
    const logoIcon = document.getElementById('logoIcon');
    const logoText = document.getElementById('logoText');
    
    function updateLogoVisibility() {
        if (showLogoCheckbox.checked) {
            logoIcon.className = 'fas fa-eye';
            logoText.textContent = 'Logo visível no site';
        } else {
            logoIcon.className = 'fas fa-eye-slash';
            logoText.textContent = 'Logo oculto no site';
        }
    }
    
    // Atualiza o estado inicial
    updateLogoVisibility();
    
    // Adiciona o evento de mudança
    showLogoCheckbox.addEventListener('change', updateLogoVisibility);
});
</script>

<?php require_once 'includes/footer.php'; ?>
