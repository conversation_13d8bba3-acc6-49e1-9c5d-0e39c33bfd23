<?php
require_once 'database/connection.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

if (!isset($_GET['email'])) {
    header('Location: dashboard.php');
    exit;
}

$email = $_GET['email'];
$db = Database::getInstance();
$pdo = $db->getConnection();

// Buscar informações do cliente
$stmt = $pdo->prepare("SELECT * FROM orders WHERE customer_email = ? ORDER BY created_at DESC");
$stmt->execute([$email]);
$orders = $stmt->fetchAll();

?>

<div class="content-wrapper" style="min-height: 100vh;">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Detalhes do Cliente</h1>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Informações do Cliente</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($email); ?></p>
                                    <?php if (!empty($orders)): ?>
                                    <p><strong>Nome:</strong> <?php echo htmlspecialchars($orders[0]['customer_name']); ?></p>
                                    <?php if (!empty($orders[0]['customer_whatsapp'])): ?>
                                    <p><strong>WhatsApp:</strong> <?php echo htmlspecialchars($orders[0]['customer_whatsapp']); ?></p>
                                    <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">Histórico de Pedidos</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Produto</th>
                                            <th>Valor</th>
                                            <th>Status</th>
                                            <th>Data</th>
                                            <th>Método</th>
                                            <th>Referência</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($order['id']); ?></td>
                                            <td><?php echo htmlspecialchars($order['product_id']); ?></td>
                                            <td>R$ <?php echo number_format($order['total_amount'], 2, ',', '.'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php 
                                                    echo $order['payment_status'] === 'approved' ? 'success' : 
                                                        ($order['payment_status'] === 'pending' ? 'warning' : 'danger'); 
                                                ?>">
                                                    <?php echo htmlspecialchars($order['payment_status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($order['payment_method']); ?></td>
                                            <td><?php echo htmlspecialchars($order['payment_id']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($orders)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">Nenhum pedido encontrado</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php require_once 'includes/footer.php'; ?>
