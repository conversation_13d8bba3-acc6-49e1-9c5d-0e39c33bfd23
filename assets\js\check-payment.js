// Função para verificar o status do pagamento
async function checkPaymentStatus(paymentId) {
  try {
    const response = await fetch(`admin/check_payment_status.php?payment_id=${paymentId}`)
    const data = await response.json()

    if (data.success) {
      // Atualiza a UI
      const statusElement = document.querySelector(`[data-payment-id="${paymentId}"]`)
      if (statusElement) {
        if (data.status === "approved") {
          statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Pago'
          statusElement.classList.remove("status-warning")
          statusElement.classList.add("status-success")

          // Mostra mensagem de sucesso
          Swal.fire({
            icon: "success",
            title: "Pagamento Confirmado!",
            text: "O status do seu pagamento foi atualizado com sucesso.",
            showConfirmButton: false,
            timer: 3000,
          })

          // Atualiza a página após 3 segundos
          setTimeout(() => {
            window.location.reload()
          }, 3000)

          return true
        }
      }
      return false
    } else {
      console.error("Erro ao verificar status:", data.error)
      return false
    }
  } catch (error) {
    console.error("Erro ao verificar status:", error)
    return false
  }
}

// Inicia a verificação do pagamento
function startPaymentCheck(paymentId) {
  // Verifica imediatamente
  checkPaymentStatus(paymentId)

  // Continua verificando a cada 30 segundos
  const interval = setInterval(() => {
    const result = checkPaymentStatus(paymentId)
    if (result) {
      clearInterval(interval)
    }
  }, 30000)

  // Para de verificar após 10 minutos
  setTimeout(() => {
    clearInterval(interval)
  }, 600000)
}

// Inicia a verificação para todos os pagamentos pendentes
document.addEventListener("DOMContentLoaded", () => {
  const pendingPayments = document.querySelectorAll(".status-warning[data-payment-id]")
  pendingPayments.forEach((element) => {
    const paymentId = element.getAttribute("data-payment-id")
    if (paymentId) {
      startPaymentCheck(paymentId)
    }
  })
})

