<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../database/connection.php';

// Verifica se o usuário está autenticado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Atualiza o timestamp de último acesso
if (isset($_SESSION['admin_id'])) {
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();

        $stmt = $pdo->prepare("UPDATE admins SET updated_at = NOW() WHERE id = ?");
        $stmt->execute([$_SESSION['admin_id']]);
    } catch (PDOException $e) {
        // Log error silently
        error_log("Error updating admin last access: " . $e->getMessage());
    }
}
