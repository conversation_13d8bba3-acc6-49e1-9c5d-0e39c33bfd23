<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Verificar quais colunas já existem
    $stmt = $pdo->query("DESCRIBE site_settings");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Array com as novas colunas e seus tipos/valores padrão
    $newColumns = [
        'primary_color' => "VARCHAR(7) DEFAULT '#4169E1'",
        'footer_color' => "VARCHAR(7) DEFAULT '#1B2838'",
        'button_gradient_start' => "VARCHAR(7) DEFAULT '#28a745'",
        'button_gradient_end' => "VARCHAR(7) DEFAULT '#218838'",
        'header_gradient_start' => "VARCHAR(7) DEFAULT '#28a745'",
        'header_gradient_end' => "VARCHAR(7) DEFAULT '#4169E1'",
        'youtube_url' => "VARCHAR(255)",
        'footer_text' => "VARCHAR(255) DEFAULT ' 2024 Sistema de Vendas'"
    ];
    
    // Adicionar apenas as colunas que não existem
    foreach ($newColumns as $column => $definition) {
        if (!in_array($column, $columns)) {
            $sql = "ALTER TABLE site_settings ADD COLUMN $column $definition";
            $pdo->exec($sql);
            echo "Coluna $column adicionada com sucesso!\n";
        } else {
            echo "Coluna $column já existe.\n";
        }
    }
    
    // Atualizar registros existentes com valores padrão
    $stmt = $pdo->prepare("
        UPDATE site_settings 
        SET primary_color = COALESCE(primary_color, '#4169E1'),
            footer_color = COALESCE(footer_color, '#1B2838'),
            button_gradient_start = COALESCE(button_gradient_start, '#28a745'),
            button_gradient_end = COALESCE(button_gradient_end, '#218838'),
            header_gradient_start = COALESCE(header_gradient_start, '#28a745'),
            header_gradient_end = COALESCE(header_gradient_end, '#4169E1'),
            footer_text = COALESCE(footer_text, ' 2024 Sistema de Vendas')
        WHERE id = ?
    ");
    
    // Pegar todos os IDs existentes
    $ids = $pdo->query("SELECT id FROM site_settings")->fetchAll(PDO::FETCH_COLUMN);
    
    // Atualizar cada registro
    foreach ($ids as $id) {
        $stmt->execute([$id]);
        echo "Registro $id atualizado com valores padrão.\n";
    }
    
    echo "Processo concluído com sucesso!\n";
    
} catch (PDOException $e) {
    die("Erro: " . $e->getMessage() . "\n");
}
