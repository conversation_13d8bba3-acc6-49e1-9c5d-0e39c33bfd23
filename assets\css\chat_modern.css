/* Variáveis */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --bg-light: #f8f9fa;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Reset e estilos gerais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;
    display: flex;
    align-items: stretch;
}

/* Container do chat */
.chat-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.chat-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    background: var(--primary-color);
    color: white;
    padding: 15px;
    text-align: center;
    flex-shrink: 0;
}

.chat-header h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

/* <PERSON><PERSON> de mensagens */
.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: white;
    display: flex;
    flex-direction: column;
}

.message {
    margin: 4px 0;
    max-width: 80%;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    align-self: flex-end;
}

.message.admin {
    align-self: flex-start;
}

.message-sender {
    font-size: 0.8em;
    margin-bottom: 4px;
    color: var(--secondary-color);
}

.message-content {
    padding: 10px 15px;
    border-radius: 15px;
    word-wrap: break-word;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
}

.message.admin .message-content {
    background: #e9ecef;
    color: #212529;
}

.message-time {
    font-size: 0.7em;
    margin-top: 4px;
    color: var(--secondary-color);
}

/* Área de input */
.chat-input {
    padding: 15px;
    background: white;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

#message-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    resize: none;
    height: 40px;
    font-size: 14px;
}

#message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-primary:hover {
    background: #0056b3;
}

/* Formulário de registro */
#registration-form {
    padding: 20px;
    background: white;
    display: flex;
    flex-direction: column;
    flex: 1;
}

#registration-form h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
}

/* Responsividade */
@media (max-width: 480px) {
    .chat-container {
        position: fixed;
        width: 100%;
        height: 100%;
    }
}
