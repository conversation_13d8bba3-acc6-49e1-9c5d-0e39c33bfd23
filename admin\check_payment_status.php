<?php
require_once 'session.php';
require_once 'database/connection.php';
require_once 'config/asaas.php';

header('Content-Type: application/json');

if (!isset($_GET['payment_id'])) {
    echo json_encode(['success' => false, 'error' => 'ID do pagamento não fornecido']);
    exit;
}

try {
    $payment_id = $_GET['payment_id'];
    
    // Conectar ao banco de dados
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Verificar status atual no Asaas
    $ch = curl_init(ASAAS_API_URL . '/payments/' . $payment_id);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
    
    $response = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_status == 200) {
        $payment_data = json_decode($response, true);
        $status = mapAsaasStatus($payment_data['status']);
        
        // Atualizar status no banco de dados
        if ($status === 'approved') {
            $stmt = $pdo->prepare("UPDATE orders SET payment_status = 'approved' WHERE payment_id = ?");
            $stmt->execute([$payment_id]);
            
            echo json_encode([
                'success' => true,
                'status' => $status,
                'message' => 'Pagamento aprovado!'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'status' => $status,
                'message' => 'Pagamento pendente'
            ]);
        }
    } else {
        throw new Exception('Erro ao verificar status no Asaas');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

