<?php
require_once 'database/connection.php';
require_once 'config/asaas.php';

// Verificar se as credenciais estão configuradas
if (empty(ASAAS_API_KEY)) {
    die("Erro: Chave de API do Asaas não configurada.");
}

try {
    // URL do webhook (substitua pelo seu domínio)
    $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'seudominio.com';
    $webhook_url = "https://{$domain}/notifications.php";
    
    // Configurar webhook no Asaas
    $webhook_data = [
        'url' => $webhook_url,
        'email' => '<EMAIL>', // Email para notificações de falha
        'enabled' => true,
        'interrupted' => false,
        'apiVersion' => 3,
        'authToken' => md5(uniqid(time())), // Token de autenticação opcional
        'types' => [
            'PAYMENT_CREATED',
            'PAYMENT_UPDATED',
            'PAYMENT_CONFIRMED',
            'PAYMENT_RECEIVED',
            'PAYMENT_OVERDUE',
            'PAYMENT_DELETED',
            'PAYMENT_RESTORED',
            'PAYMENT_REFUNDED',
            'PAYMENT_FAILED'
        ]
    ];
    
    // Enviar requisição para o Asaas
    $ch = curl_init(ASAAS_API_URL . '/webhook');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($webhook_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
    
    $response = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_status == 200 || $http_status == 201) {
        $webhook_response = json_decode($response, true);
        echo "Webhook configurado com sucesso! ID: " . $webhook_response['id'];
        
        // Salvar ID do webhook no banco de dados
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        // Criar tabela se não existir
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS webhook_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                webhook_id VARCHAR(100) NOT NULL,
                webhook_url VARCHAR(255) NOT NULL,
                auth_token VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // Inserir ou atualizar configuração
        $stmt = $pdo->prepare("
            INSERT INTO webhook_config (webhook_id, webhook_url, auth_token)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
            webhook_id = VALUES(webhook_id),
            webhook_url = VALUES(webhook_url),
            auth_token = VALUES(auth_token)
        ");
        
        $stmt->execute([
            $webhook_response['id'],
            $webhook_url,
            $webhook_data['authToken']
        ]);
        
    } else {
        echo "Erro ao configurar webhook: " . $response;
    }
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage();
}

