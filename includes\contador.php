<?php
require_once __DIR__ . '/../admin/database/connection.php';

function registrarAcesso() {
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        // Obter IP real do visitante
        $ip = $_SERVER['HTTP_CLIENT_IP'] ?? 
              $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['REMOTE_ADDR'];
              
        // Obter página atual
        $pagina = $_SERVER['REQUEST_URI'];
        
        // Registrar acesso
        $stmt = $pdo->prepare("INSERT INTO acessos (ip, pagina) VALUES (?, ?)");
        $stmt->execute([$ip, $pagina]);
        
        return true;
    } catch (Exception $e) {
        error_log("Erro ao registrar acesso: " . $e->getMessage());
        return false;
    }
}
