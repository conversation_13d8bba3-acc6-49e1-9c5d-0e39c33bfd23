<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Verificar se o token foi fornecido
if (!isset($_GET['token']) || empty($_GET['token'])) {
    die('Token de download não fornecido');
}

$token = $_GET['token'];

try {
    // Buscar informações do token
    $stmt = $pdo->prepare("
        SELECT 
            dt.*,
            p.file_path,
            p.name as product_name
        FROM download_tokens dt
        JOIN products p ON dt.product_id = p.id
        WHERE dt.token = ? 
        AND dt.used_at IS NULL 
        AND dt.expires_at > NOW()
    ");
    $stmt->execute([$token]);
    $download = $stmt->fetch(PDO::FETCH_ASSOC);

    // Verificar se o token é válido
    if (!$download) {
        die('Token de download inválido ou expirado');
    }

    // Verificar se o arquivo existe
    if (!file_exists($download['file_path'])) {
        die('Arquivo não encontrado');
    }

    // Marcar token como usado
    $stmt = $pdo->prepare("
        UPDATE download_tokens 
        SET used_at = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$download['id']]);

    // Configurar headers para download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . basename($download['file_path']) . '"');
    header('Content-Length: ' . filesize($download['file_path']));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: public');

    // Enviar arquivo
    readfile($download['file_path']);
    exit;

} catch (Exception $e) {
    error_log("Erro ao processar download: " . $e->getMessage());
    die('Erro ao processar download');
}
