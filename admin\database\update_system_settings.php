<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Criar tabela se não existir
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Configurações padrão
    $defaultSettings = [
        [
            'key' => 'tinymce_api_key',
            'value' => '',
            'description' => 'API key for TinyMCE editor'
        ],
        [
            'key' => 'site_name',
            'value' => 'Minha Loja',
            'description' => 'Nome do site'
        ],
        [
            'key' => 'site_description',
            'value' => 'Sistema de vendas online',
            'description' => 'Descrição do site'
        ]
    ];

    // Inserir ou atualizar configurações
    foreach ($defaultSettings as $setting) {
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value, setting_description)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
                setting_value = CASE 
                    WHEN setting_value = '' THEN VALUES(setting_value)
                    ELSE setting_value
                END,
                setting_description = VALUES(setting_description)
        ");
        
        $stmt->execute([
            $setting['key'],
            $setting['value'],
            $setting['description']
        ]);
    }

    echo "Configurações do sistema atualizadas com sucesso!\n";

} catch (PDOException $e) {
    die("Erro ao atualizar configurações do sistema: " . $e->getMessage() . "\n");
}
