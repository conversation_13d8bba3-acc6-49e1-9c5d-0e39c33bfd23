:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --background-color: #f1f5f9;
    --border-color: #e2e8f0;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Chat Moderno */
.chat-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f6f9fc, #eef2f7);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.chat-container {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: translateY(0);
    animation: slideUp 0.5s ease forwards;
}

.chat-header {
    background: #1a56db;
    color: white;
    padding: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 20px 20px 0 0;
}

.chat-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.chat-header i {
    font-size: 1.4rem;
}

.chat-body {
    padding: 15px;
}

.chat-title {
    font-size: 1.4rem;
    color: #1e293b;
    margin-bottom: 25px;
    font-weight: 500;
}

.chat-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
}

.form-group label i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder {
    color: #94a3b8;
}

.iniciar-chat {
    background: linear-gradient(135deg, var(--primary-color), #1a56db);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

.iniciar-chat i {
    font-size: 1.2rem;
}

.iniciar-chat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
    background: linear-gradient(135deg, #1a56db, var(--primary-color));
}

.iniciar-chat:active {
    transform: translateY(0);
}

/* Área de mensagens */
.messages-container {
    height: 400px;
    overflow-y: auto;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    margin-bottom: 20px;
}

.message {
    max-width: 80%;
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 12px;
    position: relative;
}

.message.user {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message.bot {
    background: white;
    color: var(--text-color);
    margin-right: auto;
    border-bottom-left-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.message-content {
    font-size: 0.95rem;
    line-height: 1.4;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 4px;
}

/* Área de input */
.chat-input-area {
    display: flex;
    gap: 12px;
    padding: 15px;
    background: white;
    border-top: 1px solid var(--border-color);
    position: relative;
}

#message-input {
    flex: 1;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    resize: none;
    max-height: 120px;
    font-size: 0.95rem;
    line-height: 1.4;
    transition: all 0.3s ease;
}

#message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.send-button {
    width: 46px;
    height: 46px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-button:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

.send-button:active {
    transform: translateY(0);
}

.send-button i {
    font-size: 1.2rem;
}

/* Botão flutuante do chat */
#chatButton {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

#openChat {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #1a56db;
    color: white;
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    justify-content: center;
}

#openChat i {
    font-size: 24px;
}

#openChat span {
    position: absolute;
    background: white;
    color: #1e293b;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    left: calc(100% + 10px);
    white-space: nowrap;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

#openChat span::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    border-style: solid;
    border-width: 6px 6px 6px 0;
    border-color: transparent white transparent transparent;
}

#openChat:hover {
    transform: scale(1.1);
    background: #1e40af;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

#openChat:hover span {
    opacity: 1;
    transform: translateX(0);
}

#openChat:active {
    transform: scale(1.05);
}

/* Animação de entrada */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 480px) {
    .chat-container {
        border-radius: 16px;
        margin: 10px;
    }

    .chat-header {
        padding: 16px 20px;
    }

    .chat-body {
        padding: 20px;
    }

    .form-group input {
        padding: 10px 14px;
    }

    .iniciar-chat {
        padding: 12px 24px;
    }
}
