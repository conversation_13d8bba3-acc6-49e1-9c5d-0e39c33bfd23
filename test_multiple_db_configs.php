<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Múltiplas Configurações de Banco</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background: #101010; color: #fff; }
        .card { background: #2a2a2a; border: 1px solid #444; }
        .card-header { background: #333; border-bottom: 1px solid #444; }
        .test-success { background: #1a4a1a; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
        .test-error { background: #4a1a1a; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }
        .test-warning { background: #4a4a1a; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; }
        .config-block { background: #000; color: #0ff; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-database"></i> Teste de Configurações de Banco de Dados</h2>
                        <p class="mb-0">Testando diferentes combinações de credenciais para u276254152_banco_loja</p>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        // Configurações para testar
                        $configs = [
                            [
                                'name' => 'Configuração 1: root/Vasia',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'root',
                                'password' => 'Vasia'
                            ],
                            [
                                'name' => 'Configuração 2: u276254152_banco_loja/Vasia',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'u276254152_banco_loja',
                                'password' => 'Vasia'
                            ],
                            [
                                'name' => 'Configuração 3: root/vasia (minúsculo)',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'root',
                                'password' => 'vasia'
                            ],
                            [
                                'name' => 'Configuração 4: u276254152_banco_loja/vasia',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'u276254152_banco_loja',
                                'password' => 'vasia'
                            ],
                            [
                                'name' => 'Configuração 5: root/senha vazia',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'root',
                                'password' => ''
                            ],
                            [
                                'name' => 'Configuração 6: u276254152_banco_loja/senha vazia',
                                'host' => 'localhost',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'u276254152_banco_loja',
                                'password' => ''
                            ],
                            [
                                'name' => 'Configuração 7: 127.0.0.1 como host',
                                'host' => '127.0.0.1',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'root',
                                'password' => 'Vasia'
                            ],
                            [
                                'name' => 'Configuração 8: 127.0.0.1 + user específico',
                                'host' => '127.0.0.1',
                                'dbname' => 'u276254152_banco_loja',
                                'username' => 'u276254152_banco_loja',
                                'password' => 'Vasia'
                            ]
                        ];
                        
                        $successConfig = null;
                        
                        foreach ($configs as $index => $config) {
                            echo "<h5><i class='fas fa-cog'></i> {$config['name']}</h5>";
                            echo "<div class='config-block'>";
                            echo "Host: {$config['host']}<br>";
                            echo "Database: {$config['dbname']}<br>";
                            echo "Username: {$config['username']}<br>";
                            echo "Password: " . (empty($config['password']) ? '(vazia)' : $config['password']) . "<br>";
                            echo "</div>";
                            
                            try {
                                $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
                                $pdo = new PDO($dsn, $config['username'], $config['password'], [
                                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                                    PDO::ATTR_TIMEOUT => 5
                                ]);
                                
                                // Testar uma query simples
                                $stmt = $pdo->query("SELECT 1 as test");
                                $result = $stmt->fetch();
                                
                                // Obter informações do banco
                                $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version, USER() as current_user");
                                $info = $stmt->fetch();
                                
                                // Listar tabelas
                                $stmt = $pdo->query("SHOW TABLES");
                                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                
                                echo "<div class='test-success'>";
                                echo "<h6><i class='fas fa-check-circle'></i> ✅ CONEXÃO ESTABELECIDA COM SUCESSO!</h6>";
                                echo "<strong>Informações do Banco:</strong><br>";
                                echo "• Database: {$info['db_name']}<br>";
                                echo "• Versão MySQL: {$info['version']}<br>";
                                echo "• Usuário atual: {$info['current_user']}<br>";
                                echo "• Tabelas encontradas: " . count($tables) . "<br>";
                                
                                if (count($tables) > 0) {
                                    echo "• Lista de tabelas: " . implode(', ', $tables) . "<br>";
                                }
                                
                                echo "<div class='mt-3'>";
                                echo "<strong>🎯 Esta é a configuração correta!</strong><br>";
                                echo "<button class='btn btn-success btn-sm' onclick='copyConfig({$index})'>Copiar Configuração</button>";
                                echo "</div>";
                                echo "</div>";
                                
                                $successConfig = $config;
                                break; // Para no primeiro sucesso
                                
                            } catch (PDOException $e) {
                                echo "<div class='test-error'>";
                                echo "<h6><i class='fas fa-times-circle'></i> ❌ FALHA NA CONEXÃO</h6>";
                                echo "<strong>Erro:</strong> " . $e->getMessage() . "<br>";
                                
                                // Análise do erro
                                if (strpos($e->getMessage(), 'Access denied') !== false) {
                                    echo "<strong>Diagnóstico:</strong> Credenciais incorretas (usuário/senha)<br>";
                                } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
                                    echo "<strong>Diagnóstico:</strong> Banco de dados não existe<br>";
                                } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
                                    echo "<strong>Diagnóstico:</strong> Servidor MySQL não está rodando ou host incorreto<br>";
                                } else {
                                    echo "<strong>Diagnóstico:</strong> Erro desconhecido<br>";
                                }
                                echo "</div>";
                            }
                            
                            echo "<hr>";
                        }
                        
                        if ($successConfig) {
                            echo "<div class='alert alert-success mt-4'>";
                            echo "<h4><i class='fas fa-trophy'></i> Configuração Encontrada!</h4>";
                            echo "<p>A configuração correta foi identificada. Use os dados abaixo:</p>";
                            echo "<div class='config-block'>";
                            echo "private const DB_HOST = '{$successConfig['host']}';<br>";
                            echo "private const DB_NAME = '{$successConfig['dbname']}';<br>";
                            echo "private const DB_USER = '{$successConfig['username']}';<br>";
                            echo "private const DB_PASS = '{$successConfig['password']}';<br>";
                            echo "</div>";
                            echo "<button class='btn btn-primary' onclick='updateConfig()'>Atualizar Configuração Automaticamente</button>";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-danger mt-4'>";
                            echo "<h4><i class='fas fa-exclamation-triangle'></i> Nenhuma Configuração Funcionou</h4>";
                            echo "<p><strong>Possíveis problemas:</strong></p>";
                            echo "<ul>";
                            echo "<li>MySQL não está rodando</li>";
                            echo "<li>Banco de dados não existe</li>";
                            echo "<li>Credenciais incorretas</li>";
                            echo "<li>Permissões de acesso negadas</li>";
                            echo "</ul>";
                            echo "<p><strong>Sugestões:</strong></p>";
                            echo "<ul>";
                            echo "<li>Verifique se o XAMPP/MySQL está rodando</li>";
                            echo "<li>Confirme as credenciais no painel de controle do hosting</li>";
                            echo "<li>Teste criar o banco manualmente no phpMyAdmin</li>";
                            echo "</ul>";
                            echo "</div>";
                        }
                        ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const configs = <?php echo json_encode($configs); ?>;
        const successConfig = <?php echo json_encode($successConfig); ?>;
        
        function copyConfig(index) {
            const config = configs[index];
            const text = `Host: ${config.host}
Database: ${config.dbname}
Username: ${config.username}
Password: ${config.password}`;
            
            navigator.clipboard.writeText(text).then(() => {
                alert('Configuração copiada para a área de transferência!');
            });
        }
        
        function updateConfig() {
            if (!successConfig) {
                alert('Nenhuma configuração válida encontrada!');
                return;
            }
            
            if (confirm('Deseja atualizar automaticamente o arquivo de configuração?')) {
                fetch('update_db_config.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(successConfig)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Configuração atualizada com sucesso!');
                        location.reload();
                    } else {
                        alert('Erro ao atualizar configuração: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Erro na requisição: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
