<?php
require_once 'database/connection.php';
require_once 'config/asaas.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Recebe os dados do POST
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['payment_id']) || !isset($data['status'])) {
        throw new Exception('Dados inválidos');
    }
    
    // Atualiza o status do pagamento
    $stmt = $pdo->prepare("UPDATE orders SET payment_status = :status WHERE payment_id = :payment_id");
    $stmt->execute([
        ':status' => $data['status'],
        ':payment_id' => $data['payment_id']
    ]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'Status atualizado com sucesso']);
    } else {
        throw new Exception('Pagamento não encontrado');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}

