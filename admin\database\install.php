<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Read and execute SQL file
    $sql = file_get_contents(__DIR__ . '/tables.sql');
    $pdo->exec($sql);
    
    // Check if admin user exists, if not create one
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, role) 
            VALUES (?, ?, ?, 'admin')
        ");
        $stmt->execute([
            'Administrador',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT)
        ]);
    }
    
    echo "Database tables created successfully!";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
