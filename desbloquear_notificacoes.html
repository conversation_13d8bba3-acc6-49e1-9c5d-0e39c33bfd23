<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Como Desbloquear Notificações - KESUNG SITE</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #4318FF;
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .alert {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        
        .browser-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 5px solid #4318FF;
        }
        
        .step {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .step-number {
            background: #4318FF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-content h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .step-content p {
            margin: 0;
            color: #666;
        }
        
        .visual-guide {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .browser-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .chrome { color: #4285f4; }
        .firefox { color: #ff9500; }
        .safari { color: #007aff; }
        .edge { color: #0078d4; }
        
        .btn {
            background: #4318FF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: #3612d4;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .test-section {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .faq {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .faq h4 {
            color: #4318FF;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 24px;
                flex-direction: column;
                gap: 10px;
            }
            
            .step {
                flex-direction: column;
                text-align: center;
            }
            
            .step-number {
                align-self: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            <i class="fas fa-bell"></i>
            Como Desbloquear Notificações
        </h1>
        
        <div class="alert">
            <strong><i class="fas fa-info-circle"></i> Importante:</strong>
            Para receber notificações de promoções, ofertas especiais e atualizações do KESUNG SITE, você precisa permitir notificações no seu navegador.
        </div>

        <div class="test-section">
            <h3>🧪 Teste Rápido</h3>
            <p>Clique no botão abaixo para testar se as notificações estão funcionando:</p>
            <button class="btn" onclick="testNotification()">
                <i class="fas fa-bell"></i>
                Testar Notificação
            </button>
            <div id="test-result" style="margin-top: 15px;"></div>
        </div>

        <!-- Chrome -->
        <div class="browser-section">
            <h3><i class="fas fa-globe chrome browser-icon"></i>Google Chrome</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Clique no ícone de cadeado ou informações</h4>
                    <p>Na barra de endereços, clique no ícone de cadeado (🔒) ou informações (ℹ️) ao lado da URL.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Encontre "Notificações"</h4>
                    <p>No menu que abrir, procure por "Notificações" na lista de permissões.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Altere para "Permitir"</h4>
                    <p>Clique no dropdown ao lado de "Notificações" e selecione "Permitir".</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h4>Recarregue a página</h4>
                    <p>Pressione F5 ou clique no botão de recarregar para aplicar as mudanças.</p>
                </div>
            </div>
        </div>

        <!-- Firefox -->
        <div class="browser-section">
            <h3><i class="fab fa-firefox firefox browser-icon"></i>Mozilla Firefox</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Clique no ícone de escudo ou cadeado</h4>
                    <p>Na barra de endereços, clique no ícone de escudo ou cadeado.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Acesse "Permissões"</h4>
                    <p>Clique em "Permissões" ou no ícone de engrenagem.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Ative as notificações</h4>
                    <p>Encontre "Notificações" e altere para "Permitir".</p>
                </div>
            </div>
        </div>

        <!-- Safari -->
        <div class="browser-section">
            <h3><i class="fab fa-safari safari browser-icon"></i>Safari (iPhone/iPad)</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Abra Configurações do iOS</h4>
                    <p>Vá para Configurações > Safari > Configurações para Sites.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Encontre "Notificações"</h4>
                    <p>Procure por "Notificações" na lista de configurações.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Permita para este site</h4>
                    <p>Altere a configuração para "Permitir" para o KESUNG SITE.</p>
                </div>
            </div>
        </div>

        <!-- Edge -->
        <div class="browser-section">
            <h3><i class="fab fa-edge edge browser-icon"></i>Microsoft Edge</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Clique no ícone de cadeado</h4>
                    <p>Na barra de endereços, clique no ícone de cadeado.</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Acesse "Permissões para este site"</h4>
                    <p>Clique em "Permissões para este site".</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Ative notificações</h4>
                    <p>Encontre "Notificações" e altere para "Permitir".</p>
                </div>
            </div>
        </div>

        <div class="faq">
            <h4><i class="fas fa-question-circle"></i> Perguntas Frequentes</h4>
            
            <p><strong>P: Por que preciso permitir notificações?</strong></p>
            <p>R: Para receber alertas sobre promoções exclusivas, ofertas especiais e atualizações importantes do KESUNG SITE.</p>
            
            <p><strong>P: As notificações vão incomodar?</strong></p>
            <p>R: Não! Enviamos apenas notificações relevantes e você pode desativar a qualquer momento.</p>
            
            <p><strong>P: É seguro permitir notificações?</strong></p>
            <p>R: Sim! Não coletamos dados pessoais e você mantém controle total sobre as notificações.</p>
        </div>

        <div class="success">
            <h3><i class="fas fa-check-circle"></i> Após Desbloquear</h3>
            <p>Depois de permitir as notificações:</p>
            <ul>
                <li>✅ Você receberá uma notificação de boas-vindas</li>
                <li>✅ Será alertado sobre promoções especiais</li>
                <li>✅ Receberá atualizações importantes</li>
                <li>✅ Poderá desativar a qualquer momento</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">
                <i class="fas fa-home"></i>
                Voltar ao Site
            </a>
            <button class="btn btn-secondary" onclick="testNotification()">
                <i class="fas fa-bell"></i>
                Testar Novamente
            </button>
        </div>
    </div>

    <script>
        async function testNotification() {
            const resultDiv = document.getElementById('test-result');
            
            if (!('Notification' in window)) {
                resultDiv.innerHTML = '<div style="color: #dc3545;">❌ Seu navegador não suporta notificações.</div>';
                return;
            }
            
            const permission = Notification.permission;
            
            if (permission === 'granted') {
                new Notification('Teste - KESUNG SITE', {
                    body: 'Perfeito! As notificações estão funcionando!',
                    icon: '/assets/icons/icon-192x192.png',
                    tag: 'test-notification'
                });
                resultDiv.innerHTML = '<div style="color: #28a745;">✅ Notificações funcionando perfeitamente!</div>';
            } else if (permission === 'denied') {
                resultDiv.innerHTML = '<div style="color: #dc3545;">❌ Notificações bloqueadas. Siga as instruções acima para desbloquear.</div>';
            } else {
                const newPermission = await Notification.requestPermission();
                if (newPermission === 'granted') {
                    new Notification('Teste - KESUNG SITE', {
                        body: 'Obrigado! Agora você receberá nossas notificações!',
                        icon: '/assets/icons/icon-192x192.png',
                        tag: 'welcome-notification'
                    });
                    resultDiv.innerHTML = '<div style="color: #28a745;">✅ Notificações ativadas com sucesso!</div>';
                } else {
                    resultDiv.innerHTML = '<div style="color: #dc3545;">❌ Permissão negada. Siga as instruções acima.</div>';
                }
            }
        }
        
        // Testar automaticamente ao carregar a página
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testNotification, 1000);
        });
    </script>
</body>
</html>
