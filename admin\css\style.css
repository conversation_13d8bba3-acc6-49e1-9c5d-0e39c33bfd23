:root {
    --primary-color: #198754;
    --primary-hover: #157347;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --white-color: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background-color: var(--light-color);
}

.wrapper {
    display: flex;
}

#sidebar-wrapper {
    min-height: 100vh;
    width: 250px;
    margin-left: 0;
    transition: margin 0.25s ease-out;
    background-color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

#sidebar-wrapper .sidebar-heading {
    padding: 1.5rem 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid #dee2e6;
}

#sidebar-wrapper .list-group {
    width: 100%;
}

#sidebar-wrapper .list-group-item {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

#sidebar-wrapper .list-group-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

#sidebar-wrapper .list-group-item:hover {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--primary-color);
    text-decoration: none;
}

#sidebar-wrapper .list-group-item.active {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

#sidebar-wrapper .list-group-item.active i {
    color: white !important;
}

/* Garantir que todos os itens do menu tenham o mesmo estilo */
#sidebar-wrapper .list-group-item {
    border: none !important;
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    color: var(--dark-color) !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

#page-content-wrapper {
    min-width: 100vw;
    padding: 20px;
}

.navbar {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem;
}

.navbar .navbar-toggler {
    padding: 0.25rem 0.75rem;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.card {
    border: none;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    background-color: var(--light-color);
    font-weight: 600;
}

.badge {
    padding: 8px 12px;
    font-weight: 500;
}

.badge-success {
    background-color: var(--primary-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.status-badge {
    padding: 0.5em 1em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-pending { background-color: #ffeeba; color: #856404; }
.status-completed { background-color: #d4edda; color: #155724; }
.status-cancelled { background-color: #f8d7da; color: #721c24; }

.dashboard-card {
    background: var(--white-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
}

.dashboard-card .icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.dashboard-card .number {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--dark-color);
}

.dashboard-card .label {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.chart-container {
    background: var(--white-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -15rem;
    }
}

@media (max-width: 767.98px) {
    #sidebar-wrapper {
        margin-left: -15rem;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0;
    }
}
