<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_credentials'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE payment_credentials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                public_key VARCHAR(255),
                access_token VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $public_key = $_POST['public_key'];
    $access_token = $_POST['access_token'];

    // Verifica se já existe registro
    try {
        $stmt = $pdo->query("SELECT id FROM payment_credentials LIMIT 1");
        $exists = $stmt->fetch();
    } catch (Exception $e) {
        $exists = false;
    }
    
    try {
        if ($exists) {
            // Atualiza
            $stmt = $pdo->prepare("UPDATE payment_credentials SET public_key = ?, access_token = ? WHERE id = ?");
            $stmt->execute([$public_key, $access_token, $exists['id']]);
        } else {
            // Insere
            $stmt = $pdo->prepare("INSERT INTO payment_credentials (public_key, access_token) VALUES (?, ?)");
            $stmt->execute([$public_key, $access_token]);
        }
        echo "<script>
            Swal.fire({
                title: 'Configurações salvas com sucesso!',
                icon: 'success',
                confirmButtonColor: '#0d6efd'
            });
        </script>";
    } catch (PDOException $e) {
        echo "<script>
            Swal.fire({
                title: 'Erro ao salvar as configurações',
                icon: 'error',
                confirmButtonColor: '#0d6efd'
            });
        </script>";
    }
}

// Buscar credenciais atuais
try {
    $stmt = $pdo->query("SELECT * FROM payment_credentials LIMIT 1");
    $credentials = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $credentials = [];
}

require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Configurações de Pagamento</h2>

    <div class="card">
        <div class="card-body">
            <form method="POST" action="">
                        <div class="form-group">
                            <label for="public_key">Chave Pública (Public Key)</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="public_key" 
                                   name="public_key" 
                                   value="<?php echo htmlspecialchars($credentials['public_key'] ?? ''); ?>"
                                   required>
                            <small class="form-text text-muted">
                                Chave pública fornecida pelo provedor de pagamento
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="access_token">Token de Acesso (Access Token)</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="access_token" 
                                   name="access_token" 
                                   value="<?php echo htmlspecialchars($credentials['access_token'] ?? ''); ?>"
                                   required>
                            <small class="form-text text-muted">
                                Token de acesso para autenticação com a API de pagamento
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label>Última Atualização</label>
                            <p class="form-control-static">
                                <?php 
                                if (isset($credentials['updated_at'])) {
                                    echo date('d/m/Y H:i:s', strtotime($credentials['updated_at']));
                                } else {
                                    echo 'Nunca atualizado';
                                }
                                ?>
                            </p>
                        </div>
                        
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Salvar Configurações
                </button>
            </form>

            <hr class="my-4">

            <div class="alert alert-info">
                <h5>
                    <i class="fas fa-info-circle"></i>
                    Como configurar
                </h5>
                <ol class="mb-0">
                    <li>Acesse sua conta no provedor de pagamento (Asaas)</li>
                    <li>Vá até as configurações de API/Integração</li>
                    <li>Copie a chave pública e o token de acesso</li>
                    <li>Cole as informações nos campos acima</li>
                    <li>Clique em "Salvar Configurações"</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<script>
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    Swal.fire({
        title: "Credenciais salvas com sucesso!",
        icon: "success",
        draggable: true
    }).then((result) => {
        if (result.isConfirmed) {
            this.submit();
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>

