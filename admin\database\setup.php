<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Criar banco de dados se não existir
    $pdo->exec("CREATE DATABASE IF NOT EXISTS lojaatt_leaut CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Banco de dados criado com sucesso\n";
    
    // Selecionar o banco de dados
    $pdo->exec("USE lojaatt_leaut");
    
    // Criar tabela de configurações do site
    $pdo->exec("CREATE TABLE IF NOT EXISTS `site_settings` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `title` VARCHAR(255),
        `description` TEXT,
        `logo_path` VARCHAR(255),
        `primary_color` VARCHAR(7) DEFAULT '#4169E1',
        `footer_color` VARCHAR(7) DEFAULT '#1B2838',
        `button_gradient_start` VARCHAR(7) DEFAULT '#28a745',
        `button_gradient_end` VARCHAR(7) DEFAULT '#218838',
        `header_gradient_start` VARCHAR(7) DEFAULT '#28a745',
        `header_gradient_end` VARCHAR(7) DEFAULT '#4169E1',
        `whatsapp_number` VARCHAR(20),
        `contact_email` VARCHAR(255),
        `instagram_url` VARCHAR(255),
        `facebook_url` VARCHAR(255),
        `analytics_code` TEXT,
        `youtube_url` VARCHAR(255),
        `footer_text` VARCHAR(255) DEFAULT ' 2024 Sistema de Vendas',
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ Tabela site_settings criada com sucesso\n";

    // Criar tabela de admins
    $pdo->exec("CREATE TABLE IF NOT EXISTS `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ Tabela admins criada com sucesso\n";
    
    // Criar tabela de produtos
    $pdo->exec("CREATE TABLE IF NOT EXISTS `produtos` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `description` text,
        `price` decimal(10,2) NOT NULL,
        `image_url` varchar(255) DEFAULT NULL,
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ Tabela produtos criada com sucesso\n";
    
    // Inserir configurações padrão se não existirem
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM site_settings");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO site_settings (
            title, 
            primary_color, 
            footer_color, 
            button_gradient_start, 
            button_gradient_end, 
            header_gradient_start, 
            header_gradient_end,
            footer_text
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'Sistema de Vendas',
            '#4169E1',
            '#1B2838',
            '#28a745',
            '#218838',
            '#28a745',
            '#4169E1',
            ' 2024 Sistema de Vendas'
        ]);
        echo "✓ Configurações padrão criadas com sucesso\n";
    }

    // Inserir admin padrão se não existir
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admins WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO admins (name, email, password) VALUES (?, ?, ?)");
        $stmt->execute([
            'Administrador',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT)
        ]);
        echo "✓ Admin padrão criado com sucesso\n";
    }
    
    // Inserir produtos de exemplo se não existirem
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM produtos");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $produtos = [
            ['Produto 1', 'Descrição do produto 1', 99.99, 'assets/images/produto1.jpg'],
            ['Produto 2', 'Descrição do produto 2', 149.99, 'assets/images/produto2.jpg'],
            ['Produto 3', 'Descrição do produto 3', 199.99, 'assets/images/produto3.jpg']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO produtos (name, description, price, image_url) VALUES (?, ?, ?, ?)");
        foreach ($produtos as $produto) {
            $stmt->execute($produto);
        }
        echo "✓ Produtos de exemplo criados com sucesso\n";
    }
    
    echo "\nConfiguração concluída com sucesso!\n";
    echo "Email: <EMAIL>\n";
    echo "Senha: admin123\n";
    
} catch (PDOException $e) {
    die("Erro na configuração: " . $e->getMessage() . "\n");
}
