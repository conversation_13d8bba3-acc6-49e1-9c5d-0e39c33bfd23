<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemplo: Progresso de Instalação PWA - KESUNG SITE</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #4318FF;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4318FF;
        }
        
        .mockup {
            position: relative;
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 150px;
        }
        
        /* Estilos do Banner PWA */
        .pwa-install-banner-compact {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #4318FF, #9333EA);
            color: white;
            z-index: 9999;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(67, 24, 255, 0.3);
            max-width: 280px;
            width: auto;
        }

        .pwa-banner-content-compact {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            gap: 12px;
        }

        .pwa-banner-info {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .pwa-icon {
            font-size: 18px;
            color: white;
            flex-shrink: 0;
        }

        .pwa-text {
            font-size: 14px;
            font-weight: 500;
            color: white;
            white-space: nowrap;
        }

        .pwa-banner-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pwa-install-btn-compact {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Barra de Progresso */
        .pwa-progress-container {
            width: 80px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .pwa-progress-bar {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .pwa-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #fff, #e0e0e0);
            border-radius: 2px;
            width: 30%;
            transition: width 0.8s ease;
            animation: progressPulse 1.5s infinite;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Banner de Sucesso */
        .pwa-success-banner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            z-index: 10000;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(16, 185, 129, 0.4);
            animation: successSlideIn 0.5s ease-out;
            padding: 20px 30px;
            text-align: center;
            min-width: 280px;
        }

        .pwa-success-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .pwa-success-icon {
            font-size: 32px;
            color: white;
            margin-bottom: 5px;
        }

        .pwa-success-text {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }

        .pwa-success-subtitle {
            font-size: 12px;
            color: rgba(255,255,255,0.9);
            margin-top: 5px;
        }

        @keyframes successSlideIn {
            from {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 0;
            }
            to {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
        }
        
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
        }
        
        .demo-button {
            background: #4318FF;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #3612d4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Progresso de Instalação PWA - KESUNG SITE</h1>
        
        <div class="highlight">
            <strong>🎯 Funcionalidade:</strong> Quando o usuário clica em "Instalar", mostra progresso visual e abre o app automaticamente após instalação.
        </div>

        <h2>📋 Fluxo de Instalação</h2>
        
        <div class="step">
            <strong>1.</strong> Usuário clica no botão "Instalar"
        </div>
        
        <div class="step">
            <strong>2.</strong> Banner muda para mostrar "Preparando instalação..." com barra de progresso
        </div>
        
        <div class="step">
            <strong>3.</strong> Sistema solicita confirmação do navegador
        </div>
        
        <div class="step">
            <strong>4.</strong> Progresso atualiza para "Instalando app..." (70%)
        </div>
        
        <div class="step">
            <strong>5.</strong> Progresso completa "Instalação concluída!" (100%)
        </div>
        
        <div class="step">
            <strong>6.</strong> Mostra banner de sucesso "App instalado com sucesso!"
        </div>
        
        <div class="step">
            <strong>7.</strong> Abre o app automaticamente
        </div>

        <h2>📱 Demonstração Visual</h2>
        
        <h3>Etapa 1: Banner Inicial</h3>
        <div class="mockup">
            <div style="color: #666; text-align: center; padding: 20px 0;">
                Conteúdo da página...
            </div>
            
            <div class="pwa-install-banner-compact">
                <div class="pwa-banner-content-compact">
                    <div class="pwa-banner-info">
                        <i class="fas fa-mobile-alt pwa-icon"></i>
                        <span class="pwa-text">Instale nosso app</span>
                    </div>
                    <div class="pwa-banner-actions">
                        <button class="pwa-install-btn-compact" onclick="showProgress()">
                            <i class="fas fa-download"></i>
                            Instalar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <h3>Etapa 2: Progresso de Instalação</h3>
        <div class="mockup">
            <div style="color: #666; text-align: center; padding: 20px 0;">
                Conteúdo da página...
            </div>
            
            <div class="pwa-install-banner-compact" id="progress-banner" style="display: none;">
                <div class="pwa-banner-content-compact">
                    <div class="pwa-banner-info">
                        <i class="fas fa-mobile-alt pwa-icon"></i>
                        <span class="pwa-text" id="progress-text">Preparando instalação...</span>
                    </div>
                    <div class="pwa-progress-container">
                        <div class="pwa-progress-bar">
                            <div class="pwa-progress-fill" id="progress-fill"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h3>Etapa 3: Sucesso</h3>
        <div class="mockup">
            <div style="color: #666; text-align: center; padding: 20px 0;">
                Conteúdo da página...
            </div>
            
            <div class="pwa-success-banner" id="success-banner" style="display: none;">
                <div class="pwa-success-content">
                    <i class="fas fa-check-circle pwa-success-icon"></i>
                    <span class="pwa-success-text">App instalado com sucesso!</span>
                    <small class="pwa-success-subtitle">Abrindo aplicativo...</small>
                </div>
            </div>
        </div>

        <h2>🧪 Testar Demonstração</h2>
        
        <div class="demo-section">
            <p>Clique nos botões abaixo para simular o fluxo de instalação:</p>
            
            <button class="demo-button" onclick="showProgress()">
                1. Mostrar Progresso
            </button>
            
            <button class="demo-button" onclick="updateProgress()">
                2. Atualizar Progresso
            </button>
            
            <button class="demo-button" onclick="showSuccess()">
                3. Mostrar Sucesso
            </button>
            
            <button class="demo-button" onclick="resetDemo()">
                🔄 Resetar Demo
            </button>
        </div>

        <div class="success">
            <h3>✅ Benefícios da Funcionalidade</h3>
            <ul>
                <li>🎯 <strong>Feedback Visual:</strong> Usuário vê o progresso da instalação</li>
                <li>📱 <strong>Abertura Automática:</strong> App abre sozinho após instalação</li>
                <li>✨ <strong>Experiência Fluida:</strong> Transição suave do site para o app</li>
                <li>🔔 <strong>Confirmação:</strong> Mensagem de sucesso clara</li>
                <li>⚡ <strong>Rápido:</strong> Processo otimizado e eficiente</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.location.href='/'" class="demo-button" style="font-size: 16px; padding: 12px 24px;">
                Testar no Site Real
            </button>
        </div>
    </div>

    <script>
        function showProgress() {
            document.getElementById('progress-banner').style.display = 'block';
            document.getElementById('progress-text').textContent = 'Preparando instalação...';
            document.getElementById('progress-fill').style.width = '30%';
        }

        function updateProgress() {
            document.getElementById('progress-text').textContent = 'Instalando app...';
            document.getElementById('progress-fill').style.width = '70%';
            
            setTimeout(() => {
                document.getElementById('progress-text').textContent = 'Instalação concluída!';
                document.getElementById('progress-fill').style.width = '100%';
            }, 1000);
        }

        function showSuccess() {
            document.getElementById('progress-banner').style.display = 'none';
            document.getElementById('success-banner').style.display = 'block';
            
            setTimeout(() => {
                document.getElementById('success-banner').style.display = 'none';
            }, 3000);
        }

        function resetDemo() {
            document.getElementById('progress-banner').style.display = 'none';
            document.getElementById('success-banner').style.display = 'none';
            document.getElementById('progress-fill').style.width = '30%';
        }
    </script>
</body>
</html>
