<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit037d16d59436bd5210e55c489910989a
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
        'M' => 
        array (
            'MercadoPago\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'MercadoPago\\' => 
        array (
            0 => __DIR__ . '/..' . '/mercadopago/dx-php/src/MercadoPago',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit037d16d59436bd5210e55c489910989a::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit037d16d59436bd5210e55c489910989a::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit037d16d59436bd5210e55c489910989a::$classMap;

        }, null, ClassLoader::class);
    }
}
