<?php
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método inválido');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['host'], $input['dbname'], $input['username'], $input['password'])) {
        throw new Exception('Dados de configuração inválidos');
    }
    
    $configFile = 'database/connection.php';
    
    if (!file_exists($configFile)) {
        throw new Exception('Arquivo de configuração não encontrado');
    }
    
    $content = file_get_contents($configFile);
    
    // Substituir as configurações
    $content = preg_replace(
        "/private const DB_HOST = '[^']*';/",
        "private const DB_HOST = '{$input['host']}';",
        $content
    );
    
    $content = preg_replace(
        "/private const DB_NAME = '[^']*';/",
        "private const DB_NAME = '{$input['dbname']}';",
        $content
    );
    
    $content = preg_replace(
        "/private const DB_USER = '[^']*';/",
        "private const DB_USER = '{$input['username']}';",
        $content
    );
    
    $content = preg_replace(
        "/private const DB_PASS = '[^']*';/",
        "private const DB_PASS = '{$input['password']}';",
        $content
    );
    
    // Salvar o arquivo
    if (file_put_contents($configFile, $content) === false) {
        throw new Exception('Erro ao salvar arquivo de configuração');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Configuração atualizada com sucesso',
        'config' => $input
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
