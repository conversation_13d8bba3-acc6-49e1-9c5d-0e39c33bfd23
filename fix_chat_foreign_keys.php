<?php
/**
 * Script para corrigir foreign keys do sistema de chat
 */

require_once 'database/connection.php';

try {
    echo "<h2>🔧 Corrigindo Foreign Keys do Sistema de Chat</h2>";
    
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h3>1. Removendo foreign keys conflitantes...</h3>";
    
    // Remover foreign key existente se houver
    try {
        $pdo->exec("ALTER TABLE chat_messages DROP FOREIGN KEY chat_messages_ibfk_1");
        echo "<p>✅ Foreign key antiga removida</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ Foreign key não existia ou já foi removida</p>";
    }
    
    echo "<h3>2. Verificando estrutura das tabelas...</h3>";
    
    // Verificar se chat_users existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'chat_users'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela chat_users não existe. Criando...</p>";
        
        $pdo->exec("
            CREATE TABLE chat_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                whatsapp VARCHAR(20),
                is_admin TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_is_admin (is_admin),
                INDEX idx_last_activity (last_activity)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        echo "<p>✅ Tabela chat_users criada</p>";
    } else {
        echo "<p>✅ Tabela chat_users existe</p>";
    }
    
    // Verificar se chat_messages existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'chat_messages'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela chat_messages não existe. Criando...</p>";
        
        $pdo->exec("
            CREATE TABLE chat_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                message TEXT NOT NULL,
                is_admin TINYINT(1) DEFAULT 0,
                timestamp INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_timestamp (timestamp),
                INDEX idx_is_admin (is_admin)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        echo "<p>✅ Tabela chat_messages criada</p>";
    } else {
        echo "<p>✅ Tabela chat_messages existe</p>";
    }
    
    echo "<h3>3. Adicionando foreign key correta...</h3>";
    
    // Adicionar foreign key correta
    try {
        $pdo->exec("
            ALTER TABLE chat_messages 
            ADD CONSTRAINT fk_chat_messages_user_id 
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ");
        echo "<p>✅ Foreign key correta adicionada</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ Erro ao adicionar foreign key: " . $e->getMessage() . "</p>";
        
        // Tentar sem foreign key se houver problemas
        echo "<p>🔄 Removendo foreign key para evitar conflitos...</p>";
        try {
            $pdo->exec("ALTER TABLE chat_messages DROP FOREIGN KEY fk_chat_messages_user_id");
        } catch (Exception $e2) {
            // Ignorar se não existir
        }
    }
    
    echo "<h3>4. Migrando dados se necessário...</h3>";
    
    // Verificar se há usuários em users que precisam ser migrados para chat_users
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $usersCount = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users");
        $chatUsersCount = $stmt->fetchColumn();
        
        echo "<p>📊 Usuários na tabela 'users': $usersCount</p>";
        echo "<p>📊 Usuários na tabela 'chat_users': $chatUsersCount</p>";
        
        if ($usersCount > 0 && $chatUsersCount == 0) {
            echo "<p>🔄 Migrando usuários de 'users' para 'chat_users'...</p>";
            
            $pdo->exec("
                INSERT INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity)
                SELECT 
                    COALESCE(username, 'Usuário'), 
                    email, 
                    '', 
                    is_admin, 
                    created_at, 
                    last_activity
                FROM users
                WHERE email NOT IN (SELECT email FROM chat_users)
            ");
            
            $migratedCount = $pdo->lastInsertId();
            echo "<p>✅ $migratedCount usuários migrados</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>⚠️ Erro na migração: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>5. Criando usuários de teste...</h3>";
    
    // Criar usuários de teste se não existirem
    $testUsers = [
        ['Gabriela Coutinho', '<EMAIL>', '11987654321'],
        ['João Silva', '<EMAIL>', '11976543210'],
        ['Maria Santos', '<EMAIL>', '11965432109'],
        ['Pedro Costa', '<EMAIL>', '11954321098'],
        ['Ana Oliveira', '<EMAIL>', '11943210987']
    ];
    
    foreach ($testUsers as $user) {
        try {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) 
                VALUES (?, ?, ?, 0, NOW(), NOW())
            ");
            $stmt->execute($user);
            
            if ($stmt->rowCount() > 0) {
                echo "<p>✅ Usuário criado: {$user[0]}</p>";
            }
        } catch (Exception $e) {
            echo "<p>⚠️ Erro ao criar usuário {$user[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>6. Verificação final...</h3>";
    
    // Verificar estrutura final
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users");
    $chatUsersCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_messages");
    $messagesCount = $stmt->fetchColumn();
    
    echo "<p>📊 Total de usuários de chat: $chatUsersCount</p>";
    echo "<p>📊 Total de mensagens: $messagesCount</p>";
    
    // Testar inserção de mensagem
    if ($chatUsersCount > 0) {
        try {
            $stmt = $pdo->query("SELECT id FROM chat_users LIMIT 1");
            $testUserId = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("
                INSERT INTO chat_messages (user_id, message, is_admin, timestamp) 
                VALUES (?, 'Mensagem de teste', 0, UNIX_TIMESTAMP())
            ");
            $stmt->execute([$testUserId]);
            
            echo "<p>✅ Teste de inserção de mensagem: SUCESSO</p>";
            
            // Remover mensagem de teste
            $pdo->exec("DELETE FROM chat_messages WHERE message = 'Mensagem de teste'");
            
        } catch (Exception $e) {
            echo "<p>❌ Teste de inserção de mensagem: FALHOU - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724; margin-top: 20px;'>";
    echo "<h4>✅ Correção Concluída!</h4>";
    echo "<p>O sistema de chat agora deve funcionar corretamente.</p>";
    echo "<p><strong>Próximo passo:</strong> Teste o chat em <a href='chat.php'>chat.php</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante a correção:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
