<?php
require_once 'includes/auth_check.php';

// Buscar estatísticas
$db = Database::getInstance();
$pdo = $db->getConnection();

// Total de vendas
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count, SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total FROM orders");
    $sales = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $sales = ['count' => 0, 'total' => 0];
}

// Total de produtos
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
$products = $stmt->fetch(PDO::FETCH_ASSOC);

// Total de clientes
$stmt = $pdo->query("SELECT COUNT(DISTINCT customer_email) as count FROM orders");
$customers = $stmt->fetch(PDO::FETCH_ASSOC);

// Vendas recentes
$stmt = $pdo->query("
    SELECT o.*, p.name as product_name
    FROM orders o
    LEFT JOIN products p ON o.product_id = p.id
    ORDER BY o.created_at DESC
    LIMIT 4
");
$recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Produtos mais vendidos
$stmt = $pdo->query("
    SELECT p.*, COUNT(o.id) as sales_count 
    FROM products p 
    LEFT JOIN orders o ON p.id = o.product_id 
    GROUP BY p.id 
    ORDER BY sales_count DESC 
    LIMIT 5
");
$topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para obter estatísticas do chat
function getChatStats($pdo) {
    $stats = [];
    
    // Total de usuários
    $query = "SELECT COUNT(*) as total FROM chat_users WHERE is_admin = 0";
    $stmt = $pdo->query($query);
    $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Usuários ativos hoje
    $query = "SELECT COUNT(DISTINCT user_id) as active_today FROM chat_messages 
              WHERE FROM_UNIXTIME(timestamp) >= CURDATE() AND is_admin = 0";
    $stmt = $pdo->query($query);
    $stats['active_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
    
    // Total de mensagens
    $query = "SELECT COUNT(*) as total FROM chat_messages";
    $stmt = $pdo->query($query);
    $stats['total_messages'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Média de mensagens por conversa
    $query = "SELECT AVG(msg_count) as avg_messages FROM 
              (SELECT COUNT(*) as msg_count FROM chat_messages 
               GROUP BY user_id) as message_counts";
    $stmt = $pdo->query($query);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['avg_messages'] = $result && $result['avg_messages'] ? round($result['avg_messages'], 2) : 0;
    
    return $stats;
}

// Função para obter logs de usuário
function getUserLogs($pdo, $limit = 4) {
    $query = "SELECT 
                cu.id,
                cu.name,
                cu.email,
                cu.whatsapp,
                cu.created_at as registered_at,
                cu.last_activity,
                (SELECT COUNT(*) FROM chat_messages WHERE user_id = cu.id) as message_count,
                (SELECT message FROM chat_messages WHERE user_id = cu.id ORDER BY timestamp DESC LIMIT 1) as last_message
              FROM chat_users cu
              WHERE cu.is_admin = 0
              ORDER BY cu.last_activity DESC
              LIMIT :limit";
              
    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Função auxiliar para formatar data
function formatDateTime($dateStr) {
    return date('d/m/Y H:i', strtotime($dateStr));
}

$stats = getChatStats($pdo);
$userLogs = getUserLogs($pdo);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin</title>
    <link rel="icon" type="image/x-icon" href="/Sistema-Vendas/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link href="css/standardized.css" rel="stylesheet">
    <style>
        /* Reset e Base */
        * {
            box-sizing: border-box;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            padding: 20px;
            max-width: 100%;
        }

        /* Cards do Dashboard */
        .dashboard-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            margin-bottom: 20px;
            height: 100%;
            background: white;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .dashboard-card .card-body {
            padding: 1.25rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 110px;
        }

        /* Ícones dos Cards */
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 15px;
            flex-shrink: 0;
        }

        .sales-icon { background: rgba(67, 24, 255, 0.1); color: #4318FF; }
        .revenue-icon { background: rgba(0, 157, 99, 0.1); color: #009d63; }
        .products-icon { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .customers-icon { background: rgba(220, 53, 69, 0.1); color: #dc3545; }

        /* Cards gerais */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            border-radius: 12px 12px 0 0 !important;
            padding: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* Tabelas */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            font-size: 0.8rem;
            table-layout: fixed;
            width: 100%;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            padding: 0.4rem 0.3rem;
            font-size: 0.75rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
            padding: 0.4rem 0.3rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Badges */
        .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .badge.small {
            font-size: 0.65rem;
            padding: 0.2rem 0.4rem;
        }

        /* Status de usuários */
        .user-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .user-status.active { background-color: #28a745; }
        .user-status.inactive { background-color: #dc3545; }

        /* Lista de usuários do chat */
        .chat-users-list {
            min-height: 280px;
            max-height: 280px;
            overflow-y: auto;
            border-radius: 8px;
            padding: 0;
        }

        .chat-user-item {
            padding: 15px 18px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.2s ease;
            background: white;
            margin: 0;
            display: block;
            width: 100%;
        }

        .chat-user-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .chat-user-item:last-child {
            border-bottom: none;
        }

        /* Ajustes para manter tudo dentro do card */
        .table-container {
            min-height: 280px;
            max-height: 280px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Garantir que a tabela não ultrapasse o card */
        .table-responsive {
            max-width: 100%;
            overflow-x: auto;
        }

        /* Larguras específicas das colunas */
        .table th:nth-child(1), .table td:nth-child(1) { width: 8%; }  /* ID */
        .table th:nth-child(2), .table td:nth-child(2) { width: 20%; } /* Cliente */
        .table th:nth-child(3), .table td:nth-child(3) { width: 18%; } /* Produto */
        .table th:nth-child(4), .table td:nth-child(4) { width: 12%; } /* Valor */
        .table th:nth-child(5), .table td:nth-child(5) { width: 12%; } /* Status */
        .table th:nth-child(6), .table td:nth-child(6) { width: 10%; } /* Método */
        .table th:nth-child(7), .table td:nth-child(7) { width: 12%; } /* Data */
        .table th:nth-child(8), .table td:nth-child(8) { width: 8%; }  /* Ações */

        /* Responsividade */
        @media (max-width: 1199.98px) {
            .container-fluid {
                padding: 15px;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.4rem 0.3rem;
            }
        }

        @media (max-width: 991.98px) {
            .container-fluid {
                padding: 10px;
            }

            .dashboard-card .card-body {
                padding: 1rem;
                min-height: 90px;
            }

            .card-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
                margin-bottom: 10px;
            }

            .table {
                font-size: 0.75rem;
            }

            .table th,
            .table td {
                padding: 0.3rem 0.2rem;
            }

            /* Ocultar algumas colunas em tablets */
            .table th:nth-child(6),
            .table td:nth-child(6) {
                display: none;
            }
        }

        @media (max-width: 767.98px) {
            .container-fluid {
                padding: 8px;
            }

            .dashboard-card {
                margin-bottom: 15px;
            }

            .dashboard-card .card-body {
                padding: 0.875rem;
                min-height: 80px;
            }

            .card-header {
                padding: 1rem;
                font-size: 0.9rem;
            }

            .card-body {
                padding: 1rem;
            }

            .table {
                font-size: 0.7rem;
            }

            .table th,
            .table td {
                padding: 0.25rem 0.15rem;
            }

            /* Ocultar mais colunas em mobile */
            .table th:nth-child(3),
            .table td:nth-child(3),
            .table th:nth-child(6),
            .table td:nth-child(6),
            .table th:nth-child(7),
            .table td:nth-child(7) {
                display: none;
            }

            .chat-user-item {
                padding: 12px 15px;
            }
        }

        @media (max-width: 575.98px) {
            .container-fluid {
                padding: 5px;
            }

            .dashboard-card .card-body {
                padding: 0.75rem;
                min-height: 70px;
            }

            .card-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-bottom: 8px;
            }

            .card-header {
                padding: 0.75rem;
                font-size: 0.85rem;
            }

            .card-body {
                padding: 0.75rem;
            }

            .table {
                font-size: 0.65rem;
            }

            .table th,
            .table td {
                padding: 0.2rem 0.1rem;
            }

            /* Mostrar apenas colunas essenciais */
            .table th:nth-child(n+4),
            .table td:nth-child(n+4) {
                display: none;
            }

            .table th:nth-child(5),
            .table td:nth-child(5),
            .table th:nth-child(8),
            .table td:nth-child(8) {
                display: table-cell;
            }

            .badge {
                font-size: 0.6rem;
                padding: 0.15rem 0.3rem;
            }

            .chat-user-item {
                padding: 10px 12px;
            }

            .btn-sm {
                padding: 0.15rem 0.3rem;
                font-size: 0.65rem;
            }
        }

        /* Melhorias visuais */
        .text-success {
            color: #28a745 !important;
        }

        .fw-bold {
            font-weight: 700 !important;
        }

        .text-muted {
            color: #6c757d !important;
        }




        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(72, 187, 120, 0); }
            100% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0); }
        }

        /* Botão Ver Chat melhorado */
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            font-weight: 600;
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        /* Melhorar aparência dos badges */
        .badge {
            font-size: 0.75rem;
            padding: 0.4em 0.8em;
            border-radius: 20px;
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        .badge.bg-primary {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .badge.bg-secondary {
            background: linear-gradient(45deg, #6c757d, #495057) !important;
            box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
        }



        /* Melhorar truncate de texto */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Valores monetários */
        .text-success {
            color: #28a745 !important;
        }

        .fw-bold {
            font-weight: 700 !important;
        }

        .fw-semibold {
            font-weight: 600 !important;
        }

        /* Avatar circle para tabela */
        .avatar-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.8rem;
            flex-shrink: 0;
        }



        /* Responsividade */
        @media (max-width: 1200px) {
            /* Ocultar colunas menos importantes em telas médias */
            .table th:nth-child(3),
            .table td:nth-child(3),
            .table th:nth-child(9),
            .table td:nth-child(9),
            .table th:nth-child(10),
            .table td:nth-child(10) {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 10px;
            }

            .dashboard-card .card-body {
                padding: 1rem;
                min-height: 100px;
            }

            .card-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
                margin-bottom: 10px;
            }

            .table-responsive {
                font-size: 0.8rem;
            }

            /* Em mobile, empilhar as seções */
            .row.g-3 .col-lg-8,
            .row.g-3 .col-lg-4 {
                margin-bottom: 1rem;
            }

            /* Ocultar mais colunas em mobile */
            .table th:nth-child(n+6),
            .table td:nth-child(n+6) {
                display: none;
            }

            .table th:nth-child(11),
            .table td:nth-child(11) {
                display: table-cell !important;
            }

            /* Ajustar chat users em mobile */
            .chat-user-item {
                padding: 10px;
            }

            .chat-users-list {
                max-height: 300px;
            }
        }

        @media (max-width: 576px) {
            /* Ocultar mais colunas em telas muito pequenas */
            .table th:nth-child(n+4),
            .table td:nth-child(n+4) {
                display: none;
            }

            .table th:nth-child(11),
            .table td:nth-child(11) {
                display: table-cell !important;
            }

            .table th:nth-child(1),
            .table td:nth-child(1),
            .table th:nth-child(2),
            .table td:nth-child(2) {
                display: table-cell !important;
            }
        }

        /* Ocultar widget de chat no admin */
        .chat-widget {
            display: none !important;
        }

        /* Melhorar aparência geral */
        .card-header {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6;
        }

        .text-primary {
            color: #0d6efd !important;
        }
    </style>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="container-fluid">
                <!-- Cards de Estatísticas Principais -->
                <div class="row g-3 mb-4">
                    <!-- Vendas -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon sales-icon mx-auto">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $sales['count'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Vendas</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Receita -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon revenue-icon mx-auto">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold">R$ <?php echo number_format($sales['total'] ?? 0, 2, ',', '.'); ?></h3>
                                <p class="mb-0 text-muted">Receita</p>
                            </div>
                        </div>
                    </div>

                    <!-- Produtos -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon products-icon mx-auto">
                                    <i class="fas fa-box"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $products['count'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Produtos</p>
                            </div>
                        </div>
                    </div>

                    <!-- Clientes -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon customers-icon mx-auto">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $customers['count'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Clientes</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cards de Estatísticas do Chat -->
                <div class="row g-3 mb-4">
                    <!-- Usuários do Chat -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon mx-auto" style="background-color: rgba(76, 175, 80, 0.1); color: #4CAF50;">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $stats['total_users'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Usuários do Chat</p>
                            </div>
                        </div>
                    </div>

                    <!-- Usuários Ativos Hoje -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon mx-auto" style="background-color: rgba(3, 169, 244, 0.1); color: #03A9F4;">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $stats['active_today'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Usuários Ativos Hoje</p>
                            </div>
                        </div>
                    </div>

                    <!-- Total de Mensagens -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon mx-auto" style="background-color: rgba(255, 152, 0, 0.1); color: #FF9800;">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo $stats['total_messages'] ?? 0; ?></h3>
                                <p class="mb-0 text-muted">Total de Mensagens</p>
                            </div>
                        </div>
                    </div>

                    <!-- Média de Mensagens por Conversa -->
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <div class="card-icon mx-auto" style="background-color: rgba(33, 150, 243, 0.1); color: #2196F3;">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h3 class="fs-2 mb-1 fw-bold"><?php echo number_format($stats['avg_messages'] ?? 0, 1); ?></h3>
                                <p class="mb-0 text-muted">Média de Mensagens</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Seção deana-bet-cassinoe Logs -->
                <div class="row g-4">
                    <!--ana-bet-cassino-->
                    <div class="col-lg-7 col-md-10">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white border-bottom py-3">
                                <h5 class="card-title mb-0 text-dark fw-semibold">
                                    <i class="fas fa-shopping-cart me-2 text-primary"></i>
                                    Vendas Recentes
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive table-container">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Produto</th>
                                                <th>Valor</th>
                                                <th>Status</th>
                                                <th>Método</th>
                                                <th>Data</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recentSales)): ?>
                                            <tr>
                                                <td colspan="8" class="text-center text-muted py-5">
                                                    <i class="fas fa-shopping-cart fa-3x mb-3 text-secondary opacity-50"></i>
                                                    <div>
                                                        <p class="mb-1 fw-semibold">Nenhuma venda encontrada</p>
                                                        <small class="text-muted">As vendas aparecerão aqui quando realizadas</small>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php else: ?>
                                                <?php foreach ($recentSales as $sale): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary" style="font-size: 0.65rem; padding: 0.15rem 0.3rem;">#<?php echo $sale['id']; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="fw-semibold" style="font-size: 0.75rem;"><?php echo htmlspecialchars(substr($sale['customer_name'] ?? 'Cliente', 0, 12)); ?></div>
                                                        <small class="text-muted text-truncate d-block" style="font-size: 0.65rem; max-width: 100%;"><?php echo htmlspecialchars(substr($sale['customer_email'], 0, 18)); ?></small>
                                                    </td>
                                                    <td>
                                                        <div class="fw-medium text-truncate" style="font-size: 0.75rem;" title="<?php echo htmlspecialchars($sale['product_name'] ?? $sale['product_id']); ?>"><?php echo htmlspecialchars(substr($sale['product_name'] ?? $sale['product_id'], 0, 15)); ?></div>
                                                        <small class="text-muted" style="font-size: 0.65rem;">ID: <?php echo $sale['product_id']; ?></small>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success" style="font-size: 0.75rem;">R$ <?php echo number_format($sale['amount'] ?? 0, 2, ',', '.'); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php if ($sale['payment_status'] === 'approved'): ?>
                                                        <span class="badge bg-success" style="font-size: 0.65rem; padding: 0.15rem 0.3rem;">Pago</span>
                                                        <?php elseif ($sale['payment_status'] === 'pending'): ?>
                                                        <span class="badge bg-warning text-dark" style="font-size: 0.65rem; padding: 0.15rem 0.3rem;">Pendente</span>
                                                        <?php else: ?>
                                                        <span class="badge bg-danger" style="font-size: 0.65rem; padding: 0.15rem 0.3rem;">Rejeitado</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary" style="font-size: 0.65rem; padding: 0.15rem 0.3rem;"><?php echo strtoupper($sale['payment_method'] ?? 'PIX'); ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="text-muted" style="font-size: 0.7rem;">
                                                            <div><?php echo date('d/m/Y', strtotime($sale['created_at'])); ?></div>
                                                            <small style="font-size: 0.65rem;"><?php echo date('H:i', strtotime($sale['created_at'])); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" style="padding: 0.15rem 0.3rem; font-size: 0.65rem;" onclick="viewSaleDetails(<?php echo $sale['id']; ?>)" title="Ver">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <?php if ($sale['payment_status'] === 'pending'): ?>
                                                            <button class="btn btn-outline-success" style="padding: 0.15rem 0.3rem; font-size: 0.65rem;" onclick="approveSale(<?php echo $sale['id']; ?>)" title="Aprovar">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logs de Usuários do Chat -->
                    <div class="col-lg-5 col-md-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white border-bottom py-3 d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0 text-dark fw-semibold">
                                    <i class="fas fa-comments me-2 text-primary"></i>
                                    Usuários do Chat
                                </h5>
                                <a href="chat.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    Ver Chat
                                </a>
                            </div>
                            <div class="card-body p-0">
                                <div class="chat-users-list">
                                    <?php if (empty($userLogs)): ?>
                                        <div class="text-center text-muted py-5">
                                            <i class="fas fa-users fa-3x mb-3 text-secondary opacity-50"></i>
                                            <p class="mb-0">Nenhum usuário de chat encontrado</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($userLogs as $log):
                                            $lastActivity = !empty($log['last_activity']) ? new DateTime($log['last_activity']) : new DateTime();
                                            $isActive = !empty($log['last_activity']) && strtotime($log['last_activity']) > strtotime('-5 minutes');
                                            $statusClass = $isActive ? 'active' : 'inactive';
                                            $now = new DateTime();
                                            $interval = $now->diff($lastActivity);
                                        ?>
                                        <div class="chat-user-item">
                                            <div class="d-flex">
                                                <div class="me-2">
                                                    <span class="user-status <?php echo $statusClass; ?>"
                                                          title="<?php echo $isActive ? 'Online' : 'Offline'; ?>">
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                                        <div class="flex-grow-1 me-3">
                                                            <h6 class="mb-2 fw-semibold text-dark">
                                                                <?php echo htmlspecialchars($log['name']); ?>
                                                            </h6>
                                                            <div class="small text-muted mb-2">
                                                                <i class="fas fa-envelope me-1"></i>
                                                                <?php echo htmlspecialchars(substr($log['email'], 0, 28)) . (strlen($log['email']) > 28 ? '...' : ''); ?>
                                                            </div>
                                                            <?php if (!empty($log['whatsapp'])): ?>
                                                            <div class="small mb-1">
                                                                <i class="fab fa-whatsapp me-1 text-success"></i>
                                                                <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $log['whatsapp']); ?>"
                                                                   target="_blank" class="text-decoration-none text-success">
                                                                    <?php echo htmlspecialchars($log['whatsapp']); ?>
                                                                </a>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="text-end">
                                                            <small class="text-muted d-block mb-2">
                                                                <?php echo $log['message_count']; ?> msgs
                                                            </small>
                                                            <?php if ($isActive): ?>
                                                            <span class="badge bg-success">Online</span>
                                                            <?php else: ?>
                                                            <span class="badge bg-secondary">Offline</span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <?php if (!empty($log['last_message'])): ?>
                                                    <div class="small text-muted mb-2 p-2 bg-light rounded">
                                                        <i class="fas fa-comment me-1"></i>
                                                        <strong>Última mensagem:</strong><br>
                                                        "<?php echo htmlspecialchars(substr($log['last_message'], 0, 80)) . (strlen($log['last_message']) > 80 ? '...' : ''); ?>"
                                                    </div>
                                                    <?php endif; ?>

                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="small text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            <strong>Última atividade:</strong>
                                                            <?php
                                                            if (!empty($log['last_activity'])) {
                                                                if ($interval->days > 0) {
                                                                    echo $interval->days . ' dia(s) atrás';
                                                                } elseif ($interval->h > 0) {
                                                                    echo $interval->h . ' hora(s) atrás';
                                                                } elseif ($interval->i > 0) {
                                                                    echo $interval->i . ' minuto(s) atrás';
                                                                } else {
                                                                    echo 'Agora mesmo';
                                                                }
                                                            } else {
                                                                echo 'Nunca ativo';
                                                            }
                                                            ?>
                                                        </div>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="chat.php?user_id=<?php echo $log['id']; ?>"
                                                               class="btn btn-outline-primary btn-sm"
                                                               title="Abrir Chat">
                                                                <i class="fas fa-comments"></i> Chat
                                                            </a>
                                                            <?php if (!empty($log['whatsapp'])): ?>
                                                            <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $log['whatsapp']); ?>"
                                                               target="_blank"
                                                               class="btn btn-outline-success btn-sm"
                                                               title="WhatsApp">
                                                                <i class="fab fa-whatsapp"></i>
                                                            </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Função para formatar moeda
        function formatMoney(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        }

        // Função para formatar data
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Função para aprovar pagamento manualmente
        async function approvePayment(paymentId) {
            try {
                const response = await fetch('update_payment_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        payment_id: paymentId,
                        status: 'approved'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Pagamento Aprovado!',
                        text: 'O status do pagamento foi atualizado com sucesso.',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        refreshTable();
                    });
                } else {
                    throw new Error(data.error || 'Erro ao atualizar pagamento');
                }
            } catch (error) {
                console.error('Erro:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: error.message || 'Não foi possível atualizar o status do pagamento.'
                });
            }
        }

        // Função para visualizar detalhes da venda
        function viewDetails(saleId) {
            fetch(`get_sale_details.php?id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const sale = data.sale;
                        Swal.fire({
                            title: `Detalhes da Venda #${sale.id}`,
                            html: `
                                <div class="text-start">
                                    <p><strong>Cliente:</strong> ${sale.customer_name}</p>
                                    <p><strong>Email:</strong> ${sale.customer_email}</p>
                                    <p><strong>Produto:</strong> ${sale.product_name || sale.product_id}</p>
                                    <p><strong>Quantidade:</strong> ${sale.quantity}</p>
                                    <p><strong>Valor Unitário:</strong> ${formatMoney(sale.price_at_time)}</p>
                                    <p><strong>Total:</strong> ${formatMoney(sale.total_amount)}</p>
                                    <p><strong>Status:</strong> ${sale.payment_status === 'approved' ? '<span class="text-success">Pago</span>' : '<span class="text-warning">Pendente</span>'}</p>
                                    <p><strong>Método:</strong> ${sale.payment_method.toUpperCase()}</p>
                                    <p><strong>Referência:</strong> ${sale.external_reference}</p>
                                    <p><strong>Data:</strong> ${formatDate(sale.created_at)}</p>
                                </div>
                            `,
                            width: '600px',
                            showCloseButton: true,
                            showConfirmButton: false
                        });
                    } else {
                        throw new Error(data.error || 'Erro ao carregar detalhes');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro',
                        text: error.message || 'Não foi possível carregar os detalhes da venda.'
                    });
                });
        }

        // Função para atualizar a tabela
        function refreshTable() {
            window.location.reload();
        }

        // Atualiza a tabela automaticamente a cada 30 segundos
        const refreshInterval = setInterval(refreshTable, 30000);

        // Limpa o intervalo quando a página é fechada
        window.addEventListener('beforeunload', () => {
            clearInterval(refreshInterval);
        });

        // Função para verificar status do pagamento
        async function checkPaymentStatus(paymentId) {
            try {
                const response = await fetch(`check_payment_status.php?payment_id=${paymentId}`);
                const data = await response.json();
                
                if (data.success && data.status === 'approved') {
                    // Atualiza o status na interface
                    const statusBadge = document.querySelector(`[data-payment-id="${paymentId}"]`);
                    if (statusBadge) {
                        statusBadge.className = 'status-badge status-approved';
                        statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> Pago';
                        
                        // Remove o botão de aprovar
                        const approveBtn = document.querySelector(`button[onclick="approvePayment('${paymentId}')"]`);
                        if (approveBtn) {
                            approveBtn.remove();
                        }
                        
                        // Notifica o usuário
                        Swal.fire({
                            icon: 'success',
                            title: 'Pagamento Confirmado!',
                            text: 'O pagamento foi aprovado com sucesso.',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
            }
        }

        // Função para verificar todos os pagamentos pendentes
        function checkPendingPayments() {
            const pendingBadges = document.querySelectorAll('.status-badge.status-pending');
            pendingBadges.forEach(badge => {
                const paymentId = badge.getAttribute('data-payment-id');
                if (paymentId) {
                    checkPaymentStatus(paymentId);
                }
            });
        }

        // Verifica pagamentos a cada 30 segundos
        const checkInterval = setInterval(checkPendingPayments, 30000);

        // Verifica imediatamente ao carregar a página
        checkPendingPayments();

        // Limpa o intervalo quando a página é fechada
        window.addEventListener('beforeunload', () => {
            clearInterval(checkInterval);
        });
    </script>


</body>
</html>
