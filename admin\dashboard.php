<?php
require_once 'includes/auth_check.php';

// Buscar estatísticas
$db = Database::getInstance();
$pdo = $db->getConnection();

// Total de vendas
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count, SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total FROM orders");
    $sales = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $sales = ['count' => 0, 'total' => 0];
}

// Total de produtos
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
$products = $stmt->fetch(PDO::FETCH_ASSOC);

// Total de clientes
$stmt = $pdo->query("SELECT COUNT(DISTINCT customer_email) as count FROM orders");
$customers = $stmt->fetch(PDO::FETCH_ASSOC);

// Vendas recentes
$stmt = $pdo->query("
    SELECT o.*, p.name as product_name 
    FROM orders o 
    LEFT JOIN products p ON o.product_id = p.id 
    ORDER BY o.created_at DESC 
    LIMIT 10
");
$recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Produtos mais vendidos
$stmt = $pdo->query("
    SELECT p.*, COUNT(o.id) as sales_count 
    FROM products p 
    LEFT JOIN orders o ON p.id = o.product_id 
    GROUP BY p.id 
    ORDER BY sales_count DESC 
    LIMIT 5
");
$topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para obter estatísticas do chat
function getChatStats($pdo) {
    $stats = [];
    
    // Total de usuários
    $query = "SELECT COUNT(*) as total FROM chat_users WHERE is_admin = 0";
    $stmt = $pdo->query($query);
    $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Usuários ativos hoje
    $query = "SELECT COUNT(DISTINCT user_id) as active_today FROM chat_messages 
              WHERE FROM_UNIXTIME(timestamp) >= CURDATE() AND is_admin = 0";
    $stmt = $pdo->query($query);
    $stats['active_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
    
    // Total de mensagens
    $query = "SELECT COUNT(*) as total FROM chat_messages";
    $stmt = $pdo->query($query);
    $stats['total_messages'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Média de mensagens por conversa
    $query = "SELECT AVG(msg_count) as avg_messages FROM 
              (SELECT COUNT(*) as msg_count FROM chat_messages 
               GROUP BY user_id) as message_counts";
    $stmt = $pdo->query($query);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['avg_messages'] = $result && $result['avg_messages'] ? round($result['avg_messages'], 2) : 0;
    
    return $stats;
}

// Função para obter logs de usuário
function getUserLogs($pdo, $limit = 10) {
    $query = "SELECT 
                cu.id,
                cu.name,
                cu.email,
                cu.whatsapp,
                cu.created_at as registered_at,
                cu.last_activity,
                (SELECT COUNT(*) FROM chat_messages WHERE user_id = cu.id) as message_count,
                (SELECT message FROM chat_messages WHERE user_id = cu.id ORDER BY timestamp DESC LIMIT 1) as last_message
              FROM chat_users cu
              WHERE cu.is_admin = 0
              ORDER BY cu.last_activity DESC
              LIMIT :limit";
              
    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Função auxiliar para formatar data
function formatDateTime($dateStr) {
    return date('d/m/Y H:i', strtotime($dateStr));
}

$stats = getChatStats($pdo);
$userLogs = getUserLogs($pdo);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin</title>
    <link rel="icon" type="image/x-icon" href="/Sistema-Vendas/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link href="css/standardized.css" rel="stylesheet">
    <style>
        .dashboard-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 15px;
        }
        .sales-icon { background: rgba(67, 24, 255, 0.1); color: #4318FF; }
        .revenue-icon { background: rgba(0, 157, 99, 0.1); color: #009d63; }
        .products-icon { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .customers-icon { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .table th { white-space: nowrap; }
        .table td { vertical-align: middle; }
        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            margin: 0 2px;
        }
        .user-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .user-status.active {
            background-color: #28a745;
        }
        .user-status.inactive {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="container-fluid px-4">
                <div class="row g-4 my-4">
                    <!-- Vendas -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon sales-icon">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $sales['count'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Vendas</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Receita -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon revenue-icon">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2">R$ <?php echo number_format($sales['total'] ?? 0, 2, ',', '.'); ?></h3>
                                        <p class="mb-0 text-muted">Receita</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Produtos -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon products-icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $products['count'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Produtos</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Clientes -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon customers-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $customers['count'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Clientes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Usuários do Chat -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon" style="background-color: #4CAF50; color: #fff;">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $stats['total_users'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Usuários do Chat</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Usuários Ativos Hoje -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon" style="background-color: #03A9F4; color: #fff;">
                                            <i class="fas fa-user-clock"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $stats['active_today'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Usuários Ativos Hoje</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Total de Mensagens -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon" style="background-color: #FF9800; color: #fff;">
                                            <i class="fas fa-comments"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $stats['total_messages'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Total de Mensagens</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Média de Mensagens por Conversa -->
                    <div class="col-md-3">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="card-icon" style="background-color: #2196F3; color: #fff;">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <h3 class="fs-2 mb-2"><?php echo $stats['avg_messages'] ?? 0; ?></h3>
                                        <p class="mb-0 text-muted">Média de Mensagens por Conversa</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row g-4">
                    <!-- Vendas Recentes -->
                    <div class="col-md-8">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white py-3">
                                <h5 class="card-title mb-0">Vendas Recentes</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Email</th>
                                                <th>Produto</th>
                                                <th>Qtd</th>
                                                <th>Valor Unit.</th>
                                                <th>Total</th>
                                                <th>Status</th>
                                                <th>Método</th>
                                                <th>Referência</th>
                                                <th>Data</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recentSales)): ?>
                                            <tr>
                                                <td colspan="12" class="text-center">Nenhuma venda encontrada</td>
                                            </tr>
                                            <?php else: ?>
                                                <?php foreach ($recentSales as $sale): ?>
                                                <tr>
                                                    <td>#<?php echo $sale['id']; ?></td>
                                                    <td><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($sale['customer_email']); ?></td>
                                                    <td><?php echo htmlspecialchars($sale['product_name'] ?? $sale['product_id']); ?></td>
                                                    <td>1</td>
                                                    <td>R$ <?php echo number_format($sale['amount'] ?? 0, 2, ',', '.'); ?></td>
                                                    <td>R$ <?php echo number_format($sale['amount'] ?? 0, 2, ',', '.'); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo strtolower($sale['payment_status']); ?>">
                                                            <?php echo ucfirst($sale['payment_status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo strtoupper($sale['payment_method']); ?></td>
                                                    <td><small><?php echo htmlspecialchars($sale['external_reference']); ?></small></td>
                                                    <td><?php echo formatDateTime($sale['created_at']); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <?php if ($sale['payment_status'] !== 'approved'): ?>
                                                            <button class="btn btn-success" onclick="approvePayment('<?php echo $sale['payment_id']; ?>')" title="Aprovar Pagamento">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <?php endif; ?>
                                                            <button class="btn btn-info" onclick="viewDetails(<?php echo $sale['id']; ?>)" title="Ver Detalhes">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Logs de Usuários -->
                    <div class="col-md-4">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Logs de Usuários Chats</h5>
                                <a href="chat.php" class="btn btn-sm btn-primary">
                                    <i class="fas fa-comments"></i> Ver Chat
                                </a>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush" style="max-height: 400px; overflow-y: auto;">
                                    <?php if (empty($userLogs)): ?>
                                        <div class="list-group-item text-center text-muted py-4">
                                            <i class="fas fa-users fa-2x mb-2"></i>
                                            <p class="mb-0">Nenhum usuário de chat encontrado</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($userLogs as $log):
                                            $lastActivity = !empty($log['last_activity']) ? new DateTime($log['last_activity']) : new DateTime();
                                            $isActive = (!empty($log['last_activity']) && strtotime($log['last_activity']) > strtotime('-5 minutes'));
                                            $statusClass = $isActive ? 'active' : 'inactive';
                                            $now = new DateTime();
                                            $interval = $now->diff($lastActivity);
                                        ?>
                                        <div class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-1">
                                                        <span class="user-status <?php echo $statusClass; ?> me-2"
                                                              title="<?php echo $isActive ? 'Online' : 'Offline'; ?>">
                                                        </span>
                                                        <h6 class="mb-0 fw-bold"><?php echo htmlspecialchars($log['name']); ?></h6>
                                                        <small class="text-muted ms-auto">
                                                            <?php echo $log['message_count']; ?> msgs
                                                        </small>
                                                    </div>
                                                    <p class="mb-1 small text-muted">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <?php echo htmlspecialchars($log['email']); ?>
                                                    </p>
                                                    <?php if (!empty($log['whatsapp'])): ?>
                                                    <p class="mb-1 small text-muted">
                                                        <i class="fab fa-whatsapp me-1"></i>
                                                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $log['whatsapp']); ?>"
                                                           target="_blank" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($log['whatsapp']); ?>
                                                        </a>
                                                    </p>
                                                    <?php endif; ?>
                                                    <?php if (!empty($log['last_message'])): ?>
                                                    <p class="mb-1 small">
                                                        <strong>Última:</strong>
                                                        <?php echo htmlspecialchars(substr($log['last_message'], 0, 50)) . (strlen($log['last_message']) > 50 ? '...' : ''); ?>
                                                    </p>
                                                    <?php endif; ?>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php
                                                        if (!empty($log['last_activity'])) {
                                                            if ($interval->days > 0) {
                                                                echo $interval->days . ' dias atrás';
                                                            } elseif ($interval->h > 0) {
                                                                echo $interval->h . ' horas atrás';
                                                            } elseif ($interval->i > 0) {
                                                                echo $interval->i . ' minutos atrás';
                                                            } else {
                                                                echo 'Agora mesmo';
                                                            }
                                                        } else {
                                                            echo 'Nunca';
                                                        }
                                                        ?>
                                                    </small>
                                                </div>
                                                <div class="ms-2">
                                                    <a href="chat.php?user_id=<?php echo $log['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="Abrir Chat">
                                                        <i class="fas fa-comments"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Função para formatar moeda
        function formatMoney(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        }

        // Função para formatar data
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Função para aprovar pagamento manualmente
        async function approvePayment(paymentId) {
            try {
                const response = await fetch('update_payment_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        payment_id: paymentId,
                        status: 'approved'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Pagamento Aprovado!',
                        text: 'O status do pagamento foi atualizado com sucesso.',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        refreshTable();
                    });
                } else {
                    throw new Error(data.error || 'Erro ao atualizar pagamento');
                }
            } catch (error) {
                console.error('Erro:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: error.message || 'Não foi possível atualizar o status do pagamento.'
                });
            }
        }

        // Função para visualizar detalhes da venda
        function viewDetails(saleId) {
            fetch(`get_sale_details.php?id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const sale = data.sale;
                        Swal.fire({
                            title: `Detalhes da Venda #${sale.id}`,
                            html: `
                                <div class="text-start">
                                    <p><strong>Cliente:</strong> ${sale.customer_name}</p>
                                    <p><strong>Email:</strong> ${sale.customer_email}</p>
                                    <p><strong>Produto:</strong> ${sale.product_name || sale.product_id}</p>
                                    <p><strong>Quantidade:</strong> ${sale.quantity}</p>
                                    <p><strong>Valor Unitário:</strong> ${formatMoney(sale.price_at_time)}</p>
                                    <p><strong>Total:</strong> ${formatMoney(sale.total_amount)}</p>
                                    <p><strong>Status:</strong> ${sale.payment_status === 'approved' ? '<span class="text-success">Pago</span>' : '<span class="text-warning">Pendente</span>'}</p>
                                    <p><strong>Método:</strong> ${sale.payment_method.toUpperCase()}</p>
                                    <p><strong>Referência:</strong> ${sale.external_reference}</p>
                                    <p><strong>Data:</strong> ${formatDate(sale.created_at)}</p>
                                </div>
                            `,
                            width: '600px',
                            showCloseButton: true,
                            showConfirmButton: false
                        });
                    } else {
                        throw new Error(data.error || 'Erro ao carregar detalhes');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro',
                        text: error.message || 'Não foi possível carregar os detalhes da venda.'
                    });
                });
        }

        // Função para atualizar a tabela
        function refreshTable() {
            window.location.reload();
        }

        // Atualiza a tabela automaticamente a cada 30 segundos
        const refreshInterval = setInterval(refreshTable, 30000);

        // Limpa o intervalo quando a página é fechada
        window.addEventListener('beforeunload', () => {
            clearInterval(refreshInterval);
        });

        // Função para verificar status do pagamento
        async function checkPaymentStatus(paymentId) {
            try {
                const response = await fetch(`check_payment_status.php?payment_id=${paymentId}`);
                const data = await response.json();
                
                if (data.success && data.status === 'approved') {
                    // Atualiza o status na interface
                    const statusBadge = document.querySelector(`[data-payment-id="${paymentId}"]`);
                    if (statusBadge) {
                        statusBadge.className = 'status-badge status-approved';
                        statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> Pago';
                        
                        // Remove o botão de aprovar
                        const approveBtn = document.querySelector(`button[onclick="approvePayment('${paymentId}')"]`);
                        if (approveBtn) {
                            approveBtn.remove();
                        }
                        
                        // Notifica o usuário
                        Swal.fire({
                            icon: 'success',
                            title: 'Pagamento Confirmado!',
                            text: 'O pagamento foi aprovado com sucesso.',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
            }
        }

        // Função para verificar todos os pagamentos pendentes
        function checkPendingPayments() {
            const pendingBadges = document.querySelectorAll('.status-badge.status-pending');
            pendingBadges.forEach(badge => {
                const paymentId = badge.getAttribute('data-payment-id');
                if (paymentId) {
                    checkPaymentStatus(paymentId);
                }
            });
        }

        // Verifica pagamentos a cada 30 segundos
        const checkInterval = setInterval(checkPendingPayments, 30000);

        // Verifica imediatamente ao carregar a página
        checkPendingPayments();

        // Limpa o intervalo quando a página é fechada
        window.addEventListener('beforeunload', () => {
            clearInterval(checkInterval);
        });
    </script>
</body>
</html>
