<?php
session_start();
require_once 'database/connection.php';

$database = Database::getInstance();
$pdo = $database->getConnection();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat de Atendimento</title>
    <link rel="icon" type="image/x-icon" href="/Sistema-Vendas/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/chat_modern.css" rel="stylesheet">
    <!-- Add <PERSON>t2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0D6EFD;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }
        
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background: var(--light-bg);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background: var(--primary-color);
            color: white;
            margin-left: auto;
        }

        .message.user .message-sender {
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }
        
        .message.admin {
            background: var(--light-bg);
            color: #212529;
            margin-right: auto;
            border: 1px solid var(--border-color);
        }

        .message.admin .message-sender {
            color: #444;
            font-weight: 600;
        }

        .message.admin .message-time {
            color: #666;
            font-weight: 500;
        }
        
        .message-sender {
            font-size: 0.85em;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .message-time {
            font-size: 0.75em;
            margin-top: 5px;
            opacity: 1;
        }

        .chat-header {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }

        .chat-input {
            padding: 15px;
            background: white;
            border-top: 1px solid var(--border-color);
        }

        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 10px 20px;
            color: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn-primary:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-box">
            <div class="chat-header">
                <h2><i class="fas fa-comments"></i> Chat de Atendimento</h2>
            </div>

            <div id="registration-form">
                <h3>Iniciar Conversa</h3>
                <form id="userForm">
                    <div class="form-group">
                        <label for="name"><i class="fas fa-user"></i> Nome</label>
                        <input type="text" id="name" name="name" required class="form-control" 
                            placeholder="Ex: João Silva" 
                            pattern="[A-Za-zÀ-ú]+ [A-Za-zÀ-ú ]+" 
                            title="Digite seu nome e sobrenome">
                    </div>
                    <div class="form-group">
                        <label for="email"><i class="fas fa-envelope"></i> E-mail</label>
                        <input type="email" id="email" name="email" required class="form-control" 
                            placeholder="Ex: <EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="whatsapp"><i class="fab fa-whatsapp"></i> WhatsApp</label>
                        <input type="tel" id="whatsapp" name="whatsapp" required class="form-control" 
                            placeholder="Ex: 11922445566" 
                            pattern="[0-9]{11}" 
                            maxlength="11"
                            title="Digite apenas os números (DDD + número). Ex: 11922445566">
                    </div>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-paper-plane"></i> Iniciar Chat
                    </button>
                </form>
            </div>

            <div id="chat-area" style="display: none;">
                <div class="chat-messages" id="chat-messages"></div>
                <div class="chat-input">
                    <textarea id="message-input" placeholder="Digite sua mensagem..." class="form-control"></textarea>
                    <button class="btn-primary" id="send-button">
                        <i class="fas fa-paper-plane"></i> Enviar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Add SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let userId = null;
        let lastMessageTime = 0;
        let messageUpdateInterval = null;
        let isFirstLoad = true;

        console.log('Chat inicializado - userId:', userId);

        $(document).ready(function() {
            $('#userForm').on('submit', function(e) {
                e.preventDefault();
                
                const name = $('#name').val().trim();
                const whatsapp = $('#whatsapp').val().trim();
                
                // Validate name format
                const namePattern = /[A-Za-zÀ-ú]+ [A-Za-zÀ-ú ]+/;
                if (!namePattern.test(name)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Nome Inválido',
                        text: 'Digite seu nome e sobrenome'
                    });
                    return;
                }
                
                // Validate WhatsApp format
                const whatsappPattern = /[0-9]{11}/;
                if (!whatsappPattern.test(whatsapp)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'WhatsApp Inválido',
                        text: 'Digite apenas os números (DDD + número). Ex: 43988586241'
                    });
                    return;
                }

                const formData = new FormData(this);
                
                $.ajax({
                    url: 'api/create_user.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Resposta do registro:', response);
                        if (response.success && response.user_id) {
                            userId = parseInt(response.user_id);
                            console.log('Usuário registrado com ID:', userId);
                            $('#registration-form').hide();
                            $('#chat-area').show();
                            startMessageUpdates();
                        } else {
                            console.error('Erro no registro:', response);
                            Swal.fire({
                                icon: 'error',
                                title: 'Erro no Registro',
                                text: response.message || 'Erro desconhecido'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Erro no registro:', xhr);
                        console.error('Response text:', xhr.responseText);

                        let errorMessage = 'Ocorreu um erro ao processar sua solicitação';

                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            if (xhr.responseText.includes('Duplicate entry') &&
                                xhr.responseText.includes('unique_email')) {
                                errorMessage = 'Email já cadastrado';
                            }
                        }

                        Swal.fire({
                            title: 'Erro no Registro',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#6c5ce7'
                        });
                    }
                });
            });

            function formatDateTime(dateStr) {
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                    return new Date().toLocaleString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
                return date.toLocaleString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            function loadMessages() {
                if (!userId) return;
                
                $.get('api/get_messages.php', { user_id: userId, last_time: lastMessageTime }, function(response) {
                    if (response.success && response.messages && response.messages.length > 0) {
                        let hasNewMessages = false;
                        
                        response.messages.forEach(function(msg) {
                            const messageId = `msg-${msg.id}`;
                            if ($(`#${messageId}`).length === 0) {
                                hasNewMessages = true;
                                const messageHtml = `
                                    <div id="${messageId}" class="message ${msg.is_admin ? 'admin' : 'user'}">
                                        <div class="message-sender">${msg.is_admin ? 'Atendente' : 'Você'}</div>
                                        <div class="message-content">${msg.message}</div>
                                        <div class="message-time">${formatDateTime(msg.created_at)}</div>
                                    </div>
                                `;
                                $('#chat-messages').append(messageHtml);
                                
                                if (msg.created_at > lastMessageTime) {
                                    lastMessageTime = msg.created_at;
                                }
                            }
                        });
                        
                        if (hasNewMessages) {
                            const chatMessages = document.getElementById('chat-messages');
                            chatMessages.scrollTop = chatMessages.scrollHeight;
                        }
                    }
                });
            }

            function startMessageUpdates() {
                lastMessageTime = 0;
                isFirstLoad = true;
                loadMessages();
                
                if (messageUpdateInterval) {
                    clearInterval(messageUpdateInterval);
                }
                messageUpdateInterval = setInterval(loadMessages, 3000);
            }

            $('#send-button').click(function() {
                const message = $('#message-input').val().trim();
                console.log('Tentando enviar mensagem:', message);
                console.log('User ID:', userId);

                if (!message) {
                    console.log('Mensagem vazia, cancelando envio');
                    return;
                }

                if (!userId) {
                    console.log('User ID não definido');
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro',
                        text: 'Sessão de usuário não encontrada. Recarregue a página.'
                    });
                    return;
                }

                const messageInput = $('#message-input');
                const sendButton = $('#send-button');

                messageInput.prop('disabled', true);
                sendButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Enviando...');

                $.ajax({
                    url: 'api/send_message.php',
                    method: 'POST',
                    data: {
                        user_id: userId,
                        message: message
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Resposta do servidor:', response);
                        if (response.success) {
                            messageInput.val('');
                            loadMessages();
                            console.log('Mensagem enviada com sucesso');
                        } else {
                            console.error('Erro do servidor:', response.message);
                            Swal.fire({
                                icon: 'error',
                                title: 'Erro',
                                text: response.message || 'Não foi possível enviar a mensagem.'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Erro AJAX:', {xhr, status, error});
                        console.error('Response text:', xhr.responseText);

                        let errorMessage = 'Erro ao enviar mensagem. Verifique sua conexão.';

                        if (xhr.responseText) {
                            try {
                                const errorResponse = JSON.parse(xhr.responseText);
                                errorMessage = errorResponse.message || errorMessage;
                            } catch (e) {
                                console.error('Erro ao parsear resposta de erro:', e);
                            }
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Erro',
                            text: errorMessage
                        });
                    },
                    complete: function() {
                        messageInput.prop('disabled', false).focus();
                        sendButton.prop('disabled', false).html('<i class="fas fa-paper-plane"></i> Enviar');
                    }
                });
            });

            $('#message-input').keypress(function(e) {
                if (e.which == 13 && !e.shiftKey) {
                    e.preventDefault();
                    $('#send-button').click();
                }
            });
        });
    </script>
</body>
</html>
