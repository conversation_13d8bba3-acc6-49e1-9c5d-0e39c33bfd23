<?php
session_start();
require_once 'database/connection.php';

// Recebe os dados do POST
$data = json_decode(file_get_contents('php://input'), true);

if (isset($data['order_id']) && isset($data['status'])) {
    $order_id = $data['order_id'];
    $status = $data['status'];
    
    // Insere a notificação na tabela de notificações
    $query = "INSERT INTO admin_notifications (order_id, status, is_read) VALUES (?, ?, 0)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('is', $order_id, $status);
    
    if ($stmt->execute()) {
        http_response_code(200);
        echo json_encode(['success' => true]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Erro ao salvar notificação']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Dados inválidos']);
}
