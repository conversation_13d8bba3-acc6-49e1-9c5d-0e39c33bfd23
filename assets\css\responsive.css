/* CSS 100% Responsivo para Todas as Telas */

/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container responsivo */
.container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Grid System Responsivo */
.row-responsive {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-responsive {
    flex: 1;
    padding: 0 15px;
    min-width: 0;
}

/* Produtos Grid Responsivo */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
    max-width: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.product-content {
    padding: 20px;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1a202c;
    line-height: 1.4;
}

.product-description {
    color: #718096;
    margin-bottom: 15px;
    font-size: 0.95rem;
    line-height: 1.5;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #22c55e;
    margin-bottom: 20px;
}

.btn-buy {
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-buy:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Header Responsivo */
.header-responsive {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: #4a5568;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #667eea;
}

/* Footer Responsivo */
.footer-responsive {
    background: #2d3748;
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    color: #e2e8f0;
}

.footer-section p,
.footer-section a {
    color: #a0aec0;
    text-decoration: none;
    line-height: 1.6;
}

.footer-section a:hover {
    color: white;
}

/* Breakpoints Responsivos */

/* Tablets (768px - 1024px) */
@media (max-width: 1024px) {
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .container-responsive {
        padding: 0 20px;
    }
    
    .nav-menu {
        gap: 20px;
    }
}

/* Tablets Pequenos (576px - 768px) */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 15px;
        padding: 15px 0;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .product-content {
        padding: 15px;
    }
    
    .product-title {
        font-size: 1.1rem;
    }
    
    .product-price {
        font-size: 1.3rem;
    }
    
    .btn-buy {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* Smartphones (até 576px) */
@media (max-width: 576px) {
    html {
        font-size: 14px;
    }
    
    .container-responsive {
        padding: 0 10px;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 10px 0;
    }
    
    .product-card {
        margin: 0 auto;
        max-width: 350px;
    }
    
    .product-image {
        height: 180px;
    }
    
    .product-content {
        padding: 15px;
    }
    
    .product-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }
    
    .product-description {
        font-size: 0.85rem;
        margin-bottom: 12px;
    }
    
    .product-price {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }
    
    .btn-buy {
        padding: 12px 15px;
        font-size: 0.9rem;
    }
    
    .header-responsive {
        padding: 10px 0;
    }
    
    .logo {
        font-size: 1.3rem;
    }
    
    .nav-menu {
        gap: 10px;
        font-size: 0.9rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }
}

/* Smartphones Pequenos (até 375px) */
@media (max-width: 375px) {
    .container-responsive {
        padding: 0 8px;
    }
    
    .product-card {
        max-width: 100%;
    }
    
    .product-content {
        padding: 12px;
    }
    
    .product-title {
        font-size: 0.95rem;
    }
    
    .product-description {
        font-size: 0.8rem;
    }
    
    .product-price {
        font-size: 1.1rem;
    }
    
    .btn-buy {
        padding: 10px 12px;
        font-size: 0.85rem;
    }
}

/* Telas Muito Grandes (acima de 1400px) */
@media (min-width: 1400px) {
    .container-responsive {
        max-width: 1400px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 30px;
    }
    
    .product-title {
        font-size: 1.4rem;
    }
    
    .product-price {
        font-size: 1.6rem;
    }
}

/* Utilitários Responsivos */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-none { display: none; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

/* Classes de visibilidade responsiva */
@media (max-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
}

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
}

@media (max-width: 1024px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
}
