<?php
session_start();
require_once '../../database/connection.php';

if (!isset($_SESSION['admin_id']) || !isset($_GET['conversation_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    $conversation_id = intval($_GET['conversation_id']);
    
    // Buscar mensagens da conversa
    $stmt = $pdo->prepare("
        SELECT 
            m.*,
            CASE 
                WHEN m.sender_id = :admin_id THEN 1 
                ELSE 0 
            END as is_admin,
            DATE_FORMAT(m.created_at, '%d/%m/%Y %H:%i') as created_at
        FROM chat_messages m
        WHERE 
            (sender_id = :conversation_id AND receiver_id = :admin_id)
            OR 
            (sender_id = :admin_id AND receiver_id = :conversation_id)
        ORDER BY m.created_at ASC
    ");
    
    $stmt->execute([
        ':admin_id' => $_SESSION['admin_id'],
        ':conversation_id' => $conversation_id
    ]);
    
    $messages = $stmt->fetchAll();
    
    // Marcar mensagens como lidas
    $stmt = $pdo->prepare("
        UPDATE chat_messages 
        SET read_at = NOW() 
        WHERE 
            receiver_id = :admin_id 
            AND sender_id = :conversation_id 
            AND read_at IS NULL
    ");
    
    $stmt->execute([
        ':admin_id' => $_SESSION['admin_id'],
        ':conversation_id' => $conversation_id
    ]);
    
    echo json_encode($messages);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar mensagens']);
}
