// Array de notificações
const names = [
    "<PERSON> completou a compra do Curso Completo: Monte Seu Próprio Site!",
    "Ana Souza completou a compra do Curso Completo: Monte Seu Próprio Site!",
    "<PERSON> completou a compra do Curso Completo: Monte Seu Próprio Site!",
    "<PERSON> completou a compra do Script PHP - Modelo China para Casa de Apostas!",
    "<PERSON> completou a compra do Curso Completo: Facebook Ads Venda Certa!",
    // ... adicione mais nomes conforme necessário
];

const usersOnline = [
    "<PERSON> está online agora!",
    "<PERSON> está online agora!",
    "Ana Souza está online agora!",
    "<PERSON> est<PERSON> online agora!",
    // ... adicione mais usuários conforme necessário
];

const notification = document.getElementById("notification");
let index = 0;
let onlineIndex = 0;
let toggle = true;

function showNotification() {
    notification.textContent = toggle ? names[index] : usersOnline[onlineIndex];
    notification.classList.add(toggle ? "purchase" : "online");
    notification.classList.add("active");

    setTimeout(() => {
        notification.classList.remove("active");
        notification.classList.remove(toggle ? "purchase" : "online");
    }, 5000);

    if (toggle) {
        index = (index + 1) % names.length;
    } else {
        onlineIndex = (onlineIndex + 1) % usersOnline.length;
    }
    toggle = !toggle;
}

setInterval(showNotification, 8000);

// Modal de imagem
function closeModal() {
    document.getElementById('imageModal').style.display = 'none';
}

document.querySelectorAll('.card img').forEach(image => {
    image.addEventListener('click', (e) => {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        modalImage.src = e.target.src;
        modal.style.display = 'flex';
    });
});

// Pop-up de confirmação
function showPopup(url) {
    document.querySelector('.popup').style.display = 'block';
    document.querySelector('.popup-overlay').style.display = 'block';
    
    document.getElementById('confirmBtn').onclick = function() {
        window.location.href = url;
    };
}

function closePopup() {
    document.querySelector('.popup').style.display = 'none';
    document.querySelector('.popup-overlay').style.display = 'none';
}

// Banner de cookies
document.addEventListener("DOMContentLoaded", function() {
    const cookieBanner = document.getElementById("cookieBanner");
    const acceptButton = document.getElementById("accept-cookies");
    const declineButton = document.getElementById("decline-cookies");

    if (localStorage.getItem("cookiesAccepted") !== null) {
        cookieBanner.style.display = "none";
    }

    acceptButton.addEventListener("click", () => {
        localStorage.setItem("cookiesAccepted", "true");
        cookieBanner.style.display = "none";
    });

    declineButton.addEventListener("click", () => {
        localStorage.setItem("cookiesAccepted", "false");
        cookieBanner.style.display = "none";
    });
});

// Chat
document.getElementById('openChat').addEventListener('click', () => {
    window.open('chat.php', '_blank', 'width=400,height=600');
});
