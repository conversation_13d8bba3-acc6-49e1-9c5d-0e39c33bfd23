<?php
/**
 * Monitor SEO - KESUNG SITE
 * Monitora posições no Google, analisa concorrência e gera relatórios
 */

// Configurações
$targetKeywords = [
    'curso programação web',
    'script php casa apostas',
    'facebook ads curso',
    'criar site profissional',
    'script php completo',
    'curso html css javascript',
    'sistema apostas esportivas',
    'marketing digital curso',
    'programação para iniciantes',
    'desenvolvimento web curso'
];

$competitors = [
    'udemy.com',
    'alura.com.br',
    'rocketseat.com.br',
    'cursoemvideo.com',
    'devmedia.com.br'
];

$siteUrl = 'kesungsite.com';

/**
 * Simular busca no Google (em produção, usar API oficial)
 */
function searchGoogle($keyword, $pages = 3) {
    // ATENÇÃO: Esta é uma simulação
    // Em produção, use a API oficial do Google Search Console
    
    $results = [];
    
    // Simular resultados para demonstração
    $mockResults = [
        'curso programação web' => [
            ['url' => 'udemy.com/curso-web', 'position' => 1],
            ['url' => 'alura.com.br/programacao', 'position' => 2],
            ['url' => 'kesungsite.com/cursos-programacao', 'position' => 8],
        ],
        'script php casa apostas' => [
            ['url' => 'kesungsite.com/scripts-php', 'position' => 3],
            ['url' => 'github.com/apostas-script', 'position' => 5],
        ],
        'facebook ads curso' => [
            ['url' => 'hotmart.com/facebook-ads', 'position' => 1],
            ['url' => 'kesungsite.com/marketing-digital', 'position' => 12],
        ]
    ];
    
    return $mockResults[$keyword] ?? [];
}

/**
 * Analisar posições das palavras-chave
 */
function analyzeKeywordPositions() {
    global $targetKeywords, $siteUrl;
    
    $analysis = [];
    
    foreach ($targetKeywords as $keyword) {
        echo "Analisando: {$keyword}\n";
        
        $results = searchGoogle($keyword);
        $position = null;
        $competitors = [];
        
        foreach ($results as $result) {
            if (strpos($result['url'], $siteUrl) !== false) {
                $position = $result['position'];
            } else {
                $competitors[] = [
                    'url' => $result['url'],
                    'position' => $result['position']
                ];
            }
        }
        
        $analysis[$keyword] = [
            'position' => $position,
            'competitors' => array_slice($competitors, 0, 5), // Top 5 concorrentes
            'opportunity' => $position === null ? 'Não ranqueando' : ($position > 10 ? 'Melhorar' : 'Manter'),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Aguardar para evitar rate limiting
        sleep(2);
    }
    
    return $analysis;
}

/**
 * Analisar métricas técnicas SEO
 */
function analyzeTechnicalSEO() {
    global $siteUrl;
    
    $metrics = [];
    
    // Verificar tempo de carregamento
    $start = microtime(true);
    $response = @file_get_contents("https://{$siteUrl}");
    $loadTime = microtime(true) - $start;
    
    $metrics['load_time'] = round($loadTime, 2);
    $metrics['load_time_status'] = $loadTime < 3 ? 'Bom' : ($loadTime < 5 ? 'Regular' : 'Ruim');
    
    // Verificar se o site está online
    $metrics['site_online'] = $response !== false;
    
    // Verificar sitemap
    $sitemapResponse = @file_get_contents("https://{$siteUrl}/sitemap.xml");
    $metrics['sitemap_accessible'] = $sitemapResponse !== false;
    
    if ($sitemapResponse) {
        $xml = @simplexml_load_string($sitemapResponse);
        $metrics['sitemap_urls'] = $xml ? count($xml->url) : 0;
    }
    
    // Verificar robots.txt
    $robotsResponse = @file_get_contents("https://{$siteUrl}/robots.txt");
    $metrics['robots_accessible'] = $robotsResponse !== false;
    
    // Verificar HTTPS
    $metrics['https_enabled'] = strpos($siteUrl, 'https://') === 0;
    
    return $metrics;
}

/**
 * Gerar sugestões de melhoria
 */
function generateSuggestions($analysis, $technical) {
    $suggestions = [];
    
    // Sugestões baseadas em posições
    foreach ($analysis as $keyword => $data) {
        if ($data['position'] === null) {
            $suggestions[] = "Criar conteúdo otimizado para '{$keyword}' - palavra não ranqueando";
        } elseif ($data['position'] > 10) {
            $suggestions[] = "Melhorar SEO para '{$keyword}' - posição {$data['position']}";
        }
    }
    
    // Sugestões técnicas
    if ($technical['load_time'] > 3) {
        $suggestions[] = "Otimizar velocidade do site - tempo atual: {$technical['load_time']}s";
    }
    
    if (!$technical['sitemap_accessible']) {
        $suggestions[] = "Corrigir acesso ao sitemap.xml";
    }
    
    if (!$technical['robots_accessible']) {
        $suggestions[] = "Corrigir acesso ao robots.txt";
    }
    
    return $suggestions;
}

/**
 * Gerar relatório completo
 */
function generateSEOReport() {
    echo "🔍 KESUNG SITE - Relatório SEO\n";
    echo "==============================\n\n";
    
    // Análise de palavras-chave
    echo "📊 Análise de Palavras-chave:\n";
    echo "-----------------------------\n";
    $keywordAnalysis = analyzeKeywordPositions();
    
    foreach ($keywordAnalysis as $keyword => $data) {
        $status = $data['position'] ? "Posição {$data['position']}" : "Não ranqueando";
        echo "• {$keyword}: {$status} ({$data['opportunity']})\n";
    }
    
    echo "\n";
    
    // Análise técnica
    echo "⚙️ Análise Técnica:\n";
    echo "-------------------\n";
    $technicalAnalysis = analyzeTechnicalSEO();
    
    echo "• Tempo de carregamento: {$technicalAnalysis['load_time']}s ({$technicalAnalysis['load_time_status']})\n";
    echo "• Site online: " . ($technicalAnalysis['site_online'] ? 'Sim' : 'Não') . "\n";
    echo "• Sitemap acessível: " . ($technicalAnalysis['sitemap_accessible'] ? 'Sim' : 'Não') . "\n";
    echo "• URLs no sitemap: " . ($technicalAnalysis['sitemap_urls'] ?? 'N/A') . "\n";
    echo "• Robots.txt acessível: " . ($technicalAnalysis['robots_accessible'] ? 'Sim' : 'Não') . "\n";
    echo "• HTTPS habilitado: " . ($technicalAnalysis['https_enabled'] ? 'Sim' : 'Não') . "\n";
    
    echo "\n";
    
    // Sugestões
    echo "💡 Sugestões de Melhoria:\n";
    echo "-------------------------\n";
    $suggestions = generateSuggestions($keywordAnalysis, $technicalAnalysis);
    
    if (empty($suggestions)) {
        echo "• Parabéns! Nenhuma melhoria crítica identificada.\n";
    } else {
        foreach ($suggestions as $suggestion) {
            echo "• {$suggestion}\n";
        }
    }
    
    echo "\n";
    
    // Salvar relatório
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'keywords' => $keywordAnalysis,
        'technical' => $technicalAnalysis,
        'suggestions' => $suggestions
    ];
    
    $reportFile = 'logs/seo_report_' . date('Y-m-d') . '.json';
    
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT));
    
    echo "📄 Relatório salvo em: {$reportFile}\n";
    
    return $report;
}

/**
 * Monitorar mudanças de posição
 */
function trackPositionChanges() {
    $today = date('Y-m-d');
    $reportFile = "logs/seo_report_{$today}.json";
    $yesterdayFile = "logs/seo_report_" . date('Y-m-d', strtotime('-1 day')) . ".json";
    
    if (!file_exists($yesterdayFile)) {
        echo "📈 Primeiro relatório - sem dados de comparação\n";
        return;
    }
    
    $todayData = json_decode(file_get_contents($reportFile), true);
    $yesterdayData = json_decode(file_get_contents($yesterdayFile), true);
    
    echo "📈 Mudanças de Posição (últimas 24h):\n";
    echo "------------------------------------\n";
    
    foreach ($todayData['keywords'] as $keyword => $data) {
        $todayPos = $data['position'];
        $yesterdayPos = $yesterdayData['keywords'][$keyword]['position'] ?? null;
        
        if ($todayPos && $yesterdayPos) {
            $change = $yesterdayPos - $todayPos; // Positivo = subiu
            if ($change > 0) {
                echo "📈 {$keyword}: Subiu {$change} posições (#{$todayPos})\n";
            } elseif ($change < 0) {
                echo "📉 {$keyword}: Desceu " . abs($change) . " posições (#{$todayPos})\n";
            } else {
                echo "➡️ {$keyword}: Manteve posição #{$todayPos}\n";
            }
        } elseif ($todayPos && !$yesterdayPos) {
            echo "🆕 {$keyword}: Começou a ranquear na posição #{$todayPos}\n";
        } elseif (!$todayPos && $yesterdayPos) {
            echo "❌ {$keyword}: Parou de ranquear (estava na #{$yesterdayPos})\n";
        }
    }
}

// Executar se chamado diretamente
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Executado via linha de comando
    $report = generateSEOReport();
    echo "\n";
    trackPositionChanges();
} else {
    // Executado via web (apenas para admin)
    session_start();
    
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        http_response_code(403);
        die('Acesso negado');
    }
    
    ob_start();
    $report = generateSEOReport();
    echo "\n";
    trackPositionChanges();
    $output = ob_get_clean();
    
    echo '<pre>' . htmlspecialchars($output) . '</pre>';
}
?>
