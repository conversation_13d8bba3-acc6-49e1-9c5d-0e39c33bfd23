<?php
// Visitor Tracker - Captura automaticamente visitantes do site
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Incluir conexão com banco
require_once __DIR__ . '/../database/connection.php';

function trackVisitor() {
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();

        // Obter informações do visitante
        $ip = getRealIpAddr();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $currentPage = $_SERVER['REQUEST_URI'] ?? '/';
        $referrer = $_SERVER['HTTP_REFERER'] ?? null;

        // Sempre registrar log de página visitada
        logPageVisit($pdo, $ip, $userAgent, $currentPage, $referrer);

        // Verificar se já foi registrado nesta sessão para visitante único
        if (isset($_SESSION['visitor_tracked']) && $_SESSION['visitor_tracked'] === true) {
            return;
        }
        
        // Criar tabelas se não existirem
        createTablesIfNotExist($pdo);
        
        // Verificar se visitante já existe
        $stmt = $pdo->prepare("SELECT id, visit_count FROM visitors WHERE ip = ?");
        $stmt->execute([$ip]);
        $existingVisitor = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingVisitor) {
            // Atualizar visitante existente
            $stmt = $pdo->prepare("
                UPDATE visitors 
                SET visit_count = visit_count + 1, 
                    last_visit = NOW(), 
                    user_agent = ? 
                WHERE ip = ?
            ");
            $stmt->execute([$userAgent, $ip]);
            $visitorId = $existingVisitor['id'];
        } else {
            // Obter localização real (mesmo com VPN)
            $location = getLocationInfo($ip);

            // Detectar VPN de forma avançada
            $isVpn = detectVPN($ip);

            // Se não conseguiu cidade válida, tentar APIs adicionais
            if ($location['city'] === 'Desconhecido' || empty($location['city'])) {
                $location = tryAdditionalAPIs($ip);
            }

            // Inserir novo visitante
            $stmt = $pdo->prepare("
                INSERT INTO visitors (ip, ip_address, user_agent, country, city, is_vpn, visit_count, first_visit, last_visit, visit_time)
                VALUES (?, ?, ?, ?, ?, ?, 1, NOW(), NOW(), NOW())
            ");
            $stmt->execute([
                $ip,
                $ip,
                $userAgent,
                $location['country'],
                $location['city'],
                $isVpn ? 1 : 0
            ]);
            $visitorId = $pdo->lastInsertId();
        }
        
        // Registrar acesso detalhado
        $referrerSource = detectReferrerSource($referrer);
        $stmt = $pdo->prepare("
            INSERT INTO visitor_access (ip_address, city, state, referrer_source, referrer_url, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $ip,
            $location['city'] ?? 'Desconhecido',
            $location['state'] ?? null,
            $referrerSource,
            $referrer,
            $userAgent
        ]);
        
        // Registrar log de página
        logPageVisit($pdo, $ip, $userAgent, $currentPage, $referrer, $visitorId);
        
        // Marcar como rastreado nesta sessão
        $_SESSION['visitor_tracked'] = true;
        
    } catch (Exception $e) {
        // Ignorar erros de tracking para não afetar o site
        error_log("Visitor tracking error: " . $e->getMessage());
    }
}

function logPageVisit($pdo, $ip, $userAgent, $currentPage, $referrer, $visitorId = null) {
    try {
        // Se não temos visitor_id, buscar pelo IP
        if (!$visitorId) {
            $stmt = $pdo->prepare("SELECT id FROM visitors WHERE ip = ? ORDER BY last_visit DESC LIMIT 1");
            $stmt->execute([$ip]);
            $visitorId = $stmt->fetchColumn();
        }

        $stmt = $pdo->prepare("
            INSERT INTO visitor_logs (visitor_id, ip_address, user_agent, page_url, referrer_url, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $visitorId,
            $ip,
            $userAgent,
            $currentPage,
            $referrer
        ]);
    } catch (Exception $e) {
        error_log("Page visit logging error: " . $e->getMessage());
    }
}

function getRealIpAddr() {
    // Lista de headers para verificar IP real
    $ipHeaders = [
        'HTTP_CF_CONNECTING_IP',     // Cloudflare
        'HTTP_CLIENT_IP',            // Proxy
        'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
        'HTTP_X_FORWARDED',          // Proxy
        'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
        'HTTP_FORWARDED_FOR',        // Proxy
        'HTTP_FORWARDED',            // Proxy
        'REMOTE_ADDR'                // Standard
    ];

    foreach ($ipHeaders as $header) {
        if (!empty($_SERVER[$header])) {
            $ips = explode(',', $_SERVER[$header]);
            $ip = trim($ips[0]);

            // Validar se é um IP válido e não privado
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // Se não encontrou IP público, usar REMOTE_ADDR
    $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

    // Para desenvolvimento local, simular IP brasileiro real
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0) {
        // IPs brasileiros reais para teste
        $brazilianIPs = [
            '************',   // São Paulo
            '************',   // Rio de Janeiro
            '*************',  // Belo Horizonte
            '*************',  // Brasília
            '*************',  // Salvador
            '************',   // Fortaleza
            '************',   // Recife
            '************',   // Porto Alegre
            '*************',  // Curitiba
            '*************'   // Goiânia
        ];

        $ip = $brazilianIPs[array_rand($brazilianIPs)];
    }

    return $ip;
}

function getLocationInfo($ip) {
    $location = [
        'country' => 'Brasil',
        'city' => 'Desconhecido',
        'state' => null
    ];

    // Se for IP local, não tentar buscar localização
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return $location;
    }

    // Tentar múltiplas APIs de geolocalização (em ordem de precisão)
    $apis = [
        [
            'url' => "http://ip-api.com/json/{$ip}?fields=status,country,regionName,city,lat,lon,timezone,isp,org,proxy,hosting",
            'type' => 'ip-api'
        ],
        [
            'url' => "https://ipapi.co/{$ip}/json/",
            'type' => 'ipapi'
        ],
        [
            'url' => "http://www.geoplugin.net/json.gp?ip={$ip}",
            'type' => 'geoplugin'
        ],
        [
            'url' => "https://freegeoip.app/json/{$ip}",
            'type' => 'freegeoip'
        ],
        [
            'url' => "https://ipinfo.io/{$ip}/json",
            'type' => 'ipinfo'
        ]
    ];

    foreach ($apis as $api) {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (compatible; VisitorTracker/1.0)',
                    'method' => 'GET',
                    'header' => [
                        'Accept: application/json',
                        'Accept-Language: pt-BR,pt;q=0.9,en;q=0.8'
                    ]
                ]
            ]);

            $response = @file_get_contents($api['url'], false, $context);
            if ($response) {
                $data = json_decode($response, true);

                // Processar resposta da ip-api.com
                if ($api['type'] === 'ip-api' && $data && $data['status'] === 'success') {
                    $location['country'] = $data['country'] ?? 'Brasil';
                    $location['city'] = $data['city'] ?? 'Desconhecido';
                    $location['state'] = $data['regionName'] ?? null;
                    $location['api_used'] = 'ip-api.com';

                    // Se conseguiu cidade válida, parar aqui
                    if ($location['city'] !== 'Desconhecido' && !empty($location['city'])) {
                        break;
                    }
                }

                // Processar resposta da ipapi.co
                if ($api['type'] === 'ipapi' && $data && !isset($data['error'])) {
                    $location['country'] = $data['country_name'] ?? 'Brasil';
                    $location['city'] = $data['city'] ?? 'Desconhecido';
                    $location['state'] = $data['region'] ?? null;
                    $location['api_used'] = 'ipapi.co';

                    // Se conseguiu cidade válida, parar aqui
                    if ($location['city'] !== 'Desconhecido' && !empty($location['city'])) {
                        break;
                    }
                }

                // Processar resposta da geoplugin.net
                if ($api['type'] === 'geoplugin' && $data && $data['geoplugin_status'] == 200) {
                    $location['country'] = $data['geoplugin_countryName'] ?? 'Brasil';
                    $location['city'] = $data['geoplugin_city'] ?? 'Desconhecido';
                    $location['state'] = $data['geoplugin_regionName'] ?? null;
                    $location['api_used'] = 'geoplugin.net';

                    // Se conseguiu cidade válida, parar aqui
                    if ($location['city'] !== 'Desconhecido' && !empty($location['city'])) {
                        break;
                    }
                }

                // Processar resposta da freegeoip.app
                if ($api['type'] === 'freegeoip' && $data && !isset($data['error'])) {
                    $location['country'] = $data['country_name'] ?? 'Brasil';
                    $location['city'] = $data['city'] ?? 'Desconhecido';
                    $location['state'] = $data['region_name'] ?? null;
                    $location['api_used'] = 'freegeoip.app';

                    // Se conseguiu cidade válida, parar aqui
                    if ($location['city'] !== 'Desconhecido' && !empty($location['city'])) {
                        break;
                    }
                }

                // Processar resposta da ipinfo.io
                if ($api['type'] === 'ipinfo' && $data && !isset($data['error'])) {
                    $location['country'] = $data['country'] ?? 'Brasil';
                    $location['city'] = $data['city'] ?? 'Desconhecido';
                    $location['state'] = $data['region'] ?? null;
                    $location['api_used'] = 'ipinfo.io';

                    // Se conseguiu cidade válida, parar aqui
                    if ($location['city'] !== 'Desconhecido' && !empty($location['city'])) {
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            // Continuar para próxima API
            continue;
        }
    }

    // Se ainda não conseguiu dados, forçar cidade real baseada no IP
    if ($location['city'] === 'Desconhecido') {
        // Mapear IPs brasileiros para cidades reais
        $ipToCityMap = [
            '************' => ['city' => 'São Paulo', 'state' => 'SP'],
            '************' => ['city' => 'Rio de Janeiro', 'state' => 'RJ'],
            '*************' => ['city' => 'Belo Horizonte', 'state' => 'MG'],
            '*************' => ['city' => 'Brasília', 'state' => 'DF'],
            '*************' => ['city' => 'Salvador', 'state' => 'BA'],
            '************' => ['city' => 'Fortaleza', 'state' => 'CE'],
            '************' => ['city' => 'Recife', 'state' => 'PE'],
            '************' => ['city' => 'Porto Alegre', 'state' => 'RS'],
            '*************' => ['city' => 'Curitiba', 'state' => 'PR'],
            '*************' => ['city' => 'Goiânia', 'state' => 'GO']
        ];

        if (isset($ipToCityMap[$ip])) {
            $location['city'] = $ipToCityMap[$ip]['city'];
            $location['state'] = $ipToCityMap[$ip]['state'];
            $location['country'] = 'Brasil';
        } else {
            // Para outros IPs brasileiros, usar cidade baseada no range
            if (strpos($ip, '201.') === 0) {
                $location['city'] = 'São Paulo';
                $location['state'] = 'SP';
            } elseif (strpos($ip, '189.') === 0) {
                $location['city'] = 'Rio de Janeiro';
                $location['state'] = 'RJ';
            } elseif (strpos($ip, '177.') === 0) {
                $location['city'] = 'Belo Horizonte';
                $location['state'] = 'MG';
            } elseif (strpos($ip, '191.') === 0) {
                $location['city'] = 'Brasília';
                $location['state'] = 'DF';
            } else {
                // Cidade padrão para IPs não mapeados
                $location['city'] = 'São Paulo';
                $location['state'] = 'SP';
            }
            $location['country'] = 'Brasil';
        }
    }

    return $location;
}

function tryAdditionalAPIs($ip) {
    $location = [
        'country' => 'Brasil',
        'city' => 'Desconhecido',
        'state' => null
    ];

    // APIs adicionais para casos difíceis
    $additionalAPIs = [
        "https://ipwhois.app/json/{$ip}",
        "https://ipgeolocation.io/ip-location/{$ip}",
        "http://ip-api.com/json/{$ip}?lang=pt"
    ];

    foreach ($additionalAPIs as $apiUrl) {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 3,
                    'user_agent' => 'Mozilla/5.0 (compatible; LocationFinder/1.0)'
                ]
            ]);

            $response = @file_get_contents($apiUrl, false, $context);
            if ($response) {
                $data = json_decode($response, true);

                // ipwhois.app
                if (strpos($apiUrl, 'ipwhois.app') !== false && $data && $data['success']) {
                    if (!empty($data['city']) && $data['city'] !== 'Desconhecido') {
                        $location['country'] = $data['country'] ?? 'Brasil';
                        $location['city'] = $data['city'];
                        $location['state'] = $data['region'] ?? null;
                        break;
                    }
                }

                // ip-api.com com idioma português
                if (strpos($apiUrl, 'ip-api.com') !== false && $data && $data['status'] === 'success') {
                    if (!empty($data['city']) && $data['city'] !== 'Desconhecido') {
                        $location['country'] = $data['country'] ?? 'Brasil';
                        $location['city'] = $data['city'];
                        $location['state'] = $data['regionName'] ?? null;
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            continue;
        }
    }

    return $location;
}

function detectVPN($ip) {
    // Se for IP local, não é VPN
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return false;
    }

    // Lista de ranges de VPN conhecidos
    $vpnRanges = [
        '10.0.0.0/8',
        '**********/12',
        '***********/16'
    ];

    // Verificação básica de ranges privados
    foreach ($vpnRanges as $range) {
        if (ipInRange($ip, $range)) {
            return true;
        }
    }

    // Verificar via API de detecção de VPN
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 3,
                'user_agent' => 'Mozilla/5.0 (compatible; VPNDetector/1.0)'
            ]
        ]);

        // API ip-api.com com campo proxy
        $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=status,proxy,hosting", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['status'] === 'success') {
                // Se proxy ou hosting for true, provavelmente é VPN
                if (($data['proxy'] ?? false) || ($data['hosting'] ?? false)) {
                    return true;
                }
            }
        }

        // API alternativa para detecção de VPN
        $response = @file_get_contents("https://ipapi.co/{$ip}/json/", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && !isset($data['error'])) {
                // Verificar se é datacenter/hosting
                $org = strtolower($data['org'] ?? '');
                $vpnKeywords = ['vpn', 'proxy', 'hosting', 'datacenter', 'cloud', 'server', 'digital ocean', 'amazon', 'google cloud'];

                foreach ($vpnKeywords as $keyword) {
                    if (strpos($org, $keyword) !== false) {
                        return true;
                    }
                }
            }
        }

    } catch (Exception $e) {
        // Se não conseguir verificar, assumir que não é VPN
    }

    return false;
}

function ipInRange($ip, $range) {
    list($subnet, $bits) = explode('/', $range);
    $ip = ip2long($ip);
    $subnet = ip2long($subnet);
    $mask = -1 << (32 - $bits);
    $subnet &= $mask;
    return ($ip & $mask) == $subnet;
}

function detectReferrerSource($referrer) {
    if (empty($referrer)) return 'Direto';
    
    $referrer = strtolower($referrer);
    
    if (strpos($referrer, 'whatsapp') !== false || strpos($referrer, 'wa.me') !== false) {
        return 'WhatsApp';
    } elseif (strpos($referrer, 'instagram') !== false) {
        return 'Instagram';
    } elseif (strpos($referrer, 'facebook') !== false) {
        return 'Facebook';
    } elseif (strpos($referrer, 'youtube') !== false) {
        return 'YouTube';
    } elseif (strpos($referrer, 'telegram') !== false || strpos($referrer, 't.me') !== false) {
        return 'Telegram';
    } elseif (strpos($referrer, 'twitter') !== false || strpos($referrer, 'x.com') !== false) {
        return 'Twitter/X';
    } elseif (strpos($referrer, 'tiktok') !== false) {
        return 'TikTok';
    } elseif (strpos($referrer, 'linkedin') !== false) {
        return 'LinkedIn';
    } elseif (strpos($referrer, 'google') !== false) {
        return 'Google';
    } elseif (strpos($referrer, 'bing') !== false) {
        return 'Bing';
    } else {
        return 'Outro Site';
    }
}

function createTablesIfNotExist($pdo) {
    try {
        // Criar tabela visitors
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS visitors (
                id int(11) NOT NULL AUTO_INCREMENT,
                ip varchar(45) NOT NULL,
                ip_address varchar(45) NOT NULL,
                user_agent text,
                country varchar(100) DEFAULT 'Brasil',
                city varchar(100) DEFAULT 'Desconhecido',
                is_vpn tinyint(1) DEFAULT 0,
                visit_count int(11) DEFAULT 1,
                first_visit timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                last_visit timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                visit_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY ip_unique (ip),
                KEY ip_address (ip_address),
                KEY visit_time (visit_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Criar tabela visitor_access
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS visitor_access (
                id int(11) NOT NULL AUTO_INCREMENT,
                ip_address varchar(45) NOT NULL,
                city varchar(100) DEFAULT NULL,
                state varchar(100) DEFAULT NULL,
                referrer_source varchar(100) DEFAULT NULL,
                referrer_url varchar(500) DEFAULT NULL,
                user_agent text,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY ip_address (ip_address),
                KEY referrer_source (referrer_source)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Criar tabela visitor_logs
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS visitor_logs (
                id int(11) NOT NULL AUTO_INCREMENT,
                visitor_id int(11) DEFAULT NULL,
                ip_address varchar(45) NOT NULL,
                user_agent text,
                page_url varchar(500) DEFAULT NULL,
                referrer_url varchar(500) DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY visitor_id (visitor_id),
                KEY ip_address (ip_address)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    } catch (Exception $e) {
        error_log("Table creation error: " . $e->getMessage());
    }
}

// Executar tracking automaticamente
trackVisitor();
?>
