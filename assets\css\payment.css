/* Variáveis e configurações globais */
:root {
    --primary-color: #4318FF;
    --primary-hover: #3a14e0;
    --success-color: #32CD32;
    --success-hover: #228B22;
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --gray-light: #f8f9fa;
    --gray-medium: #e0e0e0;
    --gray-dark: #666;
    --text-color: #333;
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Reset e estilos base */
.modal-dialog {
    max-width: 400px;
    width: 95%;
    margin: 1rem auto;
    display: flex;
    align-items: center;
    min-height: calc(100% - 2rem);
}

.payment-modal {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modal-content {
    border-radius: 12px;
    border: none;
    background: white;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
    max-height: 98vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background: white;
    border-radius: 12px 12px 0 0;
}

.modal-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.modal-body {
    padding: 1.25rem;
    background: white;
    overflow-y: auto;
    flex: 1;
}

/* Valor a Pagar */
.pix-amount {
    text-align: center;
    margin-bottom: 1.5rem;
}

.pix-amount h4 {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.pix-amount h3 {
    color: #0066FF;
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0;
}

/* QR Code */
.qr-code-container {
    text-align: center;
    margin-bottom: 1.5rem;
}

.qr-code-container img {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    display: block;
}

/* Campo PIX */
.pix-code-container {
    margin-bottom: 1.5rem;
}

.pix-code-container p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.input-group {
    display: flex;
    align-items: stretch;
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
}

.form-control {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px;
    font-family: monospace;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.btn-copy {
    min-width: 80px;
    border: none;
    background: #0066FF;
    color: white;
    font-weight: 500;
    font-size: 14px;
    padding: 0 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-copy:hover {
    background: #0052cc;
}

.btn-copy.copied {
    background: #28a745;
}

/* Instruções */
.pix-instructions {
    text-align: left;
}

.pix-instructions h6 {
    color: #333;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.pix-instructions ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
    list-style-position: outside;
}

.pix-instructions li {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.alert-info {
    background: #E3F2FD;
    border: none;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.625rem;
}

.alert-info i {
    color: #0066FF;
    font-size: 0.875rem;
    margin-top: 0.125rem;
}

.alert-info p {
    color: #333;
    font-size: 0.85rem;
    margin: 0;
    line-height: 1.4;
}

/* Footer */
.modal-footer {
    text-align: center;
    padding: 12px 16px;
    background: white;
    border-top: 1px solid #eee;
}

.copyright-text {
    color: #999;
    font-size: 0.75rem;
    margin: 0;
    text-align: center;
    line-height: 1.4;
}

/* Responsividade */
@media screen and (max-width: 480px) {
    .modal-dialog {
        margin: 0;
        width: 100%;
        min-height: 100%;
    }

    .modal-content {
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }

    .modal-body {
        padding: 1rem;
    }

    .qr-code-container {
        margin: 1rem 0;
    }

    .qr-code-container img {
        width: 180px;
        height: 180px;
    }

    .pix-amount {
        margin: 1rem 0;
    }

    .pix-amount h3 {
        font-size: 1.5rem;
    }

    .pix-code-container {
        margin: 1rem 0;
    }

    .form-control {
        font-size: 12px;
        padding: 10px;
    }

    .btn-copy {
        min-width: 70px;
        font-size: 13px;
    }

    .input-group {
        margin-bottom: 1rem;
    }

    .alert-info {
        margin: 1rem 0;
    }
}

@media screen and (min-width: 481px) and (max-width: 768px) {
    .modal-dialog {
        margin: 1rem auto;
        min-height: calc(100% - 2rem);
    }

    .modal-content {
        max-height: calc(100vh - 2rem);
    }

    .qr-code-container img {
        width: 220px;
        height: 220px;
    }

    .form-control {
        font-size: 12px;
    }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
    .modal-dialog {
        max-width: 480px;
    }

    .qr-code-container img {
        width: 240px;
        height: 240px;
    }
}

@media screen and (max-height: 600px) {
    .modal-dialog {
        margin: 0;
    }

    .modal-content {
        max-height: 100vh;
        border-radius: 0;
    }

    .modal-body {
        padding: 0.75rem;
    }

    .qr-code-container img {
        width: 150px;
        height: 150px;
    }

    .pix-amount {
        margin: 0.75rem 0;
    }

    .pix-amount h3 {
        font-size: 1.25rem;
    }

    .pix-code-container {
        margin: 0.75rem 0;
    }

    .alert-info {
        padding: 0.5rem;
        margin: 0.75rem 0;
    }
}

/* Opções de Pagamento */
.payment-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
}

.payment-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid var(--gray-medium);
    border-radius: var(--border-radius);
    background: #fff;
    cursor: pointer;
    transition: var(--transition);
    min-width: 150px;
}

.payment-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.payment-option i {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.payment-option span {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
}

/* Container PIX */
.pix-container {
    padding: 20px;
    text-align: center;
}

/* Instruções PIX */
.pix-instructions {
    margin: 20px 0;
    text-align: left;
}

.pix-instructions p {
    margin: 10px 0;
    color: var(--gray-dark);
    font-size: 14px;
    line-height: 1.4;
}

/* Estilo do campo PIX e botão copiar */
.pix-code-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--gray-light);
    padding: 8px;
    border-radius: 5px;
    margin: 15px 0;
}

/* Botões do Modal */
.popup button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.popup button.confirm {
    background: var(--success-color);
    color: white;
    margin-right: 10px;
}

.popup button.confirm:hover {
    background: var(--success-hover);
}

.popup button.cancel {
    background: var(--danger-color);
    color: white;
}

.popup button.cancel:hover {
    background: var(--danger-hover);
}

/* Ajuste responsivo para o container do código PIX */
@media screen and (max-width: 480px) {
    .pix-code-container {
        flex-direction: column;
        gap: 8px;
        padding: 10px;
    }

    #pixCode {
        width: 100%;
        font-size: 12px;
    }

    .copy-button {
        width: 100%;
        padding: 10px;
    }
}

/* Media Queries para Responsividade */
@media screen and (max-width: 768px) {
    .payment-options {
        flex-direction: column;
        gap: 15px;
    }

    .payment-option {
        width: 100%;
        min-width: auto;
        padding: 15px;
    }

    .qr-code {
        margin: 15px auto;
        padding: 15px;
        max-width: 200px;
    }

    .pix-container {
        padding: 15px 10px;
    }

    .pix-info {
        margin-top: 15px;
        padding: 12px;
    }

    .popup {
        width: 95%;
        padding: 20px;
    }
}

@media screen and (max-width: 480px) {
    .payment-modal {
        font-size: 14px;
    }

    .qr-code {
        max-width: 180px;
        padding: 10px;
    }

    .pix-instructions p {
        font-size: 13px;
    }

    .pix-info p {
        font-size: 12px;
    }
}

/* Ajustes específicos para telas muito pequenas */
@media screen and (max-width: 320px) {
    .qr-code {
        max-width: 150px;
    }

    .payment-modal {
        font-size: 13px;
    }

    .pix-container {
        padding: 10px 5px;
    }
}

/* Estilos para a tabela de vendas recentes */
.recent-sales {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recent-sales h2 {
    color: #333;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th {
    background: #f8f9fa;
    color: #666;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 2px solid #eee;
    white-space: nowrap;
}

.table td {
    padding: 0.75rem;
    color: #333;
    font-size: 0.875rem;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.table tr:last-child td {
    border-bottom: none;
}

.text-success {
    color: #28a745;
    font-weight: 500;
}

.text-warning {
    color: #ffc107;
    font-weight: 500;
}

/* Responsividade da tabela */
@media screen and (max-width: 768px) {
    .recent-sales {
        padding: 1rem;
        margin: 1rem;
        border-radius: 8px;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.8125rem;
    }
}