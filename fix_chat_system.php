<?php
require_once 'database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>Corrigindo Sistema de Chat...</h2>";
    
    // 1. Verificar e criar tabelas necessárias
    echo "<h3>1. Verificando tabelas...</h3>";
    
    // Tabela chat_users
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            whatsapp VARCHAR(20),
            is_admin TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_is_admin (is_admin),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_users verificada</p>";
    
    // Tabela chat_messages
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL,
            is_admin TINYINT(1) DEFAULT 0,
            timestamp INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_timestamp (timestamp),
            INDEX idx_is_admin (is_admin),
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_messages verificada</p>";
    
    // Tabela chat_sessions
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            status ENUM('pending', 'active', 'closed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_sessions verificada</p>";
    
    // 2. Verificar se existe admin na tabela chat_users
    echo "<h3>2. Verificando admin no sistema de chat...</h3>";
    
    $stmt = $pdo->query("SELECT id, name, email FROM admins LIMIT 1");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        // Inserir admin na tabela chat_users se não existir
        $stmt = $pdo->prepare("
            INSERT INTO chat_users (id, name, email, is_admin, created_at, last_activity) 
            VALUES (?, ?, ?, 1, NOW(), NOW())
            ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                email = VALUES(email),
                is_admin = 1
        ");
        $stmt->execute([$admin['id'], $admin['name'], $admin['email']]);
        echo "<p>✓ Admin sincronizado na tabela chat_users</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Nenhum admin encontrado na tabela admins</p>";
    }
    
    // 3. Testar inserção de mensagem
    echo "<h3>3. Testando sistema de mensagens...</h3>";
    
    // Criar usuário de teste se não existir
    $stmt = $pdo->prepare("
        INSERT INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) 
        VALUES ('Usuário Teste', '<EMAIL>', '11999999999', 0, NOW(), NOW())
        ON DUPLICATE KEY UPDATE last_activity = NOW()
    ");
    $stmt->execute();
    
    $testUserId = $pdo->lastInsertId();
    if ($testUserId == 0) {
        // Se não inseriu, buscar o existente
        $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE email = '<EMAIL>' AND is_admin = 0");
        $stmt->execute();
        $testUserId = $stmt->fetchColumn();
    }
    
    if ($testUserId) {
        // Inserir mensagem de teste
        $stmt = $pdo->prepare("
            INSERT INTO chat_messages (user_id, message, is_admin, timestamp) 
            VALUES (?, 'Mensagem de teste do sistema', 0, UNIX_TIMESTAMP())
        ");
        $stmt->execute([$testUserId]);
        echo "<p>✓ Mensagem de teste inserida com sucesso</p>";
        
        // Verificar se a mensagem foi inserida
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM chat_messages WHERE user_id = ?");
        $stmt->execute([$testUserId]);
        $messageCount = $stmt->fetchColumn();
        echo "<p>✓ Total de mensagens para usuário teste: $messageCount</p>";
    }
    
    // 4. Verificar APIs
    echo "<h3>4. Verificando arquivos de API...</h3>";
    
    $apiFiles = [
        'api/send_message.php',
        'api/get_messages.php',
        'api/create_user.php'
    ];
    
    foreach ($apiFiles as $file) {
        if (file_exists($file)) {
            echo "<p>✓ $file existe</p>";
        } else {
            echo "<p style='color: red;'>✗ $file não encontrado</p>";
        }
    }
    
    // 5. Verificar permissões de escrita
    echo "<h3>5. Verificando permissões...</h3>";
    
    if (is_writable('database/')) {
        echo "<p>✓ Diretório database/ tem permissão de escrita</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Diretório database/ pode não ter permissão de escrita</p>";
    }
    
    // 6. Estatísticas finais
    echo "<h3>6. Estatísticas do sistema:</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users WHERE is_admin = 0");
    $userCount = $stmt->fetchColumn();
    echo "<p>Total de usuários: $userCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_users WHERE is_admin = 1");
    $adminCount = $stmt->fetchColumn();
    echo "<p>Total de admins: $adminCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_messages");
    $messageCount = $stmt->fetchColumn();
    echo "<p>Total de mensagens: $messageCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions");
    $sessionCount = $stmt->fetchColumn();
    echo "<p>Total de sessões: $sessionCount</p>";
    
    echo "<h3 style='color: green;'>✓ Sistema de chat corrigido com sucesso!</h3>";
    echo "<p><a href='chat.php' class='btn btn-primary'>Testar Chat</a></p>";
    echo "<p><a href='admin/chat.php' class='btn btn-success'>Painel Admin Chat</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
