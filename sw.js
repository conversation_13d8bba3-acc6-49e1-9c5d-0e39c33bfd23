// Service Worker para Loja Online - KESUNG SITE
const CACHE_NAME = 'kesung-site-online-v1.0.1';
const urlsToCache = [
  '/',
  '/index.php',
  '/assets/css/style.css',
  '/assets/css/responsive.css',
  '/assets/css/chat.css',
  '/assets/js/simple_pix.js',
  '/assets/js/chat.js',
  '/assets/icons/icon-192x192.png',
  '/assets/icons/icon-512x512.png',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
  'https://cdn.jsdelivr.net/npm/sweetalert2@11'
];

// Instalar Service Worker
self.addEventListener('install', event => {
  console.log('Service Worker: Instalando...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Cache aberto');
        return cache.addAll(urlsToCache);
      })
      .catch(err => console.log('Service Worker: Erro ao cachear:', err))
  );
});

// Ativar Service Worker
self.addEventListener('activate', event => {
  console.log('Service Worker: Ativando...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Removendo cache antigo:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Interceptar requisições - Loja Online (sempre conectada)
self.addEventListener('fetch', event => {
  // Para loja online, sempre buscar dados atualizados da rede
  if (event.request.url.includes('/api/') ||
      event.request.url.includes('/get_product.php') ||
      event.request.url.includes('/admin/') ||
      event.request.method === 'POST') {
    // Sempre buscar dados críticos da rede
    event.respondWith(fetch(event.request));
    return;
  }

  // Cache apenas para recursos estáticos (CSS, JS, imagens)
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Para recursos estáticos, usar cache se disponível
        if (response && (
          event.request.url.includes('.css') ||
          event.request.url.includes('.js') ||
          event.request.url.includes('.png') ||
          event.request.url.includes('.jpg') ||
          event.request.url.includes('.svg')
        )) {
          return response;
        }
        // Sempre buscar dados atualizados da rede
        return fetch(event.request);
      })
      .catch(() => {
        // Se falhar, mostrar mensagem de conexão necessária
        if (event.request.destination === 'document') {
          return new Response(`
            <!DOCTYPE html>
            <html>
            <head>
              <title>Conexão Necessária - KESUNG SITE</title>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                .icon { font-size: 64px; color: #dc3545; margin-bottom: 20px; }
                h1 { color: #333; margin-bottom: 20px; }
                p { color: #666; margin-bottom: 30px; }
                .btn { background: #4318FF; color: white; padding: 12px 24px; border: none; border-radius: 8px; text-decoration: none; display: inline-block; }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="icon">🌐</div>
                <h1>Conexão com Internet Necessária</h1>
                <p>Nossa loja online precisa de conexão com a internet para funcionar corretamente e mostrar produtos atualizados.</p>
                <a href="/" class="btn" onclick="window.location.reload()">Tentar Novamente</a>
              </div>
            </body>
            </html>
          `, {
            headers: { 'Content-Type': 'text/html' }
          });
        }
      })
  );
});

// Notificações Push
self.addEventListener('push', event => {
  console.log('Service Worker: Push recebido');

  let data = {};
  if (event.data) {
    data = event.data.json();
  }

  // Determinar tipo de notificação e som
  const notificationType = data.type || 'default';
  let soundFile = 'notification.mp3';

  switch(notificationType) {
    case 'chat':
      soundFile = 'chat.mp3';
      break;
    case 'promocao':
      soundFile = 'promocao.mp3';
      break;
    case 'bolao':
      soundFile = 'bolao.mp3';
      break;
    case 'carrinho':
      soundFile = 'carrinho.mp3';
      break;
    default:
      soundFile = 'notification.mp3';
  }

  const options = {
    title: data.title || 'KESUNG SITE',
    body: data.body || 'Nova mensagem disponível!',
    icon: '/assets/icons/icon-192x192.png',
    badge: '/assets/icons/icon-72x72.png',
    image: data.image || '/assets/icons/icon-384x384.png',
    vibrate: [200, 100, 200, 100, 200, 100, 200],
    tag: data.tag || 'kesung-notification',
    renotify: true,
    requireInteraction: false,
    silent: false, // Permitir som nativo
    actions: [
      {
        action: 'open',
        title: 'Abrir',
        icon: '/assets/icons/icon-96x96.png'
      },
      {
        action: 'close',
        title: 'Fechar',
        icon: '/assets/icons/icon-96x96.png'
      }
    ],
    data: {
      url: data.url || null, // URL opcional
      timestamp: Date.now(),
      soundFile: soundFile,
      type: notificationType
    }
  };

  event.waitUntil(
    self.registration.showNotification(options.title, options).then(() => {
      // Tocar som personalizado
      return self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'PLAY_NOTIFICATION_SOUND',
            soundFile: soundFile,
            notificationType: notificationType
          });
        });
      });
    })
  );
});

// Clique na notificação
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notificação clicada');

  event.notification.close();

  if (event.action === 'close') {
    return;
  }

  // URL é opcional - se não tiver, não faz nada (só fecha a notificação)
  const urlToOpen = event.notification.data.url;

  // Se não tem URL, apenas fecha a notificação
  if (!urlToOpen || urlToOpen === '' || urlToOpen === '/') {
    console.log('Notificação sem URL - apenas fechando');
    return;
  }

  // Se tem URL, abre/navega
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then(clientList => {
      // Verificar se já existe uma janela aberta
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          client.navigate(urlToOpen);
          return client.focus();
        }
      }

      // Abrir nova janela se não existir
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Monitoramento de conexão para loja online
self.addEventListener('online', () => {
  console.log('Service Worker: Conexão restaurada - loja online ativa');

  // Notificar usuário que a conexão foi restaurada
  self.registration.showNotification('Conexão Restaurada', {
    body: 'A loja está online novamente! Produtos e preços atualizados.',
    icon: '/assets/icons/icon-192x192.png',
    tag: 'connection-restored',
    actions: [
      {action: 'refresh', title: 'Atualizar Página'}
    ]
  });
});

self.addEventListener('offline', () => {
  console.log('Service Worker: Conexão perdida - loja offline');

  // Notificar usuário sobre perda de conexão
  self.registration.showNotification('Conexão Perdida', {
    body: 'Verifique sua conexão com a internet para continuar comprando.',
    icon: '/assets/icons/icon-192x192.png',
    tag: 'connection-lost',
    requireInteraction: true
  });
});

// Mensagens do cliente
self.addEventListener('message', event => {
  console.log('Service Worker: Mensagem recebida:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({version: CACHE_NAME});
  }
});

// Atualização automática
self.addEventListener('updatefound', () => {
  console.log('Service Worker: Nova versão encontrada');
  
  const newWorker = self.registration.installing;
  newWorker.addEventListener('statechange', () => {
    if (newWorker.state === 'installed') {
      if (navigator.serviceWorker.controller) {
        // Nova versão disponível
        self.registration.showNotification('Atualização Disponível', {
          body: 'Uma nova versão do app está disponível!',
          icon: '/assets/icons/icon-192x192.png',
          actions: [
            {action: 'update', title: 'Atualizar'},
            {action: 'later', title: 'Mais tarde'}
          ]
        });
      }
    }
  });
});
