{"paging": {"offset": 0, "limit": 2, "total": 7}, "results": [{"id": "2c9380847e9b1dd5017ea15e30fa01ee", "status": "pending", "reason": "Plan", "summarized": {"quotas": 25, "pending_charge_quantity": 25, "pending_charge_amount": 250.0}, "payer_id": 766790067, "back_url": "https://www.mercadopago.com.br", "collector_id": 823549964, "application_id": 6245132082630004, "external_reference": "23546246234", "next_payment_date": "2022-01-10T10:10:10.000-00:00", "date_created": "2022-01-28T11:46:51.768-04:00", "last_modified": "2022-01-28T11:46:51.770-04:00", "init_point": "https://www.mercadopago.com.br/subscriptions/checkout?preapproval_id=2c9380847e9b1dd5017ea15e30fa01ee", "auto_recurring": {"frequency": 1, "frequency_type": "months", "transaction_amount": 10.0, "currency_id": "BRL", "start_date": "2020-06-02T09:07:14.260-04:00", "end_date": "2022-07-20T11:59:52.581-04:00"}, "payer_first_name": "Test", "payer_last_name": ""}, {"id": "2c9380847e9b1dd5017ea15f234701f0", "status": "pending", "reason": "Subscription", "summarized": {"quotas": 25, "pending_charge_quantity": 25, "pending_charge_amount": 250.0}, "payer_id": 766790067, "back_url": "https://www.mercadopago.com.br", "collector_id": 823549964, "application_id": 6245132082630004, "external_reference": "23546246234", "next_payment_date": "2022-01-10T10:10:10.000-00:00", "date_created": "2022-01-28T11:47:53.795-04:00", "last_modified": "2022-01-28T11:47:53.799-04:00", "init_point": "https://www.mercadopago.com.br/subscriptions/checkout?preapproval_id=2c9380847e9b1dd5017ea15f234701f0", "auto_recurring": {"frequency": 1, "frequency_type": "months", "transaction_amount": 10.0, "currency_id": "BRL", "start_date": "2020-06-02T09:07:14.260-04:00", "end_date": "2022-07-20T11:59:52.581-04:00"}, "payer_first_name": "Test", "payer_last_name": ""}]}