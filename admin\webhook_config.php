<?php
/**
 * Configuração de Webhook - Sistema kesung-site
 * Interface para configurar tokens e testar webhooks
 */

require_once '../database/connection.php';

// Verificar se é admin (implementar autenticação)
session_start();

$message = '';
$messageType = '';

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'save_token') {
            $token = trim($_POST['webhook_token'] ?? '');
            
            if (empty($token)) {
                throw new Exception('Token não pode estar vazio');
            }
            
            // Criar diretório se não existir
            $configDir = __DIR__ . '/../config';
            if (!is_dir($configDir)) {
                mkdir($configDir, 0755, true);
            }
            
            // Salvar token
            $tokenFile = $configDir . '/webhook_token.txt';
            file_put_contents($tokenFile, $token);
            
            $message = 'Token salvo com sucesso!';
            $messageType = 'success';
            
        } elseif ($action === 'generate_token') {
            // Gerar token aleatório
            $token = bin2hex(random_bytes(32));
            
            $configDir = __DIR__ . '/../config';
            if (!is_dir($configDir)) {
                mkdir($configDir, 0755, true);
            }
            
            $tokenFile = $configDir . '/webhook_token.txt';
            file_put_contents($tokenFile, $token);
            
            $message = 'Token gerado e salvo com sucesso!';
            $messageType = 'success';
            
        } elseif ($action === 'test_webhook') {
            // Testar webhook
            $testData = [
                'event' => 'PAYMENT_RECEIVED',
                'payment' => [
                    'id' => 'test_payment_' . uniqid(),
                    'status' => 'RECEIVED',
                    'value' => 99.99,
                    'externalReference' => 'TEST_' . uniqid()
                ]
            ];
            
            $webhookUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/webhook_v2.php';
            
            $ch = curl_init($webhookUrl);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($testData),
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . getCurrentToken()
                ],
                CURLOPT_TIMEOUT => 10
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $message = 'Teste de webhook realizado com sucesso! Resposta: ' . $response;
                $messageType = 'success';
            } else {
                $message = 'Falha no teste de webhook. Código: ' . $httpCode . ' Resposta: ' . $response;
                $messageType = 'danger';
            }
        }
        
    } catch (Exception $e) {
        $message = 'Erro: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

function getCurrentToken() {
    $tokenFile = __DIR__ . '/../config/webhook_token.txt';
    return file_exists($tokenFile) ? trim(file_get_contents($tokenFile)) : '';
}

function getWebhookLogs() {
    $logDir = __DIR__ . '/../logs';
    $logFile = $logDir . '/webhook_' . date('Y-m-d') . '.log';
    
    if (!file_exists($logFile)) {
        return 'Nenhum log encontrado para hoje.';
    }
    
    $logs = file_get_contents($logFile);
    $lines = explode("\n", $logs);
    
    // Pegar últimas 50 linhas
    $recentLogs = array_slice($lines, -50);
    return implode("\n", $recentLogs);
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuração de Webhook - Kesung Site</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-webhook me-2"></i>Configuração de Webhook</h1>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </a>
                </div>
                
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <!-- Configuração do Token -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-key me-2"></i>Token de Segurança</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="save_token">
                            <div class="mb-3">
                                <label for="webhook_token" class="form-label">Token do Webhook</label>
                                <div class="input-group">
                                    <input type="text" class="form-control font-monospace" id="webhook_token" 
                                           name="webhook_token" value="<?= htmlspecialchars(getCurrentToken()) ?>" 
                                           placeholder="Digite o token de segurança">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Salvar
                                    </button>
                                </div>
                                <div class="form-text">
                                    Este token será usado para validar webhooks do Asaas.
                                </div>
                            </div>
                        </form>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="generate_token">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-random me-1"></i>Gerar Token Aleatório
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Informações do Webhook -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>Informações do Webhook</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>URL do Webhook:</strong><br>
                                <code>https://<?= $_SERVER['HTTP_HOST'] ?>/webhook_v2.php</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Eventos Suportados:</strong><br>
                                <small>
                                    • PAYMENT_RECEIVED<br>
                                    • PAYMENT_CONFIRMED<br>
                                    • PAYMENT_OVERDUE
                                </small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <strong>Como configurar no Asaas:</strong><br>
                            1. Acesse sua conta no Asaas<br>
                            2. Vá em Configurações → Webhooks<br>
                            3. Adicione a URL acima<br>
                            4. Configure o token no header Authorization: Bearer [TOKEN]
                        </div>
                    </div>
                </div>
                
                <!-- Teste de Webhook -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-flask me-2"></i>Teste de Webhook</h5>
                    </div>
                    <div class="card-body">
                        <p>Teste se o webhook está funcionando corretamente:</p>
                        <form method="POST">
                            <input type="hidden" name="action" value="test_webhook">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-play me-1"></i>Executar Teste
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Logs Recentes -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-alt me-2"></i>Logs Recentes</h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 12px;"><?= htmlspecialchars(getWebhookLogs()) ?></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
