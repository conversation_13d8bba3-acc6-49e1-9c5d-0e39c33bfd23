<?php
require_once '../includes/EmailManager.php';
require_once 'database/connection.php';

header('Content-Type: application/json');

try {
    if (!isset($_POST['order_id'])) {
        throw new Exception('ID do pedido não fornecido');
    }
    
    $orderId = $_POST['order_id'];
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Buscar informações do pedido
    $stmt = $pdo->prepare("
        SELECT p.*, pr.name as product_name, c.name as customer_name, c.email 
        FROM payments p 
        JOIN products pr ON p.product_id = pr.id 
        JOIN customers c ON p.customer_id = c.id 
        WHERE p.id = ?
    ");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception('Pedido não encontrado');
    }
    
    // Enviar email
    $emailManager = new EmailManager($pdo);
    $emailManager->sendOrderConfirmation($order);
    
    die(json_encode([
        'success' => true,
        'message' => 'Email reenviado com sucesso'
    ]));
    
} catch (Exception $e) {
    http_response_code(500);
    die(json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]));
}
