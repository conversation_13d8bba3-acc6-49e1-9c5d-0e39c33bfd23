{"id": 17014025134, "date_created": "2022-01-10T10:10:10.000-00:00", "date_approved": "2022-01-10T10:10:10.000-00:00", "date_last_updated": "2022-01-10T10:10:10.000-00:00", "date_of_expiration": null, "money_release_date": "2022-01-10T10:10:10.000-00:00", "operation_type": "regular_payment", "issuer_id": "24", "payment_method_id": "master", "payment_type_id": "credit_card", "status": "approved", "status_detail": "accredited", "currency_id": "BRL", "description": "PEDIDO NOVO - VIDEOGAME", "live_mode": true, "sponsor_id": null, "authorization_code": "301299", "money_release_schema": null, "taxes_amount": 0, "counter_currency": null, "shipping_amount": 0, "pos_id": null, "store_id": null, "integrator_id": null, "platform_id": null, "corporation_id": null, "collector_id": *********, "payer": {"type": null, "id": "*********", "operator_id": null, "email": "<EMAIL>", "identification": {"type": "CPF", "number": "***********"}, "phone": {"area_code": null, "number": null, "extension": null}, "first_name": null, "last_name": null, "entity_type": null}, "marketplace_owner": null, "metadata": {"order_number": "order_1631894348"}, "internal_metadata": {"rule_engine": {"with_promise": false, "valid_promise": false, "rules": [{"rule_id": 21002808561, "rule_set": "processing_fee_and_release"}, {"rule_id": 21000231962, "rule_set": "financing_fee_collector"}]}, "unified_processing": true, "3ds_disabled_reason": "none", "approval_optimization_context": [{"trx_id": "86000149282_377b7377776e737f3377", "profile_id": "g2_getnet_getnet_7187497", "gtw_context": {"response_code": null, "http_status": null, "operation": "authorization"}, "approval_decision": {"abtesting_flows": null, "deferred_retry": true, "data_only": false, "retry_after_time": "2022-12-27T21:00:00Z", "approval_flows": ["tokenization", "threeds", "data_only", "no_cvv", "default"], "three_ds": true, "remove_cvv": false, "operation_mode": "async", "best_flows": ["threeds"]}, "mcc": null, "attempt": 1, "security_code_data": null}], "g2": "on", "3ds_status": "CHALLENGE", "3ds_challenge": true, "3ds_challenge_drop_reason": "none", "rejected_by_penalty": false, "payment_method_id": "67696841", "pci_info": {"ct_x_forwarded_for": null, "pay_x_forwarded_for": "10.195.1.92, ************", "is_public": false}, "mcc_assigned": "5099", "internal_risk_analysis": "by_risk", "approval_decision": {"abtesting_flows": null, "deferred_retry": true, "data_only": false, "retry_after_time": "2022-12-27T21:00:00Z", "approval_flows": ["tokenization", "threeds", "data_only", "no_cvv", "default"], "three_ds": true, "remove_cvv": false, "operation_mode": "async", "best_flows": ["threeds"]}, "mcc_source": "DEFAULT"}, "additional_info": {"items": [{"id": "1941", "title": "Ingresso Antecipado", "description": "Natal Iluminado 2019", "picture_url": null, "category_id": "Tickets", "quantity": "1", "unit_price": "100.0"}], "payer": {"phone": {"area_code": "11", "number": "987654321"}, "address": {"zip_code": "06233-200", "street_name": "Av. das Nações Unidas", "street_number": "3003"}, "first_name": "Nome", "last_name": "Sobrenome", "registration_date": "2022-01-10T10:10:10.000-00:00"}, "shipments": {"receiver_address": {"zip_code": "95630000", "street_name": "<PERSON><PERSON>", "street_number": "15"}}, "available_balance": null, "nsu_processadora": null, "authentication_code": null}, "order": {}, "external_reference": "1631894348", "transaction_amount": 12.34, "transaction_amount_refunded": 0, "coupon_amount": 0, "differential_pricing_id": null, "deduction_schema": null, "installments": 1, "transaction_details": {"payment_method_reference_id": null, "net_received_amount": 11.72, "total_paid_amount": 12.34, "overpaid_amount": 0, "external_resource_url": null, "installment_amount": 12.34, "financial_institution": null, "payable_deferral_period": null, "acquirer_reference": null}, "fee_details": [{"type": "mercadopago_fee", "amount": 0.62, "fee_payer": "collector"}], "charges_details": [], "captured": false, "binary_mode": false, "call_for_authorize_id": null, "statement_descriptor": "Mercadopago*fake", "card": {"id": null, "first_six_digits": "503143", "last_four_digits": "6351", "expiration_month": 12, "expiration_year": 2022, "date_created": "2022-01-10T10:10:10.000-00:00", "date_last_updated": "2022-01-10T10:10:10.000-00:00", "cardholder": {"name": "APRO", "identification": {"number": "***********", "type": "CPF"}}}, "notification_url": "https://seu-site.com.br/webhookshttps://seu-site.com.br/webhooks", "refunds": [], "processing_mode": "aggregator", "merchant_account_id": null, "merchant_number": null, "acquirer_reconciliation": [], "point_of_interaction": {}, "three_ds_info": {"external_resource_url": "https://acs-public.tp.mastercard.com/api/v1/browser_challenges", "creq": "eyJ0aHJlZURTU2VydmVyVHJhbnNJRCI6ImE4NDQ1NTE2LThjNzktNGQ1NC04MjRmLTU5YzgzNDRiY2FjNCIsImFjc1RyYW5zSUQiOiJmZjVlMWM4YS00M2Y2LTQ5ZDEtYjhmMy02M2FmMzJkMzgwYTEiLCJjaGFsbGVuZ2VXaW5kb3dTaXplIjoiMDQiLCJtZXNzYWdlVHlwZSI6IkNSZXEiLCJtZXNzYWdlVmVyc2lvbiI6IjIuMS4wIn0"}}