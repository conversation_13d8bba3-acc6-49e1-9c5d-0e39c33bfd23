<?php
require_once 'includes/auth_check.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    if (!isset($_GET['id'])) {
        header('Location: products.php');
        exit;
    }
    
    // Get product data
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        header('Location: products.php');
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = $_POST['name'];
        $description = $_POST['description'];
        $price = $_POST['price'];
        $image = $product['image']; // Keep existing image by default
        
        // Handle image upload if new image is provided
        if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['image']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $newName = uniqid() . '.' . $ext;
                $uploadDir = '../uploads/products/';
                
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $newName)) {
                    // Delete old image if exists
                    if ($product['image'] && file_exists($uploadDir . $product['image'])) {
                        unlink($uploadDir . $product['image']);
                    }
                    $image = $newName;
                }
            }
        }
        
        $stmt = $pdo->prepare("
            UPDATE products
            SET name = ?, description = ?, price = ?, image = ?
            WHERE id = ?
        ");

        if ($stmt->execute([$name, $description, $price, $image, $_GET['id']])) {
            echo "<script>
                window.location.href = 'products.php';
            </script>";
            exit;
        }
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Include header after all possible redirects
require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fs-2 m-0">Editar Produto</h2>
        <a href="products.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Voltar
        </a>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">Nome do Produto</label>
                            <input type="text" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($product['name']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Descrição</label>
                            <textarea name="description" class="form-control" rows="4"
                                    ><?php echo htmlspecialchars($product['description']); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Preço</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="number" name="price" class="form-control" step="0.01" 
                                       value="<?php echo $product['price']; ?>" required>
                            </div>
                        </div>
                        

                        
                        <div class="mb-3">
                            <label class="form-label">Imagem</label>
                            <?php if ($product['image']): ?>
                                <div class="mb-2">
                                    <img src="../uploads/products/<?php echo $product['image']; ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                         class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            <?php endif; ?>
                            <input type="file" name="image" class="form-control" accept="image/*">
                            <small class="form-text text-muted">Deixe em branco para manter a imagem atual</small>
                        </div>
                    </div>
                </div>
                
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    Swal.fire({
        title: "Produto editado com sucesso!",
        icon: "success",
        draggable: true
    }).then((result) => {
        if (result.isConfirmed) {
            this.submit();
        }
    });
});
</script>
