<?php
require_once '../database/connection.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Método inválido');
    }

    $database = Database::getInstance();
    $pdo = $database->getConnection();

    $userId = $_GET['user_id'] ?? null;
    $lastTime = $_GET['last_time'] ?? 0;

    if (!$userId) {
        throw new Exception('ID do usuário é obrigatório');
    }

    // Construir a query base
    $query = "SELECT
                m.id,
                m.user_id,
                m.message,
                m.is_admin,
                m.timestamp,
                m.created_at,
                CASE
                    WHEN m.is_admin = 1 THEN 'Admin'
                    ELSE u.name
                END as sender_name
              FROM chat_messages m
              LEFT JOIN chat_users u ON m.user_id = u.id
              WHERE m.user_id = ?";

    $params = [$userId];

    // Adicionar filtro de tempo se fornecido
    if ($lastTime && $lastTime > 0) {
        $query .= " AND m.timestamp > ?";
        $params[] = $lastTime;
    }

    $query .= " ORDER BY m.timestamp ASC";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Atualizar última atividade do usuário
    $stmt = $pdo->prepare("UPDATE chat_users SET last_activity = NOW() WHERE id = ?");
    $stmt->execute([$userId]);

    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
