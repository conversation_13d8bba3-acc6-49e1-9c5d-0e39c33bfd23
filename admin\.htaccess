RewriteEngine On

# Ativar opções apropriadas
Options +FollowSymLinks

# Configurar permissões de acesso
<IfModule mod_authz_core.c>
    Require all granted
</IfModule>
<IfModule !mod_authz_core.c>
    Order allow,deny
    Allow from all
</IfModule>

# Página de erro personalizada
ErrorDocument 403 /403.php

# Redirecionar para login.php se for o diretório raiz do /admin
RewriteCond %{REQUEST_URI} ^/admin/?$ [NC]
RewriteRule ^$ login.php [L,R=302]

# Se o arquivo ou diretório não existir, permitir reescrita
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Redirecionar todas as outras requisições para index.php (se necessário)
RewriteRule ^ index.php [L]
