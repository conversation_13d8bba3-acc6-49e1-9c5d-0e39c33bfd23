<?php
/**
 * Payment Gateway Webhook Handler
 * 
 * This script handles incoming webhook notifications from the payment processor
 * and updates the payment status in the database.
 */

// Set error reporting for debugging (remove in production)
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

// Log file for debugging
$logFile = __DIR__ . '/webhook_logs.txt';

// Security: Webhook token for validation
$validToken = 'YOUR_WEBHOOK_SECRET_TOKEN'; // Replace with your actual token

// Function to log messages
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Function to send response
function sendResponse($success, $message, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message
    ]);
    exit;
}

// Get the request method
$requestMethod = $_SERVER['REQUEST_METHOD'];

// Only accept POST requests
if ($requestMethod !== 'POST') {
    logMessage("Invalid request method: $requestMethod");
    sendResponse(false, 'Invalid request method', 405);
}

// Get the authorization header for token validation
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $authHeader);

// Validate the token
if (empty($token) || $token !== $validToken) {
    logMessage("Invalid webhook token: $token");
    sendResponse(false, 'Token de webhook inválido', 401);
}

// Get the raw POST data
$rawData = file_get_contents('php://input');
logMessage("Received webhook data: $rawData");

// Decode the JSON payload
$data = json_decode($rawData, true);

// Check if JSON is valid
if (json_last_error() !== JSON_ERROR_NONE) {
    logMessage("Invalid JSON: " . json_last_error_msg());
    sendResponse(false, 'Invalid JSON payload', 400);
}

// Check if required fields exist
if (!isset($data['event']) || !isset($data['payment'])) {
    logMessage("Missing required fields in webhook data");
    sendResponse(false, 'Missing required fields', 400);
}

// Extract event type and payment data
$event = $data['event'];
$payment = $data['payment'];
$paymentId = $payment['id'] ?? '';
$status = $payment['status'] ?? '';
$value = $payment['value'] ?? 0;
$description = $payment['description'] ?? '';
$externalReference = $payment['externalReference'] ?? '';

// Connect to database
try {
    $db = new PDO('mysql:host=localhost;dbname=your_database', 'username', 'password');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    logMessage("Database connection error: " . $e->getMessage());
    sendResponse(false, 'Database connection error', 500);
}

// Process different event types
switch ($event) {
    case 'PAYMENT_CREATED':
        logMessage("Payment created: $paymentId, Status: $status, Value: $value");
        
        // Update payment status in database
        try {
            $stmt = $db->prepare("
                UPDATE payments 
                SET payment_gateway_id = :payment_id, 
                    status = :status, 
                    updated_at = NOW() 
                WHERE external_reference = :external_reference
            ");
            
            $stmt->execute([
                ':payment_id' => $paymentId,
                ':status' => 'pending',
                ':external_reference' => $externalReference
            ]);
            
            if ($stmt->rowCount() === 0) {
                logMessage("No payment found with external reference: $externalReference");
            }
        } catch (PDOException $e) {
            logMessage("Database error: " . $e->getMessage());
            sendResponse(false, 'Database error', 500);
        }
        break;
        
    case 'PAYMENT_RECEIVED':
    case 'PAYMENT_CONFIRMED':
    case 'PAYMENT_APPROVED':
        logMessage("Payment approved: $paymentId, Status: $status, Value: $value");
        
        try {
            // Update payment status
            $stmt = $db->prepare("
                UPDATE payments 
                SET status = :status, 
                    paid_at = NOW(),
                    updated_at = NOW() 
                WHERE payment_gateway_id = :payment_id OR external_reference = :external_reference
            ");
            
            $stmt->execute([
                ':status' => 'approved',
                ':payment_id' => $paymentId,
                ':external_reference' => $externalReference
            ]);
            
            if ($stmt->rowCount() > 0) {
                // Get customer information
                $stmt = $db->prepare("
                    SELECT p.id, p.product_id, c.name, c.email, c.whatsapp
                    FROM payments p
                    JOIN customers c ON p.customer_id = c.id
                    WHERE p.payment_gateway_id = :payment_id OR p.external_reference = :external_reference
                ");
                
                $stmt->execute([
                    ':payment_id' => $paymentId,
                    ':external_reference' => $externalReference
                ]);
                
                $paymentInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($paymentInfo) {
                    // Send confirmation email
                    $customerEmail = $paymentInfo['email'];
                    $customerName = $paymentInfo['name'];
                    
                    // Here you would implement your email sending logic
                    // sendConfirmationEmail($customerEmail, $customerName, $paymentId);
                    
                    logMessage("Confirmation email sent to: $customerEmail");
                    
                    // Grant access to the product
                    $productId = $paymentInfo['product_id'];
                    $paymentId = $paymentInfo['id'];
                    
                    // Here you would implement your access granting logic
                    // grantProductAccess($customerEmail, $productId, $paymentId);
                    
                    logMessage("Access granted for product $productId to customer $customerEmail");
                }
            } else {
                logMessage("No payment found with ID: $paymentId or reference: $externalReference");
            }
        } catch (PDOException $e) {
            logMessage("Database error: " . $e->getMessage());
            sendResponse(false, 'Database error', 500);
        }
        break;
        
    case 'PAYMENT_CANCELED':
    case 'PAYMENT_DECLINED':
    case 'PAYMENT_FAILED':
        logMessage("Payment failed: $paymentId, Status: $status");
        
        try {
            $stmt = $db->prepare("
                UPDATE payments 
                SET status = :status, 
                    updated_at = NOW() 
                WHERE payment_gateway_id = :payment_id OR external_reference = :external_reference
            ");
            
            $stmt->execute([
                ':status' => 'failed',
                ':payment_id' => $paymentId,
                ':external_reference' => $externalReference
            ]);
        } catch (PDOException $e) {
            logMessage("Database error: " . $e->getMessage());
            sendResponse(false, 'Database error', 500);
        }
        break;
        
    case 'PAYMENT_REFUNDED':
        logMessage("Payment refunded: $paymentId, Status: $status");
        
        try {
            $stmt = $db->prepare("
                UPDATE payments 
                SET status = :status, 
                    updated_at = NOW() 
                WHERE payment_gateway_id = :payment_id OR external_reference = :external_reference
            ");
            
            $stmt->execute([
                ':status' => 'refunded',
                ':payment_id' => $paymentId,
                ':external_reference' => $externalReference
            ]);
        } catch (PDOException $e) {
            logMessage("Database error: " . $e->getMessage());
            sendResponse(false, 'Database error', 500);
        }
        break;
        
    default:
        logMessage("Unhandled event type: $event");
        // We still return success for unhandled events to prevent retries
        sendResponse(true, "Unhandled event: $event");
}

// If we got here, everything went well
sendResponse(true, "Webhook processed successfully");