<?php
echo "<h2>Verificando Logs de Erro</h2>";

// Possíveis localizações do log de erro
$logPaths = [
    'C:\xampp\apache\logs\error.log',
    'C:\xampp\php\logs\php_error_log',
    ini_get('error_log'),
    'error.log',
    '../error.log',
    'logs/error.log'
];

echo "<h3>Configurações PHP:</h3>";
echo "<p>Log errors: " . (ini_get('log_errors') ? 'Habilitado' : 'Desabilitado') . "</p>";
echo "<p>Error log path: " . ini_get('error_log') . "</p>";
echo "<p>Display errors: " . (ini_get('display_errors') ? 'Habilitado' : 'Desabilitado') . "</p>";

echo "<h3>Procurando arquivos de log:</h3>";

foreach ($logPaths as $path) {
    if (file_exists($path)) {
        echo "<h4>Log encontrado: $path</h4>";
        
        $content = file_get_contents($path);
        $lines = explode("\n", $content);
        $recentLines = array_slice($lines, -50); // Últimas 50 linhas
        
        echo "<div style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
        foreach ($recentLines as $line) {
            if (stripos($line, 'chat') !== false || stripos($line, 'send_message') !== false) {
                echo "<div style='background: yellow;'>" . htmlspecialchars($line) . "</div>";
            } else {
                echo "<div>" . htmlspecialchars($line) . "</div>";
            }
        }
        echo "</div>";
        
        break;
    } else {
        echo "<p>Não encontrado: $path</p>";
    }
}

// Criar um erro de teste para verificar se os logs estão funcionando
echo "<h3>Teste de Log:</h3>";
error_log("TESTE - Chat system debug - " . date('Y-m-d H:i:s'));
echo "<p>Log de teste criado. Verifique os arquivos acima.</p>";

// Verificar se há arquivos de log no diretório atual
echo "<h3>Arquivos de log no diretório atual:</h3>";
$files = glob('*.log');
foreach ($files as $file) {
    echo "<p>Encontrado: $file</p>";
}

// Informações do sistema
echo "<h3>Informações do Sistema:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Current Directory: " . getcwd() . "</p>";

// Testar se conseguimos escrever logs
echo "<h3>Teste de Escrita de Log:</h3>";
$testLogFile = 'test_error.log';
$testMessage = "Teste de escrita - " . date('Y-m-d H:i:s') . "\n";

if (file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "<p style='color: green;'>✓ Conseguimos escrever no arquivo: $testLogFile</p>";
    
    if (file_exists($testLogFile)) {
        echo "<h4>Conteúdo do arquivo de teste:</h4>";
        echo "<pre>" . htmlspecialchars(file_get_contents($testLogFile)) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>✗ Não conseguimos escrever no arquivo de log</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
</style>
