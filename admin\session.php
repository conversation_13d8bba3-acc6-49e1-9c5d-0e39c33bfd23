<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Atualiza o último acesso
if (isset($_SESSION['admin_id'])) {
    require_once __DIR__ . '/database/connection.php';
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        $stmt = $pdo->prepare("UPDATE admins SET updated_at = NOW() WHERE id = ?");
        $stmt->execute([$_SESSION['admin_id']]);
    } catch (PDOException $e) {
        // Silently fail - não queremos interromper a sessão por erro de atualização
    }
}

// Define constantes úteis
define('IS_ADMIN', true);
define('ADMIN_ID', $_SESSION['admin_id'] ?? null);
define('ADMIN_NAME', $_SESSION['admin_name'] ?? '');
define('ADMIN_EMAIL', $_SESSION['admin_email'] ?? '');
?>
