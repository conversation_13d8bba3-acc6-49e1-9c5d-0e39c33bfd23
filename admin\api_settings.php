<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) NOT NULL UNIQUE,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);

        // Inserir configuração padrão
        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute(['tinymce_api_key', 'no-api-key']);
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

// Processar o formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $apiKey = trim($_POST['tinymce_api_key'] ?? '');

        // Verificar se já existe a configuração
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_settings WHERE setting_key = 'tinymce_api_key'");
        $stmt->execute();
        $exists = $stmt->fetchColumn();

        if ($exists) {
            $stmt = $pdo->prepare("
                UPDATE system_settings
                SET setting_value = ?
                WHERE setting_key = 'tinymce_api_key'
            ");
        } else {
            $stmt = $pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value)
                VALUES ('tinymce_api_key', ?)
            ");
        }
        $stmt->execute([$apiKey]);

        $_SESSION['success_message'] = 'API key atualizada com sucesso!';
        header('Location: api_settings.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Erro ao atualizar API key: ' . $e->getMessage();
    }
}

// Buscar API key atual
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'tinymce_api_key'");
    $stmt->execute();
    $apiKey = $stmt->fetchColumn() ?: 'no-api-key';
} catch (Exception $e) {
    $apiKey = 'no-api-key';
}

// Agora podemos incluir o header que pode ter output
require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Configurações de API</h1>
    
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php 
            echo $_SESSION['success_message'];
            unset($_SESSION['success_message']);
            ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger">
            <?php 
            echo $_SESSION['error_message'];
            unset($_SESSION['error_message']);
            ?>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-key me-1"></i>
            Chaves de API
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="tinymce_api_key" class="form-label">TinyMCE API Key</label>
                    <input type="text" class="form-control" id="tinymce_api_key" name="tinymce_api_key" 
                           value="<?php echo htmlspecialchars($apiKey); ?>" required>
                    <div class="form-text">
                        Para obter uma API key gratuita, visite 
                        <a href="https://www.tiny.cloud/auth/signup/" target="_blank">tiny.cloud/auth/signup</a>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Salvar</button>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
