<?php
require_once __DIR__ . '/includes/auth_check.php';

// Get database connection
$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se o usuário está logado
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// Função para garantir que o admin existe na tabela chat_users
function ensureAdminInChatUsers($pdo, $adminId) {
    try {
        // Verificar se o admin já existe na tabela chat_users
        $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE id = ? AND is_admin = 1");
        $stmt->execute([$adminId]);
        
        if (!$stmt->fetch()) {
            // Se não existe, buscar informações do admin
            $stmt = $pdo->prepare("SELECT name, email FROM admins WHERE id = ?");
            $stmt->execute([$adminId]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin) {
                // Inserir o admin na tabela chat_users
                $stmt = $pdo->prepare("
                    INSERT INTO chat_users (id, name, email, is_admin, created_at, last_activity) 
                    VALUES (?, ?, ?, 1, NOW(), NOW())
                    ON DUPLICATE KEY UPDATE name = VALUES(name), email = VALUES(email)
                ");
                $stmt->execute([$adminId, $admin['name'], $admin['email']]);
            }
        }
        return true;
    } catch (PDOException $e) {
        error_log("Erro ao sincronizar admin com chat_users: " . $e->getMessage());
        return false;
    }
}

// Handle AJAX requests
if (isset($_POST['action']) || isset($_GET['action'])) {
    header('Content-Type: application/json');
    $action = $_POST['action'] ?? $_GET['action'];
    $response = [];

    switch ($action) {
        case 'get_conversations':
            try {
                $stmt = $pdo->prepare("
                    SELECT 
                        u.id,
                        u.name,
                        u.email,
                        u.whatsapp,
                        u.last_activity,
                        (
                            SELECT message 
                            FROM chat_messages 
                            WHERE user_id = u.id 
                            ORDER BY timestamp DESC 
                            LIMIT 1
                        ) as last_message,
                        (
                            SELECT COUNT(*)
                            FROM chat_messages
                            WHERE user_id = u.id
                            AND is_admin = 0
                        ) as unread_count
                    FROM chat_users u
                    WHERE u.is_admin = 0
                    ORDER BY u.last_activity DESC
                ");
                $stmt->execute();
                $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Garantir que sempre retorna um array
                echo json_encode($conversations);
                exit;
            } catch (PDOException $e) {
                echo json_encode(['error' => $e->getMessage()]);
                http_response_code(500);
                exit;
            }

        case 'get_messages':
            $userId = $_GET['user_id'] ?? 0;
            try {
                // Não há campo is_read na tabela, então vamos pular esta parte

                // Get messages
                $stmt = $pdo->prepare("
                    SELECT
                        m.*,
                        CASE
                            WHEN m.is_admin = 1 THEN 'Admin'
                            ELSE user.name
                        END as sender_name
                    FROM chat_messages m
                    LEFT JOIN chat_users user ON m.user_id = user.id
                    WHERE m.user_id = ?
                    ORDER BY m.timestamp ASC
                ");
                $stmt->execute([$userId]);
                $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode($messages);
                exit;
            } catch (PDOException $e) {
                echo json_encode(['error' => $e->getMessage()]);
                http_response_code(500);
                exit;
            }

        case 'send_message':
            $userId = $_POST['user_id'] ?? 0;
            $message = trim($_POST['message'] ?? '');
            $adminId = $_SESSION['admin_id'] ?? null;

            // Log de debug
            error_log("Admin Chat - send_message: userId=$userId, message='$message', adminId=$adminId");
            error_log("Admin Chat - POST data: " . print_r($_POST, true));
            error_log("Admin Chat - SESSION data: " . print_r($_SESSION, true));

            if (empty($userId) || $userId == 0 || empty($message) || empty($adminId)) {
                $errorMsg = "Parâmetros inválidos - userId: $userId, message: '$message', adminId: $adminId";
                error_log("Admin Chat - Error: $errorMsg");
                $response = ['error' => $errorMsg];
                http_response_code(400);
                break;
            }

            try {
                // Garantir que o admin existe na tabela chat_users
                if (!ensureAdminInChatUsers($pdo, $adminId)) {
                    throw new PDOException("Erro ao sincronizar admin com chat_users");
                }

                // Atualizar last_activity do usuário
                $updateStmt = $pdo->prepare("
                    UPDATE chat_users 
                    SET last_activity = NOW() 
                    WHERE id = ?
                ");
                $updateStmt->execute([$userId]);

                // Inserir mensagem
                $stmt = $pdo->prepare("
                    INSERT INTO chat_messages (
                        user_id,
                        message,
                        is_admin,
                        timestamp
                    ) VALUES (?, ?, 1, UNIX_TIMESTAMP())
                ");
                $stmt->execute([$userId, $message]);
                echo json_encode(['success' => true]);
                exit;
            } catch (PDOException $e) {
                echo json_encode(['error' => $e->getMessage()]);
                http_response_code(500);
                exit;
            }

        default:
            echo json_encode(['error' => 'Ação inválida']);
            http_response_code(400);
            exit;
    }
}

$page_title = "Central de Atendimento";
include './includes/header.php';
?>

<style>
.conversation-item {
    transition: all 0.2s ease;
    cursor: pointer;
}

.conversation-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.conversation-item.active {
    transform: translateX(0);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.conversations-list {
    background: #f8f9fa;
}

.chat-messages {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
}

.message-bubble {
    max-width: 70%;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-comments"></i> Conversas Ativas</h5>
                </div>
                <div class="card-body conversations-list p-0" style="height: 600px; overflow-y: auto;">
                    <div class="p-3 text-center text-muted">
                        <i class="fas fa-spinner fa-spin mb-2"></i><br>
                        Carregando conversas...
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="current-chat-title mb-0">Selecione uma conversa</h5>
                </div>
                <div class="card-body chat-messages" style="height: 500px; overflow-y: auto;">
                    <!-- As mensagens serão carregadas aqui via AJAX -->
                </div>
                <div class="card-footer">
                    <form id="chat-form" class="d-none">
                        <div class="input-group">
                            <input type="text" class="form-control" id="message-input" placeholder="Digite sua mensagem...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i> Enviar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentUserId = null;
console.log('Admin Chat - Sistema inicializado, currentUserId:', currentUserId);
const conversationsList = document.querySelector('.conversations-list');
const chatMessages = document.querySelector('.chat-messages');
const chatForm = document.getElementById('chat-form');
const messageInput = document.getElementById('message-input');
const currentChatTitle = document.querySelector('.current-chat-title');

// Função para formatar data
function formatLastActivity(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 1) return 'Agora mesmo';
    if (minutes < 60) return `${minutes} min atrás`;
    if (hours < 24) return `${hours}h atrás`;
    if (days === 1) return 'Ontem';
    return `${days} dias atrás`;
}

// Função para carregar conversas
function loadConversations() {
    fetch('chat.php?action=get_conversations')
        .then(response => response.json())
        .then(data => {
            console.log('Admin Chat - Dados recebidos:', data);

            if (!Array.isArray(data)) {
                console.error('Erro ao carregar conversas:', data);

                // Mostrar mensagem de erro na interface
                conversationsList.innerHTML = `
                    <div class="p-3 text-center text-muted">
                        <i class="fas fa-exclamation-triangle mb-2"></i><br>
                        Erro ao carregar conversas<br>
                        <small>${data.error || 'Erro desconhecido'}</small>
                    </div>
                `;
                return;
            }

            if (data.length === 0) {
                conversationsList.innerHTML = `
                    <div class="p-3 text-center text-muted">
                        <i class="fas fa-comments mb-3" style="font-size: 2rem;"></i><br>
                        <strong>Nenhuma conversa encontrada</strong><br>
                        <small class="text-muted">Aguardando mensagens dos clientes</small><br><br>
                        <button class="btn btn-sm btn-primary" onclick="createTestUsers()">
                            <i class="fas fa-plus"></i> Criar Usuários de Teste
                        </button>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = data.map(user => `
                <div class="conversation-item p-3 border-bottom cursor-pointer ${user.unread_count > 0 ? 'bg-light border-start border-primary border-3' : ''} ${currentUserId == user.id ? 'active bg-primary text-white' : ''}"
                     data-id="${user.id}"
                     data-name="${user.name}"
                     style="transition: all 0.2s; cursor: pointer;"
                     onmouseover="this.style.backgroundColor = currentUserId == ${user.id} ? '' : '#f8f9fa'"
                     onmouseout="this.style.backgroundColor = currentUserId == ${user.id} ? '' : '${user.unread_count > 0 ? '#f8f9fa' : 'white'}'">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <i class="fas fa-user-circle fa-2x text-${currentUserId == user.id ? 'white-50' : 'primary'}"></i>
                            </div>
                            <div>
                                <strong class="d-block">${user.name}</strong>
                                <small class="text-${currentUserId == user.id ? 'white-50' : 'muted'}">
                                    <i class="fas fa-clock me-1"></i>${formatLastActivity(user.last_activity)}
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            ${user.unread_count > 0 ? `<span class="badge bg-danger rounded-pill">${user.unread_count}</span>` : ''}
                        </div>
                    </div>
                    <div class="small text-${currentUserId == user.id ? 'white-50' : 'muted'} mb-1">
                        <i class="fas fa-envelope me-1"></i> ${user.email}
                    </div>
                    <div class="small text-${currentUserId == user.id ? 'white-50' : 'muted'} mb-1">
                        <i class="fab fa-whatsapp me-1 text-success"></i> ${user.whatsapp || 'Não informado'}
                    </div>
                    <div class="small text-${currentUserId == user.id ? 'white-50' : 'muted'} text-truncate">
                        <i class="fas fa-comment me-1"></i> ${user.last_message || 'Nenhuma mensagem ainda'}
                    </div>
                </div>
            `).join('');

            // Adicionar evento de clique para cada conversa
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.addEventListener('click', () => {
                    currentUserId = parseInt(item.dataset.id);
                    console.log('Admin Chat - Conversa selecionada:', {
                        userId: currentUserId,
                        userName: item.dataset.name,
                        userIdType: typeof currentUserId,
                        isValid: currentUserId > 0
                    });

                    currentChatTitle.textContent = `Chat com ${item.dataset.name}`;
                    chatForm.classList.remove('d-none');
                    loadMessages(currentUserId);
                    
                    // Atualizar seleção visual
                    document.querySelectorAll('.conversation-item').forEach(i => {
                        i.classList.remove('active', 'bg-primary', 'text-white');
                        i.querySelectorAll('.text-white-50').forEach(el => {
                            el.classList.remove('text-white-50');
                            el.classList.add('text-muted');
                        });
                    });
                    item.classList.add('active', 'bg-primary', 'text-white');
                    item.querySelectorAll('.text-muted').forEach(el => {
                        el.classList.remove('text-muted');
                        el.classList.add('text-white-50');
                    });
                });
            });
        })
        .catch(error => console.error('Erro ao carregar conversas:', error));
}

// Função para carregar mensagens
function loadMessages(userId) {
    fetch(`chat.php?action=get_messages&user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (!Array.isArray(data)) {
                console.error('Erro ao carregar mensagens:', data);
                return;
            }

            chatMessages.innerHTML = data.map(message => `
                <div class="message mb-3 ${message.is_admin ? 'text-end' : ''}">
                    <div class="message-content d-inline-block p-3 rounded-3 ${message.is_admin ? 'bg-primary text-white' : 'bg-light'}">
                        ${message.message}
                        <div class="small text-${message.is_admin ? 'white-50' : 'muted'} mt-1">
                            ${message.sender_name} - ${new Date(message.timestamp * 1000).toLocaleTimeString()}
                        </div>
                    </div>
                </div>
            `).join('');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        })
        .catch(error => console.error('Erro ao carregar mensagens:', error));
}

// Enviar mensagem
chatForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const message = messageInput.value.trim();

    console.log('Admin Chat - Tentando enviar mensagem:', {
        message: message,
        currentUserId: currentUserId,
        messageLength: message.length,
        userIdType: typeof currentUserId
    });

    if (!message || !currentUserId || currentUserId <= 0 || isNaN(currentUserId)) {
        console.error('Admin Chat - Parâmetros inválidos:', {
            message: message,
            currentUserId: currentUserId,
            messageEmpty: !message,
            userIdEmpty: !currentUserId,
            userIdZero: currentUserId <= 0,
            userIdNaN: isNaN(currentUserId),
            userIdType: typeof currentUserId
        });
        alert('Selecione uma conversa válida antes de enviar a mensagem');
        return;
    }

    // Desabilitar o formulário durante o envio
    const submitButton = chatForm.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    messageInput.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';

    const formData = new FormData();
    formData.append('action', 'send_message');
    formData.append('user_id', currentUserId);
    formData.append('message', message);

    console.log('Admin Chat - Dados sendo enviados:', {
        action: 'send_message',
        user_id: currentUserId,
        message: message
    });

    fetch('chat.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Admin Chat - Resposta recebida:', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok
        });

        if (!response.ok) {
            return response.text().then(text => {
                console.error('Admin Chat - Erro HTTP:', text);
                throw new Error(`HTTP error! status: ${response.status} - ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Admin Chat - Dados da resposta:', data);

        if (data.success) {
            messageInput.value = '';
            loadMessages(currentUserId);
            console.log('Admin Chat - Mensagem enviada com sucesso');
        } else {
            console.error('Admin Chat - Erro do servidor:', data.error);
            alert('Erro ao enviar mensagem: ' + (data.error || 'Erro desconhecido'));
        }
    })
    .catch(error => {
        console.error('Admin Chat - Erro na requisição:', error);
        alert('Erro ao enviar mensagem: ' + error.message);
    })
    .finally(() => {
        // Reabilitar o formulário
        submitButton.disabled = false;
        messageInput.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> Enviar';
        messageInput.focus();
    });
});

// Função para criar usuários de teste
function createTestUsers() {
    if (!confirm('Deseja criar usuários de teste para demonstração?')) {
        return;
    }

    const testUsers = [
        {name: 'João Silva', email: '<EMAIL>', whatsapp: '11987654321'},
        {name: 'Maria Santos', email: '<EMAIL>', whatsapp: '11976543210'},
        {name: 'Pedro Costa', email: '<EMAIL>', whatsapp: '11965432109'},
        {name: 'Ana Oliveira', email: '<EMAIL>', whatsapp: '11954321098'}
    ];

    conversationsList.innerHTML = `
        <div class="p-3 text-center text-muted">
            <i class="fas fa-spinner fa-spin mb-2"></i><br>
            Criando usuários de teste...
        </div>
    `;

    Promise.all(testUsers.map(user => {
        const formData = new FormData();
        formData.append('name', user.name);
        formData.append('email', user.email);
        formData.append('whatsapp', user.whatsapp);

        return fetch('../api/create_user.php', {
            method: 'POST',
            body: formData
        });
    }))
    .then(() => {
        // Aguardar um pouco e recarregar conversas
        setTimeout(() => {
            loadConversations();
        }, 1000);
    })
    .catch(error => {
        console.error('Erro ao criar usuários de teste:', error);
        alert('Erro ao criar usuários de teste');
        loadConversations();
    });
}

// Carregar conversas inicialmente e atualizar periodicamente
loadConversations();
setInterval(loadConversations, 5000);

// Se houver uma conversa ativa, atualizar mensagens periodicamente
setInterval(() => {
    if (currentUserId) {
        loadMessages(currentUserId);
    }
}, 3000);
</script>

<?php include './includes/footer.php'; ?>
