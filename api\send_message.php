<?php
require_once '../database/connection.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Log de debug
error_log("send_message.php: Iniciando processamento");
error_log("send_message.php: Method: " . $_SERVER['REQUEST_METHOD']);
error_log("send_message.php: POST data: " . print_r($_POST, true));

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método inválido');
    }

    $database = Database::getInstance();
    $pdo = $database->getConnection();

    $userId = $_POST['user_id'] ?? null;
    $message = trim($_POST['message'] ?? '');

    error_log("send_message.php: userId: $userId, message: $message");

    if (!$userId || empty($message)) {
        throw new Exception('ID do usuário e mensagem são obrigatórios');
    }

    // Verificar se o usuário existe
    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if (!$user) {
        error_log("send_message.php: Usuário não encontrado: $userId");
        throw new Exception('Usuário não encontrado');
    }

    error_log("send_message.php: Usuário encontrado: " . $user['username']);

    // Inserir mensagem
    $stmt = $pdo->prepare("INSERT INTO chat_messages (user_id, message, is_admin, created_at) VALUES (?, ?, 0, NOW())");
    $result = $stmt->execute([$userId, $message]);

    if (!$result) {
        error_log("send_message.php: Erro ao inserir mensagem");
        throw new Exception('Erro ao inserir mensagem no banco de dados');
    }

    $messageId = $pdo->lastInsertId();
    error_log("send_message.php: Mensagem inserida com ID: $messageId");

    // Atualizar última atividade do usuário
    $stmt = $pdo->prepare("UPDATE users SET last_activity = NOW(), is_online = 1 WHERE id = ?");
    $stmt->execute([$userId]);

    error_log("send_message.php: Mensagem enviada com sucesso");

    echo json_encode([
        'success' => true,
        'message' => 'Mensagem enviada com sucesso',
        'message_id' => $messageId
    ]);

} catch (Exception $e) {
    error_log("send_message.php: Erro: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}
