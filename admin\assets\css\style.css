/* Estilos Globais */
.nav-sidebar,
.nav-sidebar * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.nav-sidebar .nav-link {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    color: #333;
    padding: 0.75rem 1rem;
    border-left: 4px solid transparent;
}

.nav-sidebar .nav-link:hover {
    background-color: rgba(0, 128, 0, 0.1);
}

.nav-sidebar .nav-link.active {
    background-color: #28a745;
    color: white;
    border-left-color: #1e7e34;
}

/* Ícones do Menu */
.nav-sidebar .nav-icon {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Ajuste para submenus */
.nav-sidebar .nav-treeview {
    margin-left: 1rem;
}

/* Efeito de hover suave */
.nav-sidebar .nav-item:hover > .nav-link:not(.active) {
    background-color: rgba(0, 128, 0, 0.1);
}

/* Remover outline ao clicar */
.nav-sidebar .nav-link:focus {
    outline: none;
    box-shadow: none;
}

/* Garantir que apenas um item fique ativo */
.nav-sidebar .nav-link:not(.active) {
    background-color: transparent;
    color: #333;
}

/* Modal/Popup Styles */
.modal {
    background: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
    margin: 0;
    width: 100%;
    max-width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #ff3b30;
    border: none;
    border-radius: 20px;
    width: 95%;
    max-width: 400px;
    position: relative;
    margin: auto;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

@media (min-width: 768px) {
    .modal-content {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .modal-content {
        max-width: 800px;
    }
}

@media (min-width: 1200px) {
    .modal-content {
        max-width: 1000px;
    }
}

.modal-header {
    border: none;
    padding: 0;
    position: relative;
}

.modal-body {
    padding: 20px;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

.btn-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    opacity: 1;
    z-index: 1050;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.btn-close:hover {
    opacity: 0.8;
}

/* Botões do Modal */
.modal-footer {
    border: none;
    justify-content: center;
    padding: 20px;
}

.modal-footer .btn {
    padding: 8px 25px;
    border-radius: 5px;
    font-weight: 500;
    margin: 0 10px;
    min-width: 100px;
}

.btn-accept {
    background: #28a745;
    color: white;
    border: none;
}

.btn-reject {
    background: #dc3545;
    color: white;
    border: none;
}
