<?php
function show_notification($type, $message) {
    $icons = [
        'success' => 'check-circle',
        'error' => 'times-circle',
        'warning' => 'exclamation-circle',
        'info' => 'info-circle',
        'question' => 'question-circle'
    ];

    $colors = [
        'success' => '#4CAF50',
        'error' => '#f44336',
        'warning' => '#ff9800',
        'info' => '#2196F3',
        'question' => '#9C27B0'
    ];

    if (isset($icons[$type])) {
        echo "<div class='alert alert-{$type} alert-dismissible fade show shadow-sm' role='alert' 
                   style='border-left: 4px solid {$colors[$type]}; background-color: #fff;'>
                <div class='d-flex align-items-center'>
                    <i class='fas fa-{$icons[$type]} me-2' style='color: {$colors[$type]};'></i>
                    <div class='notification-message'>{$message}</div>
                </div>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
              </div>";
    }
}

// Função para exibir notificações da sessão
function show_session_messages() {
    if (isset($_SESSION['success'])) {
        show_notification('success', $_SESSION['success']);
        unset($_SESSION['success']);
    }
    if (isset($_SESSION['error'])) {
        show_notification('error', $_SESSION['error']);
        unset($_SESSION['error']);
    }
    if (isset($_SESSION['warning'])) {
        show_notification('warning', $_SESSION['warning']);
        unset($_SESSION['warning']);
    }
    if (isset($_SESSION['info'])) {
        show_notification('info', $_SESSION['info']);
        unset($_SESSION['info']);
    }
}
?>
