<?php
/**
 * Submissão Automática de Sitemap - KESUNG SITE
 * Submete o sitemap para Google, Bing e outros motores de busca
 */

// Configurações
$siteUrl = 'https://kesungsite.com';
$sitemapUrl = $siteUrl . '/sitemap.xml';

// Motores de busca para submissão
$searchEngines = [
    'Google' => 'https://www.google.com/ping?sitemap=' . urlencode($sitemapUrl),
    'Bing' => 'https://www.bing.com/ping?sitemap=' . urlencode($sitemapUrl),
    'Yandex' => 'https://webmaster.yandex.com/ping?sitemap=' . urlencode($sitemapUrl),
    'Baidu' => 'https://ping.baidu.com/ping/RPC2'
];

/**
 * Função para fazer requisição HTTP
 */
function makeRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'KESUNG SITE Sitemap Submitter');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * Submeter sitemap para motores de busca
 */
function submitSitemap() {
    global $searchEngines, $sitemapUrl;
    
    $results = [];
    
    foreach ($searchEngines as $engine => $url) {
        echo "Submetendo para {$engine}...\n";
        
        $result = makeRequest($url);
        $results[$engine] = $result;
        
        if ($result['success']) {
            echo "✅ {$engine}: Sucesso (HTTP {$result['http_code']})\n";
        } else {
            echo "❌ {$engine}: Erro (HTTP {$result['http_code']}) - {$result['error']}\n";
        }
        
        // Aguardar entre requisições
        sleep(2);
    }
    
    return $results;
}

/**
 * Verificar se sitemap existe e é válido
 */
function validateSitemap() {
    global $sitemapUrl;
    
    echo "Verificando sitemap: {$sitemapUrl}\n";
    
    $result = makeRequest($sitemapUrl);
    
    if (!$result['success']) {
        echo "❌ Erro ao acessar sitemap: HTTP {$result['http_code']}\n";
        return false;
    }
    
    // Verificar se é XML válido
    $xml = simplexml_load_string($result['response']);
    if ($xml === false) {
        echo "❌ Sitemap não é um XML válido\n";
        return false;
    }
    
    // Contar URLs
    $urlCount = count($xml->url);
    echo "✅ Sitemap válido com {$urlCount} URLs\n";
    
    return true;
}

/**
 * Gerar relatório de submissão
 */
function generateReport($results) {
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'sitemap_url' => $GLOBALS['sitemapUrl'],
        'results' => $results,
        'summary' => [
            'total' => count($results),
            'success' => 0,
            'failed' => 0
        ]
    ];
    
    foreach ($results as $engine => $result) {
        if ($result['success']) {
            $report['summary']['success']++;
        } else {
            $report['summary']['failed']++;
        }
    }
    
    // Salvar relatório
    $reportFile = 'logs/sitemap_submission_' . date('Y-m-d') . '.json';
    
    // Criar diretório se não existir
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT));
    
    echo "\n📊 Relatório salvo em: {$reportFile}\n";
    echo "✅ Sucessos: {$report['summary']['success']}\n";
    echo "❌ Falhas: {$report['summary']['failed']}\n";
    
    return $report;
}

/**
 * Notificar Google Search Console via API (se configurado)
 */
function notifyGoogleSearchConsole() {
    // Esta função requer configuração da API do Google Search Console
    // Por enquanto, apenas log
    echo "💡 Para notificação automática do Google Search Console, configure a API\n";
    echo "   Acesse: https://developers.google.com/webmaster-tools/search-console-api\n";
}

/**
 * Função principal
 */
function main() {
    echo "🚀 KESUNG SITE - Submissão de Sitemap\n";
    echo "=====================================\n\n";
    
    // Verificar sitemap
    if (!validateSitemap()) {
        echo "❌ Abortando submissão devido a erro no sitemap\n";
        return;
    }
    
    echo "\n";
    
    // Submeter para motores de busca
    $results = submitSitemap();
    
    echo "\n";
    
    // Gerar relatório
    $report = generateReport($results);
    
    echo "\n";
    
    // Notificar Google Search Console
    notifyGoogleSearchConsole();
    
    echo "\n🎉 Submissão concluída!\n";
}

// Executar se chamado diretamente
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Executado via linha de comando
    main();
} else {
    // Executado via web (apenas para admin)
    session_start();
    
    // Verificar se é admin (adapte conforme seu sistema de autenticação)
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        http_response_code(403);
        die('Acesso negado');
    }
    
    // Executar e mostrar resultado em HTML
    ob_start();
    main();
    $output = ob_get_clean();
    
    echo '<pre>' . htmlspecialchars($output) . '</pre>';
}

/**
 * Função para agendar submissão automática (cron job)
 * Adicione ao crontab:
 * 0 6 * * * /usr/bin/php /path/to/submit_sitemap.php
 */
function scheduleAutoSubmission() {
    // Esta função pode ser chamada por um cron job diário
    // para submeter automaticamente o sitemap
    
    $lastSubmission = @file_get_contents('logs/last_sitemap_submission.txt');
    $today = date('Y-m-d');
    
    if ($lastSubmission !== $today) {
        main();
        file_put_contents('logs/last_sitemap_submission.txt', $today);
    }
}
?>
