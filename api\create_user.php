<?php
session_start();
require_once '../database/connection.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método inválido');
    }

    $database = Database::getInstance();
    $pdo = $database->getConnection();

    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $whatsapp = $_POST['whatsapp'] ?? '';

    if (empty($name) || empty($email)) {
        throw new Exception('Nome e email são obrigatórios');
    }

    // Ensure chat_users table exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            whatsapp VARCHAR(20),
            is_admin TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_is_admin (is_admin),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");

    // Ensure chat_messages table exists with correct foreign key
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL,
            is_admin TINYINT(1) DEFAULT 0,
            timestamp INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_timestamp (timestamp),
            INDEX idx_is_admin (is_admin),
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");

    // Check if user already exists in chat_users
    $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE email = ?");
    $stmt->execute([$email]);
    $existingUser = $stmt->fetch();

    if ($existingUser) {
        $userId = $existingUser['id'];

        // Update user info if exists
        $stmt = $pdo->prepare("UPDATE chat_users SET name = ?, whatsapp = ?, last_activity = NOW() WHERE id = ?");
        $stmt->execute([$name, $whatsapp, $userId]);
    } else {
        // Create new user in chat_users
        $stmt = $pdo->prepare("INSERT INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) VALUES (?, ?, ?, 0, NOW(), NOW())");
        $stmt->execute([$name, $email, $whatsapp]);

        $userId = $pdo->lastInsertId();
    }

    // Store user ID in PHP session
    $_SESSION['user_id'] = $userId;

    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'message' => 'Usuário criado/atualizado com sucesso'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
