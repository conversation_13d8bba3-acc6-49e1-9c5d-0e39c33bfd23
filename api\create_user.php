<?php
session_start();
require_once '../database/connection.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método inválido');
    }

    $database = Database::getInstance();
    $pdo = $database->getConnection();

    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $whatsapp = $_POST['whatsapp'] ?? '';

    if (empty($name) || empty($email)) {
        throw new Exception('Nome e email são obrigatórios');
    }

    // Start transaction
    $pdo->beginTransaction();

    // First, ensure the users table exists with correct structure
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            balance DECIMAL(10,2) DEFAULT 0.00,
            is_admin TINYINT(1) DEFAULT 0,
            is_online TINYINT(1) DEFAULT 0,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Check if user already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $existingUser = $stmt->fetch();

    if ($existingUser) {
        $userId = $existingUser['id'];

        // Update user info if exists
        $stmt = $pdo->prepare("UPDATE users SET last_activity = NOW(), is_online = 1 WHERE id = ?");
        $stmt->execute([$userId]);
    } else {
        // Create new user
        $username = preg_replace('/[^a-zA-Z0-9_]/', '', strtolower($name)) . '_' . rand(1000, 9999);
        $password = password_hash('temp123', PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, balance, is_online, last_activity) VALUES (?, ?, ?, 0.00, 1, NOW())");
        $stmt->execute([$username, $email, $password]);

        $userId = $pdo->lastInsertId();
    }

    // Store user ID in PHP session
    $_SESSION['user_id'] = $userId;

    // Commit transaction
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'message' => 'Usuário criado/atualizado com sucesso'
    ]);

} catch (Exception $e) {
    // Rollback transaction if there was an error
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
