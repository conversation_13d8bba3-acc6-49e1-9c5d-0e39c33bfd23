<?php
session_start();
require_once '../../database/connection.php';

if (!isset($_SESSION['admin_id']) || !isset($_POST['conversation_id']) || !isset($_POST['message'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

$conversation_id = intval($_POST['conversation_id']);
$message = trim($_POST['message']);
$admin_id = $_SESSION['admin_id'];

if (empty($message)) {
    http_response_code(400);
    exit('Mensagem vazia');
}

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();

    // Inserir mensagem
    $stmt = $pdo->prepare("
        INSERT INTO chat_messages (
            conversation_id, 
            sender_id, 
            message, 
            is_from_customer, 
            created_at
        ) VALUES (
            :conversation_id,
            :admin_id,
            :message,
            0,
            NOW()
        )
    ");
    
    $stmt->execute([
        ':conversation_id' => $conversation_id,
        ':admin_id' => $admin_id,
        ':message' => $message
    ]);

    // Atualizar timestamp da última mensagem
    $stmt = $pdo->prepare("
        UPDATE chat_conversations 
        SET last_message_at = NOW()
        WHERE id = :conversation_id
    ");
    $stmt->execute([':conversation_id' => $conversation_id]);

    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao enviar mensagem']);
}
