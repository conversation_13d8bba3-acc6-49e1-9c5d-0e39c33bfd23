<?php
session_start();
require_once 'database/connection.php';

echo "<h1>🧪 Teste Completo do Sistema de Chat</h1>";

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>1. 📊 Verificação das Tabelas</h2>";
    
    // Verificar tabelas
    $tables = ['chat_users', 'chat_messages', 'admins'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>✅ Tabela <strong>$table</strong>: $count registros</p>";
        } else {
            echo "<p>❌ Tabela <strong>$table</strong>: NÃO EXISTE</p>";
        }
    }
    
    echo "<h2>2. 👥 Usuários de Chat</h2>";
    
    $stmt = $pdo->query("SELECT id, name, email, is_admin, created_at FROM chat_users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "<p>⚠️ Nenhum usuário encontrado</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Admin</th><th>Criado em</th></tr>";
        foreach ($users as $user) {
            $isAdmin = $user['is_admin'] ? '✅ Sim' : '❌ Não';
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>$isAdmin</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. 💬 Mensagens</h2>";
    
    $stmt = $pdo->query("
        SELECT 
            cm.id,
            cm.user_id,
            cu.name as user_name,
            cm.message,
            cm.is_admin,
            FROM_UNIXTIME(cm.timestamp) as sent_at
        FROM chat_messages cm
        LEFT JOIN chat_users cu ON cm.user_id = cu.id
        ORDER BY cm.timestamp DESC
        LIMIT 10
    ");
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($messages)) {
        echo "<p>⚠️ Nenhuma mensagem encontrada</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Usuário</th><th>Mensagem</th><th>Admin</th><th>Enviado em</th></tr>";
        foreach ($messages as $msg) {
            $isAdmin = $msg['is_admin'] ? '✅ Admin' : '👤 Cliente';
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['user_name']} (ID: {$msg['user_id']})</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>$isAdmin</td>";
            echo "<td>{$msg['sent_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>4. 🔐 Sessão Atual</h2>";
    
    if (empty($_SESSION)) {
        echo "<p>⚠️ Nenhuma sessão ativa</p>";
    } else {
        echo "<pre>";
        print_r($_SESSION);
        echo "</pre>";
    }
    
    echo "<h2>5. 🧪 Teste de Inserção</h2>";
    
    // Testar inserção de usuário
    try {
        $testName = "Usuário Teste " . date('H:i:s');
        $testEmail = "teste" . time() . "@email.com";
        
        $stmt = $pdo->prepare("
            INSERT INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) 
            VALUES (?, ?, '11999999999', 0, NOW(), NOW())
        ");
        $stmt->execute([$testName, $testEmail]);
        
        $testUserId = $pdo->lastInsertId();
        echo "<p>✅ Usuário de teste criado: ID $testUserId - $testName</p>";
        
        // Testar inserção de mensagem
        $testMessage = "Mensagem de teste " . date('H:i:s');
        $stmt = $pdo->prepare("
            INSERT INTO chat_messages (user_id, message, is_admin, timestamp) 
            VALUES (?, ?, 0, UNIX_TIMESTAMP())
        ");
        $stmt->execute([$testUserId, $testMessage]);
        
        echo "<p>✅ Mensagem de teste criada: '$testMessage'</p>";
        
        // Limpar dados de teste
        $pdo->prepare("DELETE FROM chat_messages WHERE user_id = ?")->execute([$testUserId]);
        $pdo->prepare("DELETE FROM chat_users WHERE id = ?")->execute([$testUserId]);
        
        echo "<p>🧹 Dados de teste removidos</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Erro no teste: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. 🔗 Links de Teste</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='admin_login_test.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>🔐 Login Admin</a>";
    echo "<a href='chat.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>💬 Chat Cliente</a>";
    echo "<a href='admin/chat.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px;'>👨‍💼 Chat Admin</a>";
    echo "<a href='fix_chat_foreign_keys.php' style='display: inline-block; padding: 10px 20px; margin: 5px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px;'>🔧 Corrigir FK</a>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724; margin-top: 20px;'>";
    echo "<h4>✅ Teste Concluído!</h4>";
    echo "<p>Se todos os itens acima estão com ✅, o sistema deve estar funcionando.</p>";
    echo "<p><strong>Próximos passos:</strong></p>";
    echo "<ol>";
    echo "<li>Faça login como admin usando o link acima</li>";
    echo "<li>Teste o chat do cliente</li>";
    echo "<li>Teste o chat do admin</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante o teste:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background: #e9ecef;
    font-weight: bold;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    overflow-x: auto;
}

h1, h2 {
    color: #495057;
}

h1 {
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
    margin-top: 30px;
}
</style>
