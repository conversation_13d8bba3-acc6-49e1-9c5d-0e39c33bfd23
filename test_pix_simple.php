<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste PIX Simples</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>🧪 Teste PIX Simples</h4>
                    </div>
                    <div class="card-body">
                        <p>Teste do sistema PIX sem conflitos de JavaScript</p>
                        
                        <div class="product-card border p-3 mb-3">
                            <h5>Produto Teste</h5>
                            <p class="text-muted">Produto para teste do sistema PIX</p>
                            <h4 class="text-primary">R$ 10,00</h4>
                            <button class="btn btn-success" onclick="testPix()">
                                💳 Comprar com PIX
                            </button>
                        </div>
                        
                        <div id="debug-info" class="mt-3">
                            <h6>Debug Info:</h6>
                            <div id="debug-log" class="bg-light p-2 small" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function addDebugLog(message) {
        const debugLog = document.getElementById('debug-log');
        const timestamp = new Date().toLocaleTimeString();
        debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
        debugLog.scrollTop = debugLog.scrollHeight;
        console.log(message);
    }

    async function testPix() {
        try {
            addDebugLog('🚀 Iniciando teste PIX...');
            
            // Mostrar formulário de dados
            const { value: formData } = await Swal.fire({
                title: 'Dados para Pagamento',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Nome Completo *</label>
                            <input type="text" id="test_name" class="form-control" value="João Teste" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">E-mail *</label>
                            <input type="email" id="test_email" class="form-control" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">WhatsApp *</label>
                            <input type="tel" id="test_whatsapp" class="form-control" value="11999999999" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">CPF/CNPJ *</label>
                            <input type="text" id="test_document" class="form-control" value="12345678901" required>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="test_terms" checked required>
                            <label class="form-check-label" for="test_terms">
                                Concordo com os termos
                            </label>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Gerar PIX',
                preConfirm: () => {
                    const name = document.getElementById('test_name').value;
                    const email = document.getElementById('test_email').value;
                    const whatsapp = document.getElementById('test_whatsapp').value;
                    const doc = document.getElementById('test_document').value;
                    const terms = document.getElementById('test_terms').checked;
                    
                    if (!name || !email || !whatsapp || !doc || !terms) {
                        Swal.showValidationMessage('Preencha todos os campos');
                        return false;
                    }
                    
                    return { name, email, whatsapp, document: doc };
                }
            });
            
            if (!formData) {
                addDebugLog('❌ Cancelado pelo usuário');
                return;
            }
            
            addDebugLog('✅ Dados coletados: ' + JSON.stringify(formData));
            
            // Processar pagamento
            addDebugLog('📡 Enviando dados para o servidor...');
            
            const paymentData = {
                product_id: 1,
                customer_name: formData.name,
                customer_email: formData.email,
                customer_whatsapp: formData.whatsapp,
                customer_document: formData.document
            };
            
            const response = await fetch('process_payment_v2.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(paymentData)
            });
            
            addDebugLog('📨 Resposta recebida, status: ' + response.status);
            
            const responseText = await response.text();
            addDebugLog('📄 Resposta bruta: ' + responseText.substring(0, 200) + '...');
            
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                throw new Error('Resposta inválida do servidor: ' + responseText.substring(0, 100));
            }
            
            if (!data.success) {
                throw new Error(data.error || 'Erro desconhecido');
            }
            
            addDebugLog('✅ PIX gerado com sucesso!');
            
            // Mostrar PIX
            Swal.fire({
                title: 'PIX Gerado!',
                html: `
                    <div class="text-center">
                        <h5>Valor: R$ ${data.amount}</h5>
                        <img src="data:image/png;base64,${data.qr_code}" alt="QR Code" style="max-width: 200px;">
                        <div class="mt-3">
                            <label>Código PIX:</label>
                            <textarea class="form-control" rows="3" readonly>${data.pix_code}</textarea>
                        </div>
                    </div>
                `,
                confirmButtonText: 'OK'
            });
            
            addDebugLog('🎉 Teste concluído com sucesso!');
            
        } catch (error) {
            addDebugLog('❌ Erro: ' + error.message);
            Swal.fire({
                icon: 'error',
                title: 'Erro',
                text: error.message
            });
        }
    }

    // Log inicial
    addDebugLog('🔧 Sistema de teste carregado');
    </script>
</body>
</html>
