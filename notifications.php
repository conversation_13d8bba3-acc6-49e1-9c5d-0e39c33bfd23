<?php
require_once 'admin/database/connection.php';
require_once 'admin/config/asaas.php';

// Recebe o JSON do webhook
$json = file_get_contents('php://input');
$data = json_decode($json);

// Log da notificação recebida
error_log("[Webhook] Notificação recebida: " . $json);

// Verificar se é uma notificação válida do Asaas
if (!$data || !isset($data->event)) {
    error_log("[Webhook] Payload inválido");
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Payload inválido']);
    exit;
}

try {
    // Conectar ao banco de dados
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Processar eventos de pagamento
    if (strpos($data->event, 'PAYMENT_') === 0) {
        $payment_id = $data->payment->id;
        
        // Verificar status do pagamento
        $ch = curl_init(ASAAS_API_URL . '/payments/' . $payment_id);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            $payment = json_decode($response);
            if ($payment) {
                // Mapear status do Asaas
                $status = mapAsaasStatus($payment->status);
                
                // Log detalhado do pagamento
                error_log(sprintf(
                    "[Webhook] Dados do pagamento - ID: %s, Status: %s, Referência: %s, Status Original: %s",
                    $payment->id,
                    $status,
                    $payment->externalReference,
                    $payment->status
                ));
                
                // Atualizar o status do pagamento no banco de dados
                $stmt = $pdo->prepare("
                    UPDATE orders 
                    SET payment_status = ?, 
                        updated_at = NOW() 
                    WHERE payment_id = ? OR external_reference = ?
                ");
                
                $result = $stmt->execute([
                    $status,
                    $payment->id,
                    $payment->externalReference
                ]);
                
                // Log do resultado da atualização
                error_log(sprintf(
                    "[Webhook] Resultado da atualização - Sucesso: %s, Linhas afetadas: %d",
                    $result ? 'true' : 'false',
                    $stmt->rowCount()
                ));
                
                http_response_code(200);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Notificação processada com sucesso',
                    'status' => $status
                ]);
            } else {
                throw new Exception('Pagamento não encontrado');
            }
        } else {
            throw new Exception('Erro ao consultar pagamento: HTTP ' . $httpCode);
        }
    } else {
        // Outros tipos de eventos
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Evento recebido, mas não processado: ' . $data->event
        ]);
    }
} catch (Exception $e) {
    error_log("[Webhook] Erro: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

