<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../admin/database/connection.php';

function checkAuth() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: login.php');
        exit;
    }

    // Atualiza o último acesso
    if (isset($_SESSION['admin_id'])) {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            $stmt = $pdo->prepare("UPDATE admins SET updated_at = NOW() WHERE id = ?");
            $stmt->execute([$_SESSION['admin_id']]);
        } catch (PDOException $e) {
            // Silently fail - não queremos interromper a sessão por erro de atualização
        }
    }
}

function login($username, $password) {
    global $pdo;
    
    try {
        $db = Database::getInstance();
        $pdo = $db->getConnection();
        
        $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['admin_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_logged_in'] = true;
            return true;
        }
    } catch (PDOException $e) {
        error_log("Erro no login: " . $e->getMessage());
    }
    
    return false;
}

function logout() {
    session_destroy();
    header('Location: login.php');
    exit;
}
