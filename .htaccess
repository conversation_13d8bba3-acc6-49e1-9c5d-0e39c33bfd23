# .htaccess Otimizado para SEO - KESUNG SITE
# Configurações para URLs amigáveis, cache e performance

# Habilitar rewrite engine
RewriteEngine On

# Permitir acesso a todos os arquivos por padrão
<Files "*">
    Require all granted
</Files>

# ========================================
# URLs AMIGÁVEIS PARA SEO
# ========================================

# URLs de categorias principais
RewriteRule ^cursos-programacao/?$ cursos-programacao.php [L]
RewriteRule ^scripts-php/?$ scripts-php.php [L]
RewriteRule ^casa-apostas/?$ casa-apostas.php [L]
RewriteRule ^marketing-digital/?$ marketing-digital.php [L]

# URLs de produtos individuais
RewriteRule ^produto/([a-zA-Z0-9-]+)/?$ produto.php?slug=$1 [L,QSA]

# URLs de blog
RewriteRule ^blog/?$ blog.php [L]
RewriteRule ^blog/([a-zA-Z0-9-]+)/?$ blog-post.php?slug=$1 [L,QSA]

# Sitemap dinâmico
RewriteRule ^sitemap\.xml$ generate_sitemap.php [L]

# Redirecionar /admin para /admin/
RewriteRule ^admin/?$ admin/index.php [L]

# Redirecionar /webhooks para webhooks.php
RewriteRule ^webhooks/?$ webhooks.php [L]

# ========================================
# SEGURANÇA E PROTEÇÃO
# ========================================

# Desabilitar listagem de diretórios
Options -Indexes

# Proteger arquivos sensíveis
<FilesMatch "\.(log|sql|bak|backup|htaccess|htpasswd|ini|sh|inc)$">
    Require all denied
</FilesMatch>

# ========================================
# COMPRESSÃO GZIP
# ========================================
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# ========================================
# CACHE DO NAVEGADOR
# ========================================
<IfModule mod_expires.c>
    ExpiresActive on

    # Imagens
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS e JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# ========================================
# HEADERS DE SEGURANÇA
# ========================================
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# ========================================
# PHP SETTINGS
# ========================================
php_value max_execution_time 300
php_value memory_limit 256M
php_value upload_max_filesize 10M
php_value post_max_size 10M