# Sistema de Vendas com PIX e Tracking de Visitantes

Sistema completo de vendas online com pagamento PIX integrado e sistema avançado de tracking de visitantes.

## 🚀 Funcionalidades

### 💳 Sistema de Pagamento PIX Online
- Geração automática de QR Code PIX em tempo real
- Formulário responsivo com validações
- Verificação automática de status de pagamento via webhook
- Interface moderna com SweetAlert2
- Processamento instantâneo de pagamentos

### 🌍 Sistema de Tracking de Visitantes Online
- Detecção automática de localização real (país, estado, cidade)
- Identificação avançada de VPNs em tempo real
- Rastreamento de fontes de tráfego (WhatsApp, Instagram, Facebook, etc.)
- Logs detalhados de navegação online
- Analytics em tempo real

### 📊 Painel Administrativo Online
- Gerenciamento de produtos em tempo real
- Visualização de visitantes ao vivo
- Relatórios de vendas instantâneos
- Configurações do sistema online
- Logs de email e pagamentos em tempo real
- Dashboard com métricas atualizadas automaticamente

## 🛠️ Tecnologias Utilizadas

- **Backend:** PHP 8.0+
- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **Database:** MySQL 8.0+
- **Frameworks:** Bootstrap 5.3, SweetAlert2
- **APIs:** Múltiplas APIs de geolocalização
- **Pagamentos:** API PIX meiodepagamento.com

## 📋 Requisitos do Sistema

- PHP 8.0 ou superior
- MySQL 8.0 ou superior
- Apache/Nginx
- Extensões PHP: PDO, cURL, JSON, GD
- SSL/HTTPS (recomendado para produção)

## ⚙️ Instalação

1. **Clone o repositório:**
   ```bash
   git clone [url-do-repositorio]
   cd sistema-vendas-pix
   ```

2. **Configure o banco de dados:**
   - Importe o arquivo SQL fornecido
   - Configure as credenciais em `database/connection.php`

3. **Configure as APIs:**
   - PIX: Configure o token em `config/pix_config.php`
   - Email: Configure SMTP em `admin/email_settings.php`

4. **Permissões:**
   ```bash
   chmod 755 uploads/
   chmod 755 logs/
   ```

## 🔧 Configuração

### Banco de Dados
Edite `database/connection.php`:
```php
private $host = 'localhost';
private $db_name = 'seu_banco';
private $username = 'seu_usuario';
private $password = 'sua_senha';
```

### PIX
Configure em `config/pix_config.php`:
```php
define('PIX_TOKEN', 'seu_token_aqui');
define('PIX_WEBHOOK_URL', 'https://seusite.com/webhook_v2.php');
```

## 📁 Estrutura do Projeto

```
/
├── admin/                  # Painel administrativo
│   ├── dashboard.php      # Dashboard principal
│   ├── products.php       # Gerenciar produtos
│   ├── visitors.php       # Logs de visitantes
│   └── ...
├── assets/                # Recursos estáticos
│   ├── css/              # Estilos
│   ├── js/               # JavaScript
│   └── images/           # Imagens
├── database/             # Configuração do banco
├── includes/             # Arquivos incluídos
│   ├── visitor_tracker.php  # Sistema de tracking
│   └── ...
├── uploads/              # Arquivos enviados
└── index.php            # Página principal
```

## 🌐 URLs Principais

- **Home:** `http://localhost/`
- **Admin:** `http://localhost/admin/`
- **Visitantes:** `http://localhost/admin/visitors.php`
- **Produtos:** `http://localhost/admin/products.php`

## 🔒 Segurança

- Validação de entrada em todos os formulários
- Proteção contra SQL Injection
- Sanitização de dados
- Controle de acesso ao painel admin
- Logs de segurança

## 📈 Monitoramento

O sistema inclui:
- Logs automáticos de visitantes
- Rastreamento de conversões
- Métricas de performance
- Alertas de erro

## 🆘 Suporte

Para suporte técnico:
1. Verifique os logs em `/logs/`
2. Consulte a documentação
3. Entre em contato com o desenvolvedor

## 📝 Licença

Este projeto é proprietário. Todos os direitos reservados.

---

**Desenvolvido com ❤️ para máxima performance e segurança.**
📋 KESUNG SITE - Versão Atual
🏷️ Versão Principal: 1.0.0
Baseado nos arquivos analisados:

📦 Service Worker Cache:
✅ Cache Name: kesung-site-v1.0.0 (sw.js linha 2)
📄 Composer/Dependências:
✅ Root Version: 1.0.0+no-version-set (vendor/composer/installed.php)
✅ MercadoPago SDK: 3.0.8
✅ PHPMailer: ^6.9
🛠️ Tecnologias e Versões:
Backend:
🔧 PHP: 8.0+ (requerido 8.2+ para MercadoPago)
🗄️ MySQL: 8.0+
📦 Composer: Ativo com autoload
Frontend:
🎨 Bootstrap: 5.3.0
⚡ Font Awesome: 6.4.0
🍭 SweetAlert2: 11 (latest)
📱 PWA: Completo com Service Worker
APIs Integradas:
💳 PIX: meiodepagamento.com + Asaas
📧 Email: PHPMailer 6.9+
🌍 Geolocalização: Múltiplas APIs
📊 Analytics: Google Analytics (configurável)
🚀 Funcionalidades da Versão 1.0.0:
✅ Core Features:
🛒 Sistema de vendas completo
💳 Pagamento PIX integrado
📱 PWA com notificações push
🌍 Tracking avançado de visitantes
👨‍💼 Painel administrativo completo
✅ SEO & Performance:
🔍 Sitemap dinâmico
📈 Meta tags otimizadas
⚡ Cache e compressão
📱 URLs amigáveis
🤖 Robots.txt otimizado
✅ Segurança:
🔒 Proteção SQL Injection
🛡️ Headers de segurança
🚫 Validação de entrada
📝 Logs de auditoria
✅ Mobile & PWA:
📱 Design 100% responsivo
🔔 Push notifications
📲 Instalação como app
🔄 Sincronização offline
📊 Status do Projeto:
🎯 Estágio: Produção Estável
✅ Core completo e funcional
✅ Testes realizados
✅ SEO implementado
✅ PWA ativo
✅ Pagamentos funcionando
🔄 Próximas Versões:
🚀 v1.1.0: Melhorias de UX
🚀 v1.2.0: Novos métodos de pagamento
🚀 v2.0.0: Sistema de afiliados
📅 Timeline:
🎉 v1.0.0: Dezembro 2024 (Atual)
📈 Desenvolvimento: Contínuo
🔄 Atualizações: Semanais
Resumo: Você tem a versão 1.0.0 estável e completa do KESUNG SITE! 🎉✨