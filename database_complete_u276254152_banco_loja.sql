-- =====================================================
-- SQL COMPLETO PARA IMPORTAR VIA PHPMYADMIN
-- Sistema: KESUNG SITE - C:\xampp\htdocs
-- Banco: u276254152_banco_loja
-- Data: 2025-01-01
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- CONFIGURAÇÕES INICIAIS
-- =====================================================

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- TABELA: users (Usuários principais do sistema)
-- =====================================================

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `balance` decimal(10,2) DEFAULT 0.00,
  `is_admin` tinyint(1) DEFAULT 0,
  `is_online` tinyint(1) DEFAULT 0,
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: admins (Administradores do sistema)
-- =====================================================

CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(20) DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: products (Produtos do sistema)
-- =====================================================

CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: orders (Pedidos/Vendas)
-- =====================================================

CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(255) NOT NULL,
  `customer_lastname` varchar(100) DEFAULT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_document` varchar(20) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_id` varchar(100) DEFAULT NULL,
  `payment_status` enum('pending','paid','approved','rejected','cancelled') DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT 'pix',
  `external_reference` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_email` (`customer_email`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_external_reference` (`external_reference`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: sales (Vendas - compatibilidade)
-- =====================================================

CREATE TABLE `sales` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(255) NOT NULL,
  `customer_lastname` varchar(100) DEFAULT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_document` varchar(20) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_id` varchar(100) DEFAULT NULL,
  `payment_status` varchar(50) DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT 'pix',
  `external_reference` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_email` (`customer_email`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: customers (Clientes)
-- =====================================================

CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `document` varchar(20) DEFAULT NULL,
  `total_orders` int(11) DEFAULT 0,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_name` (`name`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: chat_users (Usuários do chat)
-- =====================================================

CREATE TABLE `chat_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: chat_messages (Mensagens do chat)
-- =====================================================

CREATE TABLE `chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `is_admin` tinyint(1) DEFAULT 0,
  `timestamp` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: chat_sessions (Sessões do chat)
-- =====================================================

CREATE TABLE `chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `status` enum('pending','active','closed') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `chat_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: visitors (Visitantes do site)
-- =====================================================

CREATE TABLE `visitors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(45) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `country` varchar(100) DEFAULT 'Brasil',
  `city` varchar(100) DEFAULT 'Desconhecido',
  `is_vpn` tinyint(1) DEFAULT 0,
  `visit_count` int(11) DEFAULT 1,
  `first_visit` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_visit` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `visit_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_unique` (`ip`),
  KEY `ip_address` (`ip_address`),
  KEY `visit_time` (`visit_time`),
  KEY `idx_country` (`country`),
  KEY `idx_is_vpn` (`is_vpn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: download_tokens (Tokens de download)
-- =====================================================

CREATE TABLE `download_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `download_count` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: email_logs (Logs de emails enviados)
-- =====================================================

CREATE TABLE `email_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_email` varchar(255) NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text,
  `status` enum('success','error') DEFAULT 'error',
  `error_message` text,
  `payment_id` varchar(255) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_email` (`customer_email`),
  KEY `idx_status` (`status`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_sent_at` (`sent_at`),
  FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: email_templates (Templates de email)
-- =====================================================

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `variables` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: system_settings (Configurações do sistema)
-- =====================================================

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text,
  `setting_description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: payment_credentials (Credenciais de pagamento)
-- =====================================================

CREATE TABLE `payment_credentials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `public_key` varchar(255) NOT NULL,
  `access_token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELAS PWA E NOTIFICAÇÕES
-- =====================================================

-- TABELA: app_installs (Instalações do app PWA)
CREATE TABLE `app_installs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `install_date` datetime NOT NULL,
  `platform` varchar(50) DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_install_date` (`install_date`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_platform` (`platform`),
  KEY `idx_app_installs_date_platform` (`install_date`,`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TABELA: push_subscriptions (Assinaturas de push notifications)
CREATE TABLE `push_subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` text NOT NULL,
  `keys_json` json NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_endpoint` (`endpoint`(255)),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_push_subscriptions_active` (`is_active`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TABELA: notification_logs (Logs de notificações enviadas)
CREATE TABLE `notification_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `body` text,
  `url` varchar(500) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `sent_count` int(11) DEFAULT 0,
  `failed_count` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_notification_logs_date` (`created_at` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TABELA: notification_campaigns (Campanhas de notificação)
CREATE TABLE `notification_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `url` varchar(500) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL,
  `sent_at` datetime DEFAULT NULL,
  `status` enum('draft','scheduled','sent','cancelled') DEFAULT 'draft',
  `target_audience` json DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled_at` (`scheduled_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TABELA: app_users (Usuários detalhados do app)
CREATE TABLE `app_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `first_install` datetime NOT NULL,
  `last_seen` datetime DEFAULT NULL,
  `install_count` int(11) DEFAULT 1,
  `platform` varchar(50) DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `preferences` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_ip_user_agent` (`ip_address`,`user_agent`(255)),
  KEY `idx_platform` (`platform`),
  KEY `idx_last_seen` (`last_seen`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TABELA: notification_interactions (Interações com notificações)
CREATE TABLE `notification_interactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notification_log_id` int(11) DEFAULT NULL,
  `subscription_id` int(11) DEFAULT NULL,
  `action` enum('delivered','clicked','dismissed','failed') NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `user_agent` text,
  `ip_address` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_action` (`action`),
  KEY `idx_timestamp` (`timestamp`),
  FOREIGN KEY (`notification_log_id`) REFERENCES `notification_logs` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`subscription_id`) REFERENCES `push_subscriptions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: product_ratings (Avaliações de produtos)
-- =====================================================

CREATE TABLE `product_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 AND `rating` <= 5),
  `comment` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_customer_email` (`customer_email`),
  KEY `idx_rating` (`rating`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- VIEWS PARA RELATÓRIOS
-- =====================================================

-- VIEW: Estatísticas de instalação do app
CREATE VIEW `v_app_install_stats` AS
SELECT
    DATE(`install_date`) as `install_date`,
    `platform`,
    COUNT(*) as `installs`,
    COUNT(DISTINCT `ip_address`) as `unique_users`
FROM `app_installs`
GROUP BY DATE(`install_date`), `platform`
ORDER BY `install_date` DESC;

-- VIEW: Performance de notificações
CREATE VIEW `v_notification_performance` AS
SELECT
    nl.`id`,
    nl.`title`,
    nl.`created_at`,
    nl.`sent_count`,
    nl.`failed_count`,
    COALESCE(SUM(CASE WHEN ni.`action` = 'clicked' THEN 1 ELSE 0 END), 0) as `clicks`,
    COALESCE(SUM(CASE WHEN ni.`action` = 'dismissed' THEN 1 ELSE 0 END), 0) as `dismissals`,
    CASE
        WHEN nl.`sent_count` > 0 THEN
            ROUND((COALESCE(SUM(CASE WHEN ni.`action` = 'clicked' THEN 1 ELSE 0 END), 0) / nl.`sent_count`) * 100, 2)
        ELSE 0
    END as `click_rate`
FROM `notification_logs` nl
LEFT JOIN `notification_interactions` ni ON nl.`id` = ni.`notification_log_id`
GROUP BY nl.`id`, nl.`title`, nl.`created_at`, nl.`sent_count`, nl.`failed_count`
ORDER BY nl.`created_at` DESC;

-- =====================================================
-- DADOS INICIAIS
-- =====================================================

-- Admin padrão
INSERT INTO `admins` (`username`, `name`, `email`, `password`, `role`) VALUES
('admin', 'Administrador', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin');

-- Templates de email padrão
INSERT INTO `email_templates` (`name`, `subject`, `body`, `description`, `is_active`) VALUES
('payment_confirmation', 'Confirmação de Pagamento - {product_name}', '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><h2>Olá {customer_name},</h2><p>Seu pagamento foi confirmado com sucesso!</p><div style="margin: 20px 0; padding: 20px; background-color: #f9f9f9; border-radius: 5px;"><h3>Detalhes do Pagamento:</h3><p><strong>Produto:</strong> {product_name}</p><p><strong>Valor:</strong> R$ {product_price}</p><p><strong>ID do Pagamento:</strong> {payment_id}</p></div><p>Obrigado pela sua compra!</p></div>', 'Email de confirmação de pagamento', 1),
('payment_pending', 'Pagamento Pendente - {product_name}', '<h2>Pagamento Pendente</h2><p>Olá {customer_name},</p><p>Seu pagamento para <strong>{product_name}</strong> ainda está pendente.</p><p>Assim que o pagamento for confirmado, você receberá o link para download.</p>', 'Email enviado quando o pagamento está pendente', 1),
('download_ready', 'Download Liberado - {product_name}', '<h2>Download Liberado!</h2><p>Olá {customer_name},</p><p>Seu download está pronto! Clique no link abaixo para baixar:</p><p><a href="{download_link}" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Baixar Agora</a></p>', 'Email com link de download', 1);

-- Configurações do sistema
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_description`) VALUES
('site_name', 'KESUNG SITE', 'Nome do site'),
('site_description', 'Sistema completo de vendas online', 'Descrição do site'),
('tinymce_api_key', 'no-api-key', 'Chave da API do TinyMCE'),
('pix_key', '', 'Chave PIX para pagamentos'),
('mercadopago_public_key', '', 'Chave pública do Mercado Pago'),
('mercadopago_access_token', '', 'Token de acesso do Mercado Pago');

-- Campanhas de notificação padrão
INSERT INTO `notification_campaigns` (`name`, `title`, `body`, `url`, `status`) VALUES
('Boas-vindas', 'Bem-vindo ao KESUNG SITE!', 'Obrigado por instalar nosso app! Fique por dentro das melhores promoções.', '/', 'draft'),
('Promoção Semanal', 'Promoção Especial!', 'Não perca! Descontos de até 50% em produtos selecionados.', '/#promocoes', 'draft'),
('Novo Produto', 'Novo Produto Disponível!', 'Confira nosso mais novo lançamento com preço especial.', '/#novidades', 'draft'),
('Chat Atendimento', 'Mensagem do Atendimento', 'Nossa equipe respondeu sua mensagem. Clique para ver.', '/#chat', 'draft');

-- Produtos de exemplo
INSERT INTO `products` (`name`, `description`, `price`, `status`) VALUES
('Curso de Programação Web', 'Curso completo de desenvolvimento web com HTML, CSS, JavaScript, PHP e MySQL', 197.77, 'active'),
('E-book Marketing Digital', 'Guia completo de marketing digital para iniciantes', 97.77, 'active'),
('Template WordPress Premium', 'Template profissional para WordPress com design responsivo', 147.77, 'active'),
('Script PHP Completo', 'Sistema completo em PHP para gestão de vendas online', 297.77, 'active'),
('Pacote de Ícones Premium', 'Mais de 1000 ícones vetorizados para seus projetos', 67.77, 'active');

-- Usuários de teste para chat
INSERT INTO `chat_users` (`name`, `email`, `whatsapp`, `is_admin`) VALUES
('Gabriela Coutinho', '<EMAIL>', '11987654321', 0),
('João Silva', '<EMAIL>', '11976543210', 0),
('Maria Santos', '<EMAIL>', '11965432109', 0),
('Pedro Costa', '<EMAIL>', '11954321098', 0),
('Ana Oliveira', '<EMAIL>', '11943210987', 0);

-- Clientes de exemplo
INSERT INTO `customers` (`name`, `email`, `whatsapp`, `total_orders`, `total_spent`) VALUES
('João Silva', '<EMAIL>', '11976543210', 2, 395.54),
('Maria Santos', '<EMAIL>', '11965432109', 1, 197.77),
('Pedro Costa', '<EMAIL>', '11954321098', 3, 543.31),
('Ana Oliveira', '<EMAIL>', '11943210987', 1, 97.77);

-- Pedidos de exemplo
INSERT INTO `orders` (`customer_name`, `customer_email`, `product_id`, `amount`, `total_amount`, `payment_status`, `payment_method`, `external_reference`, `payment_id`) VALUES
('João Silva', '<EMAIL>', 1, 197.77, 197.77, 'approved', 'pix', 'REF001', 'PAY001'),
('Maria Santos', '<EMAIL>', 2, 97.77, 97.77, 'pending', 'pix', 'REF002', 'PAY002'),
('Pedro Costa', '<EMAIL>', 3, 147.77, 147.77, 'approved', 'pix', 'REF003', 'PAY003'),
('Ana Oliveira', '<EMAIL>', 4, 297.77, 297.77, 'approved', 'pix', 'REF004', 'PAY004');

-- =====================================================
-- CONFIGURAÇÕES FINAIS
-- =====================================================

-- Habilitar foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Auto increment inicial para as tabelas principais
ALTER TABLE `users` AUTO_INCREMENT = 1000;
ALTER TABLE `admins` AUTO_INCREMENT = 100;
ALTER TABLE `products` AUTO_INCREMENT = 1;
ALTER TABLE `orders` AUTO_INCREMENT = 1;
ALTER TABLE `customers` AUTO_INCREMENT = 1;
ALTER TABLE `chat_users` AUTO_INCREMENT = 1;
ALTER TABLE `chat_messages` AUTO_INCREMENT = 1;

-- =====================================================
-- TRIGGERS PARA MANTER DADOS SINCRONIZADOS
-- =====================================================

-- Trigger para atualizar estatísticas do cliente após nova venda
DELIMITER $$
CREATE TRIGGER `update_customer_stats_after_order`
AFTER INSERT ON `orders`
FOR EACH ROW
BEGIN
    UPDATE `customers`
    SET
        `total_orders` = (SELECT COUNT(*) FROM `orders` WHERE `customer_email` = NEW.`customer_email`),
        `total_spent` = (SELECT COALESCE(SUM(`amount`), 0) FROM `orders` WHERE `customer_email` = NEW.`customer_email` AND `payment_status` = 'approved')
    WHERE `email` = NEW.`customer_email`;

    -- Inserir cliente se não existir
    INSERT IGNORE INTO `customers` (`name`, `email`, `total_orders`, `total_spent`, `created_at`)
    VALUES (NEW.`customer_name`, NEW.`customer_email`, 1,
        CASE WHEN NEW.`payment_status` = 'approved' THEN NEW.`amount` ELSE 0 END,
        NOW());
END$$

-- Trigger para sincronizar tabela sales com orders
CREATE TRIGGER `sync_sales_after_order_insert`
AFTER INSERT ON `orders`
FOR EACH ROW
BEGIN
    INSERT INTO `sales` (
        `customer_name`, `customer_lastname`, `customer_email`, `customer_document`,
        `product_id`, `amount`, `total_amount`, `payment_id`, `payment_status`,
        `payment_method`, `external_reference`, `created_at`
    ) VALUES (
        NEW.`customer_name`, NEW.`customer_lastname`, NEW.`customer_email`, NEW.`customer_document`,
        NEW.`product_id`, NEW.`amount`, NEW.`total_amount`, NEW.`payment_id`, NEW.`payment_status`,
        NEW.`payment_method`, NEW.`external_reference`, NEW.`created_at`
    );
END$$

DELIMITER ;

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- =====================================================
-- INSTRUÇÕES DE USO:
-- 1. Acesse o phpMyAdmin
-- 2. Selecione o banco 'u276254152_banco_loja'
-- 3. Vá na aba 'Importar'
-- 4. Selecione este arquivo SQL
-- 5. Clique em 'Executar'
--
-- DADOS DE ACESSO PADRÃO:
-- Admin: <EMAIL> / admin123
--
-- SISTEMA COMPLETO INCLUINDO:
-- - Sistema de usuários e administradores
-- - Sistema de produtos e vendas
-- - Sistema de chat completo
-- - Sistema PWA com notificações
-- - Sistema de emails e templates
-- - Sistema de downloads com tokens
-- - Relatórios e estatísticas
-- =====================================================
