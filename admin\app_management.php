<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Inicializar variáveis com valores padrão
$totalInstalls = 0;
$installsToday = 0;
$activeSubscriptions = 0;
$notificationsToday = 0;
$platformStats = [];
$recentInstalls = [];
$campaigns = [];

// Buscar estatísticas
try {
    // Verificar se as tabelas existem
    $stmt = $pdo->query("SHOW TABLES LIKE 'app_installs'");
    if ($stmt->rowCount() > 0) {
        // Total de instalações
        $stmt = $pdo->query("SELECT COUNT(*) FROM app_installs");
        $totalInstalls = $stmt->fetchColumn();

        // Instalações hoje
        $stmt = $pdo->query("SELECT COUNT(*) FROM app_installs WHERE DATE(install_date) = CURDATE()");
        $installsToday = $stmt->fetchColumn();

        // Instalações por plataforma
        $stmt = $pdo->query("
            SELECT platform, COUNT(*) as count
            FROM app_installs
            GROUP BY platform
            ORDER BY count DESC
        ");
        $platformStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Últimas instalações
        $stmt = $pdo->query("
            SELECT * FROM app_installs
            ORDER BY install_date DESC
            LIMIT 10
        ");
        $recentInstalls = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Verificar tabela de subscriptions
    $stmt = $pdo->query("SHOW TABLES LIKE 'push_subscriptions'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM push_subscriptions WHERE is_active = 1");
        $activeSubscriptions = $stmt->fetchColumn();
    }

    // Verificar tabela de logs de notificação
    $stmt = $pdo->query("SHOW TABLES LIKE 'notification_logs'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT COALESCE(SUM(sent_count), 0) FROM notification_logs WHERE DATE(created_at) = CURDATE()");
        $notificationsToday = $stmt->fetchColumn();
    }

    // Verificar tabela de campanhas
    $stmt = $pdo->query("SHOW TABLES LIKE 'notification_campaigns'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("
            SELECT * FROM notification_campaigns
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $campaigns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de App - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link href="css/standardized.css" rel="stylesheet">
    <style>
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        .text-xs {
            font-size: 0.7rem;
        }
        .text-gray-300 {
            color: #dddfeb !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        .shadow {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        }
        .platform-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            margin-bottom: 8px;
        }
        .platform-badge i {
            font-size: 1.2em;
        }
        .install-item {
            padding: 8px 0;
            border-bottom: 1px solid #e3e6f0;
        }
        .install-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
                <div class="container-fluid">
                    <button class="btn btn-primary" id="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> Administrador
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Perfil</a></li>
                                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Configurações</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid p-4">
                <!-- Page Header -->
                <div class="d-sm-flex align-items-center justify-content-between mb-4">
                    <div>
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-mobile-alt text-primary"></i> Gerenciamento de App PWA
                        </h1>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" onclick="sendTestNotification()">
                            <i class="fas fa-bell"></i> Teste de Notificação
                        </button>
                        <a href="../pwa_demo.php" class="btn btn-info" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Ver Demo
                        </a>
                    </div>
                </div>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Erro:</strong> <?php echo htmlspecialchars($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Sistema PWA Operacional!</strong> Todas as funcionalidades estão funcionando corretamente.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Estatísticas -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total de Instalações
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php echo number_format($totalInstalls); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-download fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Instalações Hoje
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php echo number_format($installsToday); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Subscriptions Ativas
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php echo number_format($activeSubscriptions); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Notificações Hoje
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php echo number_format($notificationsToday); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enviar Notificação -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card shadow">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-bullhorn"></i> Enviar Notificação Push
                                    </h6>
                                    <div class="dropdown no-arrow">
                                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-right shadow">
                                            <div class="dropdown-header">Ações:</div>
                                            <a class="dropdown-item" href="#" onclick="clearForm()">
                                                <i class="fas fa-eraser fa-sm fa-fw mr-2 text-gray-400"></i>
                                                Limpar Formulário
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="notificationForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Título</label>
                                                    <input type="text" class="form-control" id="notificationTitle" 
                                                           placeholder="Ex: Promoção Especial!" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">URL de Destino (opcional)</label>
                                                    <input type="url" class="form-control" id="notificationUrl"
                                                           placeholder="Deixe vazio - notificação só aparece no celular">
                                                    <small class="form-text text-muted">
                                                        <i class="fas fa-info-circle"></i>
                                                        Deixe vazio para apenas mostrar notificação no celular sem abrir nada
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Mensagem</label>
                                            <textarea class="form-control" id="notificationBody" rows="3" 
                                                      placeholder="Digite a mensagem da notificação..." required></textarea>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">URL da Imagem (opcional)</label>
                                                    <input type="url" class="form-control" id="notificationImage" 
                                                           placeholder="https://exemplo.com/imagem.jpg">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Modelo</label>
                                                    <select class="form-control" id="notificationTemplate" onchange="loadTemplate()">
                                                        <option value="">Selecione um modelo...</option>
                                                        <option value="promocao">Promoção</option>
                                                        <option value="novo_produto">Novo Produto</option>
                                                        <option value="chat">Mensagem do Chat</option>
                                                        <option value="carrinho">Carrinho Abandonado</option>
                                                        <option value="bolao">Bolão</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Tipo de Som</label>
                                                    <select class="form-control" id="notificationType">
                                                        <option value="default">Padrão</option>
                                                        <option value="promocao">Promoção</option>
                                                        <option value="chat">Chat</option>
                                                        <option value="bolao">Bolão</option>
                                                        <option value="carrinho">Carrinho</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Enviar para Todos
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Estatísticas por Plataforma -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-chart-pie"></i> Instalações por Plataforma
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($platformStats)): ?>
                                        <?php foreach ($platformStats as $platform): ?>
                                            <div class="platform-badge w-100">
                                                <div class="d-flex justify-content-between align-items-center w-100">
                                                    <span>
                                                        <?php
                                                        $platformIcon = '';
                                                        switch(strtolower($platform['platform'])) {
                                                            case 'android':
                                                                $platformIcon = 'fab fa-android text-success';
                                                                break;
                                                            case 'ios':
                                                                $platformIcon = 'fab fa-apple text-dark';
                                                                break;
                                                            case 'windows':
                                                                $platformIcon = 'fab fa-windows text-primary';
                                                                break;
                                                            case 'macos':
                                                                $platformIcon = 'fab fa-apple text-secondary';
                                                                break;
                                                            case 'linux':
                                                                $platformIcon = 'fab fa-linux text-warning';
                                                                break;
                                                            default:
                                                                $platformIcon = 'fas fa-desktop text-muted';
                                                        }
                                                        ?>
                                                        <i class="<?php echo $platformIcon; ?>"></i>
                                                        <?php echo htmlspecialchars($platform['platform']); ?>
                                                    </span>
                                                    <span class="badge bg-primary rounded-pill"><?php echo $platform['count']; ?></span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Nenhuma instalação registrada ainda.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-history"></i> Últimas Instalações
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($recentInstalls)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>IP</th>
                                                        <th>Plataforma</th>
                                                        <th>Data</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($recentInstalls as $install): ?>
                                                        <tr>
                                                            <td><code><?php echo htmlspecialchars($install['ip_address']); ?></code></td>
                                                            <td>
                                                                <span class="badge bg-secondary">
                                                                    <?php echo htmlspecialchars($install['platform']); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <small><?php echo date('d/m H:i', strtotime($install['install_date'])); ?></small>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">Nenhuma instalação registrada ainda.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Menu Toggle Script -->
    <script>
        document.getElementById("menu-toggle").addEventListener("click", function(e) {
            e.preventDefault();
            document.getElementById("wrapper").classList.toggle("toggled");
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Templates de notificação
        const templates = {
            promocao: {
                title: 'Promoção Especial! 🔥',
                body: 'Não perca! Descontos de até 50% em produtos selecionados. Oferta por tempo limitado!',
                url: '', // Sem URL - só mostra notificação
                type: 'promocao'
            },
            novo_produto: {
                title: 'Novo Produto Disponível! ✨',
                body: 'Confira nosso mais novo lançamento com preço especial de lançamento.',
                url: '', // Sem URL - só mostra notificação
                type: 'default'
            },
            chat: {
                title: 'Nova Mensagem do Chat 💬',
                body: 'Você recebeu uma nova mensagem no chat!',
                url: '', // Sem URL - só mostra notificação
                type: 'chat'
            },
            carrinho: {
                title: 'Carrinho Abandonado 🛒',
                body: 'Você esqueceu alguns itens no seu carrinho!',
                url: '', // Sem URL - só mostra notificação
                type: 'carrinho'
            },
            bolao: {
                title: 'Novo Bolão! ⚽',
                body: 'Um novo bolão foi criado! Faça suas apostas!',
                url: '', // Sem URL - só mostra notificação
                type: 'bolao'
            }
        };

        function loadTemplate() {
            const select = document.getElementById('notificationTemplate');
            const template = templates[select.value];

            if (template) {
                document.getElementById('notificationTitle').value = template.title;
                document.getElementById('notificationBody').value = template.body;
                document.getElementById('notificationUrl').value = template.url || '';
                document.getElementById('notificationType').value = template.type || 'default';
            }
        }

        // Enviar notificação
        document.getElementById('notificationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const title = document.getElementById('notificationTitle').value;
            const body = document.getElementById('notificationBody').value;
            const url = document.getElementById('notificationUrl').value.trim();
            const image = document.getElementById('notificationImage').value.trim();
            const type = document.getElementById('notificationType').value;

            // Preparar dados - só incluir URL se não estiver vazia
            const notificationData = {
                title: title,
                body: body,
                type: type
            };

            // Só adicionar URL se tiver conteúdo
            if (url && url !== '') {
                notificationData.url = url;
            }

            // Só adicionar imagem se tiver conteúdo
            if (image && image !== '') {
                notificationData.image = image;
            }

            try {
                const response = await fetch('/api/app_installs.php?action=send-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(notificationData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Notificação Enviada!',
                        text: `Enviada para ${result.sent} usuários`,
                        timer: 3000
                    });
                    
                    // Limpar formulário
                    document.getElementById('notificationForm').reset();
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: error.message
                });
            }
        });

        // Enviar notificação de teste
        async function sendTestNotification() {
            try {
                const response = await fetch('/api/app_installs.php?action=send-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Teste de Notificação 🔔',
                        body: 'Esta é uma notificação de teste do KESUNG SITE! Apenas aparece no celular.',
                        type: 'default'
                        // Sem URL - só mostra notificação
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Teste Enviado!',
                        text: `Notificação de teste enviada para ${result.sent} usuários`,
                        timer: 3000
                    });
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: error.message
                });
            }
        }

        // Limpar formulário
        function clearForm() {
            document.getElementById('notificationForm').reset();
            document.getElementById('notificationTemplate').value = '';
        }



        // Auto-refresh das estatísticas a cada 30 segundos
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
