<?php
// Configurações do banco de dados
define('DB_HOST', 'localhost'); // Mantenha localhost
define('DB_NAME', 'u276254152_banco_loja'); // Você preencherá com o nome do banco criado na Hostinger
define('DB_USER', 'root'); // Você preencherá com o usuário do banco na Hostinger
define('DB_PASS', 'Geranda@1'); // Você preencherá com a senha do banco na Hostinger

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die("Erro na conexão com o banco de dados: " . $e->getMessage());
}

// Iniciar sessão se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configurações de upload
if (!defined('UPLOAD_DIR_ADMIN')) {
    define('UPLOAD_DIR_ADMIN', __DIR__ . '/../admin/uploads/products/');
}
if (!defined('UPLOAD_DIR_FRONT')) {
    define('UPLOAD_DIR_FRONT', __DIR__ . '/../imagens/products/');
}

// Criar diretórios se não existirem
if (!is_dir(UPLOAD_DIR_ADMIN)) {
    mkdir(UPLOAD_DIR_ADMIN, 0777, true);
}
if (!is_dir(UPLOAD_DIR_FRONT)) {
    mkdir(UPLOAD_DIR_FRONT, 0777, true);
}

// Funções úteis
function formatPrice($price) {
    return number_format($price, 2, ',', '.');
}

function sanitizeString($str) {
    return htmlspecialchars(trim($str), ENT_QUOTES, 'UTF-8');
}
