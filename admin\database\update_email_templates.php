<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Verificar se o template já existe
    $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE name = 'payment_confirmation'");
    $stmt->execute();
    $exists = $stmt->fetch();

    if (!$exists) {
        // Inserir o template de confirmação de pagamento
        $stmt = $pdo->prepare("
            INSERT INTO email_templates (name, subject, body, variables, is_active) 
            VALUES (
                'payment_confirmation',
                'Confirmação de Pagamento - {product_name}',
                '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">
                    <h2>Olá {customer_name},</h2>
                    <p>Seu pagamento foi confirmado com sucesso!</p>
                    <div style=\"margin: 20px 0; padding: 20px; background-color: #f9f9f9; border-radius: 5px;\">
                        <h3>Detalhes do Pagamento:</h3>
                        <p><strong>Produto:</strong> {product_name}</p>
                        <p><strong>Valor:</strong> R$ {product_price}</p>
                        <p><strong>ID do Pagamento:</strong> {payment_id}</p>
                        <p><strong>Data:</strong> {payment_date}</p>
                    </div>
                    <div style=\"text-align: center; margin: 30px 0;\">
                        <a href=\"{access_url}\" style=\"background-color: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;\">
                            Acessar Produto
                        </a>
                    </div>
                    <p>Se você tiver alguma dúvida, não hesite em nos contatar.</p>
                    <hr style=\"border: 1px solid #eee; margin: 20px 0;\">
                    <p style=\"color: #666; font-size: 12px;\">
                        Este é um email automático, por favor não responda.
                    </p>
                </div>',
                '[\"customer_name\", \"product_name\", \"product_price\", \"payment_id\", \"payment_date\", \"access_url\"]',
                1
            )
        ");
        $stmt->execute();
        echo "Template de confirmação de pagamento inserido com sucesso!\n";
    } else {
        echo "Template de confirmação de pagamento já existe.\n";
    }
} catch (PDOException $e) {
    die("Erro ao atualizar templates de email: " . $e->getMessage() . "\n");
}
