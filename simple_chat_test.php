<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples do Chat Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #101010; color: #fff; }
        .card { background: #2a2a2a; border: 1px solid #444; }
        .card-header { background: #333; border-bottom: 1px solid #444; }
        .conversation-item { cursor: pointer; }
        .conversation-item:hover { background: #444; }
        .conversation-item.active { background: #007bff; }
        #logs { background: #000; color: #0f0; font-family: monospace; font-size: 12px; height: 200px; overflow-y: auto; padding: 10px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Conversas Disponíveis</h5>
                    </div>
                    <div class="card-body" id="conversations">
                        <p>Carregando...</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 id="chat-title">Selecione uma conversa</h5>
                    </div>
                    <div class="card-body">
                        <div id="messages" style="height: 200px; overflow-y: auto; border: 1px solid #444; padding: 10px; margin-bottom: 10px;">
                            <!-- Mensagens aparecerão aqui -->
                        </div>
                        <div class="input-group">
                            <input type="text" id="message-input" class="form-control" placeholder="Digite sua mensagem..." disabled>
                            <button id="send-btn" class="btn btn-primary" disabled>Enviar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Logs de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="logs"></div>
                        <button onclick="clearLogs()" class="btn btn-sm btn-secondary">Limpar Logs</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUserId = null;
        let logContainer = document.getElementById('logs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#0ff',
                'success': '#0f0',
                'error': '#f00',
                'warning': '#ff0'
            };
            
            logContainer.innerHTML += `<div style="color: ${colors[type] || '#0ff'}">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            logContainer.innerHTML = '';
        }
        
        // Carregar conversas
        function loadConversations() {
            addLog('Carregando conversas...', 'info');
            
            fetch('admin/chat.php?action=get_conversations')
                .then(response => {
                    addLog(`Response status: ${response.status}`, 'info');
                    return response.json();
                })
                .then(data => {
                    addLog(`Conversas recebidas: ${JSON.stringify(data)}`, 'success');
                    
                    if (Array.isArray(data) && data.length > 0) {
                        const conversationsHtml = data.map(user => `
                            <div class="conversation-item p-2 mb-2 border rounded" 
                                 data-id="${user.id}" 
                                 data-name="${user.name}"
                                 onclick="selectConversation(${user.id}, '${user.name}')">
                                <strong>${user.name}</strong><br>
                                <small>${user.email}</small><br>
                                <small>Última atividade: ${user.last_activity}</small>
                            </div>
                        `).join('');
                        
                        document.getElementById('conversations').innerHTML = conversationsHtml;
                    } else {
                        document.getElementById('conversations').innerHTML = '<p>Nenhuma conversa encontrada</p>';
                    }
                })
                .catch(error => {
                    addLog(`Erro ao carregar conversas: ${error.message}`, 'error');
                    document.getElementById('conversations').innerHTML = '<p style="color: red;">Erro ao carregar conversas</p>';
                });
        }
        
        // Selecionar conversa
        function selectConversation(userId, userName) {
            addLog(`Selecionando conversa: ${userName} (ID: ${userId})`, 'info');
            
            currentUserId = userId;
            document.getElementById('chat-title').textContent = `Chat com ${userName}`;
            document.getElementById('message-input').disabled = false;
            document.getElementById('send-btn').disabled = false;
            
            // Atualizar visual
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-id="${userId}"]`).classList.add('active');
            
            addLog(`currentUserId definido como: ${currentUserId} (tipo: ${typeof currentUserId})`, 'success');
            
            loadMessages(userId);
        }
        
        // Carregar mensagens
        function loadMessages(userId) {
            addLog(`Carregando mensagens para usuário ${userId}`, 'info');
            
            fetch(`admin/chat.php?action=get_messages&user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    addLog(`Mensagens recebidas: ${data.length} mensagens`, 'success');
                    
                    const messagesHtml = data.map(msg => `
                        <div class="mb-2 p-2 ${msg.is_admin ? 'text-end' : 'text-start'}">
                            <div class="badge ${msg.is_admin ? 'bg-primary' : 'bg-secondary'}">
                                ${msg.is_admin ? 'Admin' : msg.sender_name}: ${msg.message}
                            </div>
                            <br><small>${new Date(msg.timestamp * 1000).toLocaleString()}</small>
                        </div>
                    `).join('');
                    
                    document.getElementById('messages').innerHTML = messagesHtml;
                })
                .catch(error => {
                    addLog(`Erro ao carregar mensagens: ${error.message}`, 'error');
                });
        }
        
        // Enviar mensagem
        document.getElementById('send-btn').addEventListener('click', function() {
            const message = document.getElementById('message-input').value.trim();
            
            addLog(`Tentando enviar mensagem: "${message}"`, 'info');
            addLog(`currentUserId: ${currentUserId} (tipo: ${typeof currentUserId})`, 'info');
            
            if (!message) {
                addLog('Mensagem vazia', 'warning');
                return;
            }
            
            if (!currentUserId || currentUserId == 0) {
                addLog('currentUserId inválido', 'error');
                alert('Selecione uma conversa primeiro');
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'send_message');
            formData.append('user_id', currentUserId);
            formData.append('message', message);
            
            addLog(`Enviando dados: action=send_message, user_id=${currentUserId}, message="${message}"`, 'info');
            
            fetch('admin/chat.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                addLog(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                return response.text();
            })
            .then(data => {
                addLog(`Response data: ${data}`, 'info');
                
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success) {
                        addLog('Mensagem enviada com sucesso!', 'success');
                        document.getElementById('message-input').value = '';
                        loadMessages(currentUserId);
                    } else {
                        addLog(`Erro do servidor: ${jsonData.error}`, 'error');
                    }
                } catch (e) {
                    addLog(`Erro ao parsear JSON: ${e.message}`, 'error');
                    addLog(`Raw response: ${data}`, 'error');
                }
            })
            .catch(error => {
                addLog(`Erro na requisição: ${error.message}`, 'error');
            });
        });
        
        // Enter para enviar
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('send-btn').click();
            }
        });
        
        // Carregar conversas ao iniciar
        loadConversations();
    </script>
</body>
</html>
