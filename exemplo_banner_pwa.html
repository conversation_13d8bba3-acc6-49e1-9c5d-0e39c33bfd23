<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemplo: Banner PWA Compacto - KESUNG SITE</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #4318FF;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4318FF;
        }
        
        .mockup {
            position: relative;
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
        }
        
        /* Banner PWA Compacto - Exemplo */
        .pwa-install-banner-compact {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #4318FF, #9333EA);
            color: white;
            z-index: 9999;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(67, 24, 255, 0.3);
            animation: slideInRight 0.4s ease-out;
            max-width: 280px;
            width: auto;
        }

        .pwa-banner-content-compact {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            gap: 12px;
        }

        .pwa-banner-info {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .pwa-icon {
            font-size: 18px;
            color: white;
            flex-shrink: 0;
        }

        .pwa-text {
            font-size: 14px;
            font-weight: 500;
            color: white;
            white-space: nowrap;
        }

        .pwa-banner-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pwa-install-btn-compact {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .pwa-install-btn-compact:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-1px);
        }

        .pwa-close-btn-compact {
            background: none;
            border: none;
            color: white;
            font-size: 14px;
            cursor: pointer;
            padding: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            opacity: 0.8;
        }

        .pwa-close-btn-compact:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            opacity: 1;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Responsividade Mobile */
        @media (max-width: 768px) {
            .pwa-install-banner-compact {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
                border-radius: 10px;
            }

            .pwa-banner-content-compact {
                padding: 10px 12px;
                gap: 10px;
            }

            .pwa-text {
                font-size: 13px;
            }

            .pwa-install-btn-compact {
                font-size: 11px;
                padding: 5px 10px;
            }

            .pwa-icon {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .pwa-install-banner-compact {
                top: 5px;
                right: 5px;
                left: 5px;
            }

            .pwa-banner-content-compact {
                padding: 8px 10px;
                gap: 8px;
            }

            .pwa-text {
                font-size: 12px;
            }

            .pwa-install-btn-compact {
                font-size: 10px;
                padding: 4px 8px;
            }

            .pwa-icon {
                font-size: 14px;
            }

            .pwa-close-btn-compact {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fca5a5;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #86efac;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Banner PWA Compacto - KESUNG SITE</h1>
        
        <div class="highlight">
            <strong>🎯 Objetivo:</strong> Banner menor, responsivo e posicionado no canto superior direito para instalação do app.
        </div>

        <h2>📋 Características do Novo Banner</h2>
        
        <div class="demo-section">
            <h3>✅ Melhorias Implementadas:</h3>
            <ul>
                <li><strong>Tamanho Compacto:</strong> Apenas ícone + texto + botão</li>
                <li><strong>Posição Flutuante:</strong> Canto superior direito</li>
                <li><strong>Responsivo:</strong> Adapta-se a todos os tamanhos de tela</li>
                <li><strong>Não Invasivo:</strong> Não empurra o conteúdo da página</li>
                <li><strong>Animação Suave:</strong> Desliza da direita para esquerda</li>
            </ul>
        </div>

        <h2>📱 Demonstração Visual</h2>
        
        <div class="mockup">
            <div style="color: #666; text-align: center; padding: 40px 0;">
                <p>Conteúdo da página aqui...</p>
                <p>O banner aparece no canto superior direito</p>
            </div>
            
            <!-- Banner PWA Compacto -->
            <div class="pwa-install-banner-compact">
                <div class="pwa-banner-content-compact">
                    <div class="pwa-banner-info">
                        <i class="fas fa-mobile-alt pwa-icon"></i>
                        <span class="pwa-text">Instale nosso app</span>
                    </div>
                    <div class="pwa-banner-actions">
                        <button class="pwa-install-btn-compact">
                            <i class="fas fa-download"></i>
                            Instalar
                        </button>
                        <button class="pwa-close-btn-compact">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 Comparação: Antes vs Depois</h2>
        
        <div class="comparison">
            <div class="before">
                <h4>❌ Banner Anterior</h4>
                <ul style="text-align: left;">
                    <li>Ocupava toda a largura</li>
                    <li>Empurrava conteúdo para baixo</li>
                    <li>Muito texto</li>
                    <li>Botão "Agora não"</li>
                    <li>Menos responsivo</li>
                </ul>
            </div>
            <div class="after">
                <h4>✅ Banner Novo</h4>
                <ul style="text-align: left;">
                    <li>Compacto no canto</li>
                    <li>Não interfere no layout</li>
                    <li>Texto mínimo</li>
                    <li>Apenas botão instalar</li>
                    <li>100% responsivo</li>
                </ul>
            </div>
        </div>

        <h2>📱 Responsividade</h2>
        
        <div class="demo-section">
            <h3>🖥️ Desktop (> 768px):</h3>
            <ul>
                <li>Banner no canto superior direito</li>
                <li>Largura máxima: 280px</li>
                <li>Texto completo: "Instale nosso app"</li>
            </ul>
            
            <h3>📱 Tablet (≤ 768px):</h3>
            <ul>
                <li>Banner ocupa largura total (com margens)</li>
                <li>Texto reduzido</li>
                <li>Botões menores</li>
            </ul>
            
            <h3>📱 Mobile (≤ 480px):</h3>
            <ul>
                <li>Banner ainda mais compacto</li>
                <li>Ícones e textos menores</li>
                <li>Margens reduzidas</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Resultado Final</h3>
            <p><strong>Banner PWA otimizado:</strong></p>
            <ul>
                <li>🎯 <strong>Compacto:</strong> Apenas elementos essenciais</li>
                <li>📱 <strong>Responsivo:</strong> Funciona em todos os dispositivos</li>
                <li>🎨 <strong>Não invasivo:</strong> Não atrapalha a navegação</li>
                <li>⚡ <strong>Rápido:</strong> Animação suave de entrada</li>
                <li>🔧 <strong>Funcional:</strong> Ícone celular + texto + botão instalar</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.location.href='/'" style="background: #4318FF; color: white; padding: 12px 24px; border: none; border-radius: 8px; font-size: 14px; cursor: pointer;">
                Ver Banner em Ação no Site
            </button>
        </div>
    </div>
</body>
</html>
