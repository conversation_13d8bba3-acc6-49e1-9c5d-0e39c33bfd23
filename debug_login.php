<?php
require_once 'admin/database/connection.php';

echo "<h2>🔍 Debug do Sistema de Login</h2>";

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h3>1. Testando conexão com banco...</h3>";
    echo "<p>✅ Conexão estabelecida com sucesso</p>";
    
    echo "<h3>2. Verificando tabela admins...</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM admins");
    $result = $stmt->fetch();
    echo "<p>Total de admins: {$result['total']}</p>";
    
    echo "<h3>3. Listando todos os admins...</h3>";
    $stmt = $pdo->query("SELECT id, name, email, LEFT(password, 20) as password_preview FROM admins");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($admins)) {
        echo "<p>❌ Nenhum admin encontrado!</p>";
        
        // Criar admin automaticamente
        echo "<h3>4. Criando admin automaticamente...</h3>";
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (name, email, password) VALUES (?, ?, ?)");
        $stmt->execute(['Admin', '<EMAIL>', $password]);
        echo "<p>✅ Admin criado: <EMAIL> / admin123</p>";
        
        // Listar novamente
        $stmt = $pdo->query("SELECT id, name, email, LEFT(password, 20) as password_preview FROM admins");
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Password (preview)</th></tr>";
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>{$admin['id']}</td>";
        echo "<td>{$admin['name']}</td>";
        echo "<td>{$admin['email']}</td>";
        echo "<td>{$admin['password_preview']}...</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>4. Testando login com credenciais...</h3>";
    
    $test_email = '<EMAIL>';
    $test_password = 'admin123';
    
    echo "<p>Testando: {$test_email} / {$test_password}</p>";
    
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ? LIMIT 1");
    $stmt->execute([$test_email]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p>✅ Admin encontrado no banco</p>";
        echo "<p>ID: {$admin['id']}</p>";
        echo "<p>Nome: {$admin['name']}</p>";
        echo "<p>Email: {$admin['email']}</p>";
        
        if (password_verify($test_password, $admin['password'])) {
            echo "<p>✅ Senha verificada com sucesso!</p>";
        } else {
            echo "<p>❌ Falha na verificação da senha</p>";
            echo "<p>Hash armazenado: " . substr($admin['password'], 0, 50) . "...</p>";
            
            // Tentar recriar a senha
            echo "<h4>Recriando senha...</h4>";
            $new_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE id = ?");
            $stmt->execute([$new_password, $admin['id']]);
            echo "<p>✅ Senha atualizada</p>";
            
            // Testar novamente
            $stmt = $pdo->prepare("SELECT password FROM admins WHERE id = ?");
            $stmt->execute([$admin['id']]);
            $updated_admin = $stmt->fetch();
            
            if (password_verify($test_password, $updated_admin['password'])) {
                echo "<p>✅ Nova senha funciona!</p>";
            } else {
                echo "<p>❌ Ainda há problema com a senha</p>";
            }
        }
    } else {
        echo "<p>❌ Admin não encontrado no banco</p>";
    }
    
    echo "<h3>5. Simulando processo de login...</h3>";
    
    // Simular o processo exato do login.php
    $email = '<EMAIL>';
    $password = 'admin123';
    
    if (empty($email) || empty($password)) {
        echo "<p>❌ Campos vazios</p>";
    } else {
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ? LIMIT 1");
        $stmt->execute([$email]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin && password_verify($password, $admin['password'])) {
            echo "<p>✅ Login seria bem-sucedido!</p>";
            echo "<p>Redirecionaria para dashboard.php</p>";
        } else {
            echo "<p>❌ Login falharia</p>";
            if (!$admin) {
                echo "<p>Motivo: Admin não encontrado</p>";
            } else {
                echo "<p>Motivo: Senha incorreta</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
}
?>

<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h4>🔑 Credenciais para Teste:</h4>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>Senha:</strong> admin123</p>
    <p><a href="admin/login.php" target="_blank">🚀 Testar Login Agora</a></p>
</div>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
table {
    background: white;
    margin: 10px 0;
}
th, td {
    padding: 8px 12px;
    text-align: left;
}
th {
    background: #f8f9fa;
}
</style>
