<?php
session_start();
require_once '../../database/connection.php';

if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();

    // Buscar todas as conversas únicas
    $stmt = $pdo->prepare("
        SELECT DISTINCT 
            c.id,
            c.name,
            c.email,
            (
                SELECT message 
                FROM chat_messages 
                WHERE (sender_id = c.id OR receiver_id = c.id)
                ORDER BY created_at DESC 
                LIMIT 1
            ) as last_message,
            (
                SELECT created_at 
                FROM chat_messages 
                WHERE (sender_id = c.id OR receiver_id = c.id)
                ORDER BY created_at DESC 
                LIMIT 1
            ) as last_message_time
        FROM customers c
        INNER JOIN chat_messages cm 
        ON c.id = cm.sender_id OR c.id = cm.receiver_id
        GROUP BY c.id
        ORDER BY last_message_time DESC
    ");
    
    $stmt->execute();
    $conversations = $stmt->fetchAll();

    echo json_encode($conversations);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar conversas']);
}
