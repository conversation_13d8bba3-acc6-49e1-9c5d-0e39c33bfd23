function showSuccessAlert(message) {
    Swal.fire({
        title: message,
        icon: "success",
        confirmButtonColor: '#0d6efd'
    });
}

function showErrorAlert(message) {
    Swal.fire({
        title: message,
        icon: "error",
        confirmButtonColor: '#0d6efd'
    });
}

function showConfirmDialog(message, callback) {
    Swal.fire({
        title: message,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#0d6efd',
        cancelButtonColor: '#6c757d'
    }).then((result) => {
        if (result.isConfirmed && callback) {
            callback();
        }
    });
}
