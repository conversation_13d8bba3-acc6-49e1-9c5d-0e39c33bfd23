/**
 * Sistema de Sons para Notificações
 * Gera sons usando Web Audio API
 */

class NotificationSound {
    constructor() {
        this.audioContext = null;
        this.init();
    }

    init() {
        try {
            // Criar contexto de áudio
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API não suportada:', error);
        }
    }

    // Som de notificação padrão
    playNotification() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Configurar som
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime + 0.1);
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + 0.2);

        // Envelope de volume
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.3);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.3);
    }

    // Som de promoção (mais animado)
    playPromotion() {
        if (!this.audioContext) return;

        const oscillator1 = this.audioContext.createOscillator();
        const oscillator2 = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator1.connect(gainNode);
        oscillator2.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Configurar sons
        oscillator1.frequency.setValueAtTime(523, this.audioContext.currentTime); // C5
        oscillator1.frequency.setValueAtTime(659, this.audioContext.currentTime + 0.1); // E5
        oscillator1.frequency.setValueAtTime(784, this.audioContext.currentTime + 0.2); // G5

        oscillator2.frequency.setValueAtTime(262, this.audioContext.currentTime); // C4
        oscillator2.frequency.setValueAtTime(330, this.audioContext.currentTime + 0.1); // E4
        oscillator2.frequency.setValueAtTime(392, this.audioContext.currentTime + 0.2); // G4

        // Envelope de volume
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.2, this.audioContext.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.4);

        oscillator1.start(this.audioContext.currentTime);
        oscillator1.stop(this.audioContext.currentTime + 0.4);
        oscillator2.start(this.audioContext.currentTime);
        oscillator2.stop(this.audioContext.currentTime + 0.4);
    }

    // Som de chat (suave)
    playChat() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Som suave para chat
        oscillator.frequency.setValueAtTime(440, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(550, this.audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.15, this.audioContext.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.2);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }

    // Som de alerta (urgente)
    playAlert() {
        if (!this.audioContext) return;

        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.audioContext.destination);

                oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
                
                gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.02);
                gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.1);

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.1);
            }, i * 150);
        }
    }

    // Som de bolão (esportivo)
    playBolao() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Som esportivo - como apito
        oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + 0.15);
        oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime + 0.3);

        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.25, this.audioContext.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.15);
        gainNode.gain.linearRampToValueAtTime(0.25, this.audioContext.currentTime + 0.25);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.4);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.4);
    }

    // Som de carrinho abandonado (lembrete suave)
    playCarrinho() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Som de lembrete suave
        oscillator.frequency.setValueAtTime(350, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(450, this.audioContext.currentTime + 0.2);
        oscillator.frequency.setValueAtTime(350, this.audioContext.currentTime + 0.4);

        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.1);
        gainNode.gain.linearRampToValueAtTime(0.05, this.audioContext.currentTime + 0.3);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.5);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }

    // Tocar som baseado no tipo de notificação
    playByType(type) {
        switch (type) {
            case 'promotion':
            case 'promocao':
                this.playPromotion();
                break;
            case 'chat':
            case 'message':
                this.playChat();
                break;
            case 'alert':
            case 'urgent':
                this.playAlert();
                break;
            case 'bolao':
            case 'sports':
                this.playBolao();
                break;
            case 'carrinho':
            case 'cart':
                this.playCarrinho();
                break;
            default:
                this.playNotification();
        }
    }
}

// Criar instância global
const notificationSound = new NotificationSound();

// Exportar para uso global
window.notificationSound = notificationSound;

// Função para tocar som de notificação
window.playNotificationSound = function(type = 'default') {
    notificationSound.playByType(type);
};

// Integração com Service Worker
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', event => {
        if (event.data && event.data.type === 'PLAY_NOTIFICATION_SOUND') {
            notificationSound.playByType(event.data.notificationType || 'default');
        }
        // Compatibilidade com versão anterior
        if (event.data && event.data.type === 'PLAY_SOUND') {
            notificationSound.playByType(event.data.soundType || 'default');
        }
    });
}

// Auto-inicializar contexto de áudio no primeiro clique do usuário
document.addEventListener('click', function initAudio() {
    if (notificationSound.audioContext && notificationSound.audioContext.state === 'suspended') {
        notificationSound.audioContext.resume();
    }
    document.removeEventListener('click', initAudio);
}, { once: true });

console.log('Sistema de sons de notificação carregado');
