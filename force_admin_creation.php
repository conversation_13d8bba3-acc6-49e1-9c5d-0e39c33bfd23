<?php
require_once 'admin/database/connection.php';

echo "<h2>🚀 Forçando Criação do Admin</h2>";

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h3>1. Limpando admins existentes...</h3>";
    $pdo->exec("DELETE FROM admins");
    echo "<p>✅ Todos os admins removidos</p>";
    
    echo "<h3>2. Criando novo admin...</h3>";
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO admins (name, email, password, role) VALUES (?, ?, ?, ?)");
    $result = $stmt->execute(['Administrador', '<EMAIL>', $password, 'admin']);
    
    if ($result) {
        echo "<p>✅ Admin criado com sucesso!</p>";
        $adminId = $pdo->lastInsertId();
        echo "<p>ID do admin: $adminId</p>";
    } else {
        echo "<p>❌ Falha ao criar admin</p>";
        print_r($stmt->errorInfo());
    }
    
    echo "<h3>3. Verificando admin criado...</h3>";
    $stmt = $pdo->query("SELECT * FROM admins");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($admins as $admin) {
        echo "<p>ID: {$admin['id']}, Nome: {$admin['name']}, Email: {$admin['email']}</p>";
    }
    
    echo "<h3>4. Testando login programaticamente...</h3>";
    
    // Simular login
    session_start();
    
    $email = '<EMAIL>';
    $password_test = 'admin123';
    
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ? LIMIT 1");
    $stmt->execute([$email]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify($password_test, $admin['password'])) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_name'] = $admin['name'];
        $_SESSION['admin_email'] = $admin['email'];
        
        echo "<p>✅ Login programático bem-sucedido!</p>";
        echo "<p>Sessão criada com sucesso</p>";
        
        // Verificar se pode acessar o dashboard
        echo "<h3>5. Testando acesso ao dashboard...</h3>";
        echo "<p><a href='admin/dashboard.php' target='_blank'>🚀 Acessar Dashboard Diretamente</a></p>";
        
    } else {
        echo "<p>❌ Falha no login programático</p>";
        if (!$admin) {
            echo "<p>Admin não encontrado</p>";
        } else {
            echo "<p>Senha não confere</p>";
            echo "<p>Hash: " . substr($admin['password'], 0, 30) . "...</p>";
        }
    }
    
    echo "<h3>6. Informações de Debug...</h3>";
    echo "<p>PHP Version: " . phpversion() . "</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Session Status: " . session_status() . "</p>";
    
    if (isset($_SESSION['admin_logged_in'])) {
        echo "<p>✅ Sessão admin ativa</p>";
        echo "<p>Admin ID: " . $_SESSION['admin_id'] . "</p>";
        echo "<p>Admin Nome: " . $_SESSION['admin_name'] . "</p>";
    } else {
        echo "<p>❌ Sessão admin não encontrada</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<div style="background: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h4>🔑 Credenciais Definitivas:</h4>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>Senha:</strong> admin123</p>
    
    <h4>🚀 Links de Teste:</h4>
    <p><a href="admin/login.php" target="_blank">1. Página de Login</a></p>
    <p><a href="admin/dashboard.php" target="_blank">2. Dashboard (se logado)</a></p>
</div>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
</style>
