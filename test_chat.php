<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Sistema de Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-dark text-white">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card bg-secondary">
                    <div class="card-header">
                        <h3><i class="fas fa-bug"></i> Teste do Sistema de Chat</h3>
                    </div>
                    <div class="card-body">
                        <div id="test-results"></div>
                        
                        <div class="mt-4">
                            <h5>Teste Manual de Envio</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>User ID:</label>
                                    <input type="number" id="test-user-id" class="form-control" value="1">
                                </div>
                                <div class="col-md-6">
                                    <label>Mensagem:</label>
                                    <input type="text" id="test-message" class="form-control" value="Teste de mensagem">
                                </div>
                            </div>
                            <button id="send-test" class="btn btn-primary mt-2">
                                <i class="fas fa-paper-plane"></i> Enviar Teste
                            </button>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Logs do Sistema</h5>
                            <div id="logs" class="bg-dark p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                                <!-- Logs aparecerão aqui -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logContainer = $('#logs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-info',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-white';
            
            logContainer.append(`<div class="${colorClass}">[${timestamp}] ${message}</div>`);
            logContainer.scrollTop(logContainer[0].scrollHeight);
        }
        
        function runTests() {
            addLog('Iniciando testes do sistema de chat...', 'info');
            
            // Teste 1: Verificar se as APIs existem
            addLog('Teste 1: Verificando APIs...', 'info');
            
            $.get('api/send_message.php')
                .done(function() {
                    addLog('✗ API send_message.php respondeu (deveria dar erro de método)', 'warning');
                })
                .fail(function(xhr) {
                    if (xhr.status === 400) {
                        addLog('✓ API send_message.php existe e valida método', 'success');
                    } else {
                        addLog('✗ API send_message.php erro: ' + xhr.status, 'error');
                    }
                });
            
            $.get('api/get_messages.php')
                .done(function() {
                    addLog('✗ API get_messages.php respondeu sem parâmetros', 'warning');
                })
                .fail(function(xhr) {
                    if (xhr.status === 400) {
                        addLog('✓ API get_messages.php existe e valida parâmetros', 'success');
                    } else {
                        addLog('✗ API get_messages.php erro: ' + xhr.status, 'error');
                    }
                });
            
            // Teste 2: Verificar banco de dados
            addLog('Teste 2: Verificando banco de dados...', 'info');
            
            $.get('check_chat_tables.php')
                .done(function(data) {
                    addLog('✓ Verificação do banco executada', 'success');
                })
                .fail(function() {
                    addLog('✗ Erro ao verificar banco de dados', 'error');
                });
        }
        
        $('#send-test').click(function() {
            const userId = $('#test-user-id').val();
            const message = $('#test-message').val();
            
            addLog(`Enviando mensagem de teste: "${message}" para usuário ${userId}`, 'info');
            
            $.ajax({
                url: 'api/send_message.php',
                method: 'POST',
                data: {
                    user_id: userId,
                    message: message
                },
                dataType: 'json',
                success: function(response) {
                    addLog('Resposta: ' + JSON.stringify(response), 'success');
                    if (response.success) {
                        addLog('✓ Mensagem enviada com sucesso!', 'success');
                    } else {
                        addLog('✗ Erro: ' + response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    addLog('✗ Erro AJAX: ' + error, 'error');
                    addLog('Status: ' + xhr.status, 'error');
                    addLog('Response: ' + xhr.responseText, 'error');
                }
            });
        });
        
        // Executar testes automaticamente
        $(document).ready(function() {
            runTests();
        });
    </script>
</body>
</html>
