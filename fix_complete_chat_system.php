<?php
/**
 * Script Completo para Correção do Sistema de Chat
 * Conecta ao banco remoto e corrige todas as tabelas e dados
 */

// Configuração do banco remoto
$host = 'localhost';
$dbname = 'u276254152_banco_loja';
$username = 'root';
$password = 'Vasia';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<h2>✅ Conectado ao banco: $dbname</h2>";
    
    // 1. Verificar e criar tabela de usuários
    echo "<h3>1. Verificando tabela de usuários...</h3>";
    
    $createUsersTable = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        balance DECIMAL(10,2) DEFAULT 0.00,
        is_admin TINYINT(1) DEFAULT 0,
        is_online TINYINT(1) DEFAULT 0,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createUsersTable);
    echo "✅ Tabela 'users' verificada/criada<br>";
    
    // 2. Verificar e criar tabela de mensagens
    echo "<h3>2. Verificando tabela de mensagens...</h3>";
    
    $createMessagesTable = "
    CREATE TABLE IF NOT EXISTS chat_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        message TEXT NOT NULL,
        is_admin TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createMessagesTable);
    echo "✅ Tabela 'chat_messages' verificada/criada<br>";
    
    // 3. Verificar e criar tabela de administradores
    echo "<h3>3. Verificando tabela de administradores...</h3>";
    
    $createAdminsTable = "
    CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'admin',
        is_active TINYINT(1) DEFAULT 1,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createAdminsTable);
    echo "✅ Tabela 'admins' verificada/criada<br>";
    
    // 4. Inserir usuários de teste
    echo "<h3>4. Inserindo usuários de teste...</h3>";
    
    $testUsers = [
        ['username' => 'paula_helena', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'casa_jordan', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test1', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test2', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)],
        ['username' => 'user_test3', 'email' => '<EMAIL>', 'password' => password_hash('123456', PASSWORD_DEFAULT)]
    ];
    
    foreach ($testUsers as $user) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, balance) VALUES (?, ?, ?, ?)");
            $stmt->execute([$user['username'], $user['email'], $user['password'], rand(100, 1000)]);
            echo "✅ Usuário '{$user['username']}' inserido<br>";
        } catch (Exception $e) {
            echo "⚠️ Usuário '{$user['username']}' já existe<br>";
        }
    }
    
    // 5. Inserir admin de teste
    echo "<h3>5. Inserindo admin de teste...</h3>";
    
    try {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT IGNORE INTO admins (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'super_admin']);
        echo "✅ Admin inserido (login: admin, senha: admin123)<br>";
    } catch (Exception $e) {
        echo "⚠️ Admin já existe<br>";
    }
    
    // 6. Inserir mensagens de teste
    echo "<h3>6. Inserindo mensagens de teste...</h3>";
    
    $users = $pdo->query("SELECT id, username FROM users LIMIT 5")->fetchAll();
    
    $testMessages = [
        'Olá, preciso de ajuda com meu pedido',
        'Quando será processado meu saque?',
        'Tenho uma dúvida sobre o sistema',
        'Obrigado pelo atendimento!',
        'Gostaria de saber sobre promoções'
    ];
    
    foreach ($users as $index => $user) {
        if (isset($testMessages[$index])) {
            try {
                $stmt = $pdo->prepare("INSERT INTO chat_messages (user_id, message, is_admin) VALUES (?, ?, 0)");
                $stmt->execute([$user['id'], $testMessages[$index]]);
                echo "✅ Mensagem inserida para {$user['username']}<br>";
                
                // Resposta do admin
                $adminResponse = "Olá {$user['username']}, como posso ajudá-lo?";
                $stmt = $pdo->prepare("INSERT INTO chat_messages (user_id, message, is_admin) VALUES (?, ?, 1)");
                $stmt->execute([$user['id'], $adminResponse]);
                echo "✅ Resposta do admin inserida<br>";
            } catch (Exception $e) {
                echo "⚠️ Erro ao inserir mensagem: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    // 7. Verificar dados inseridos
    echo "<h3>7. Verificação final...</h3>";
    
    $userCount = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $messageCount = $pdo->query("SELECT COUNT(*) as count FROM chat_messages")->fetch()['count'];
    $adminCount = $pdo->query("SELECT COUNT(*) as count FROM admins")->fetch()['count'];
    
    echo "📊 <strong>Estatísticas:</strong><br>";
    echo "👥 Usuários: $userCount<br>";
    echo "💬 Mensagens: $messageCount<br>";
    echo "👨‍💼 Admins: $adminCount<br>";
    
    // 8. Listar conversas ativas
    echo "<h3>8. Conversas ativas:</h3>";
    
    $conversations = $pdo->query("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.balance,
            u.is_online,
            u.last_activity,
            COUNT(cm.id) as message_count,
            MAX(cm.created_at) as last_message
        FROM users u
        LEFT JOIN chat_messages cm ON u.id = cm.user_id
        GROUP BY u.id
        ORDER BY last_message DESC
    ")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Saldo</th><th>Online</th><th>Mensagens</th><th>Última Msg</th></tr>";
    
    foreach ($conversations as $conv) {
        $online = $conv['is_online'] ? '🟢' : '🔴';
        $lastMsg = $conv['last_message'] ? date('d/m/Y H:i', strtotime($conv['last_message'])) : 'Nunca';
        
        echo "<tr>";
        echo "<td>{$conv['id']}</td>";
        echo "<td>{$conv['username']}</td>";
        echo "<td>{$conv['email']}</td>";
        echo "<td>R$ " . number_format($conv['balance'], 2, ',', '.') . "</td>";
        echo "<td>$online</td>";
        echo "<td>{$conv['message_count']}</td>";
        echo "<td>$lastMsg</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🎉 Sistema de chat corrigido com sucesso!</h2>";
    echo "<p><strong>Próximos passos:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Banco de dados configurado</li>";
    echo "<li>✅ Tabelas criadas</li>";
    echo "<li>✅ Dados de teste inseridos</li>";
    echo "<li>🔗 <a href='admin/chat.php'>Testar Chat Admin</a></li>";
    echo "<li>🔗 <a href='chat.php'>Testar Chat Público</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2>❌ Erro de conexão:</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "<p><strong>Verifique:</strong></p>";
    echo "<ul>";
    echo "<li>Host: $host</li>";
    echo "<li>Database: $dbname</li>";
    echo "<li>Username: $username</li>";
    echo "<li>Password: " . (empty($password) ? 'VAZIA' : 'DEFINIDA') . "</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<h2>❌ Erro geral:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção Completa do Sistema de Chat</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #101010; 
            color: #fff; 
            padding: 20px; 
        }
        h2, h3 { color: #4CAF50; }
        table { 
            background: #2a2a2a; 
            color: #fff; 
            margin: 10px 0; 
        }
        th { 
            background: #333; 
            padding: 8px; 
        }
        td { 
            padding: 6px; 
            border-bottom: 1px solid #444; 
        }
        a { 
            color: #4CAF50; 
            text-decoration: none; 
        }
        a:hover { 
            text-decoration: underline; 
        }
    </style>
</head>
<body>
</body>
</html>
