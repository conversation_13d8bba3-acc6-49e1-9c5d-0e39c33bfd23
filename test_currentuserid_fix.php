<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste da Correção do currentUserId</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #101010; color: #fff; }
        .card { background: #2a2a2a; border: 1px solid #444; }
        .card-header { background: #333; border-bottom: 1px solid #444; }
        .test-item { cursor: pointer; padding: 10px; margin: 5px 0; border: 1px solid #444; border-radius: 5px; }
        .test-item:hover { background: #444; }
        .test-item.selected { background: #007bff; }
        #logs { background: #000; color: #0f0; font-family: monospace; font-size: 12px; height: 300px; overflow-y: auto; padding: 10px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Usuários de Teste</h5>
                    </div>
                    <div class="card-body" id="test-users">
                        <div class="test-item" data-id="1" data-name="Usuário 1">
                            <strong>Usuário 1</strong><br>
                            <small>ID: 1</small>
                        </div>
                        <div class="test-item" data-id="2" data-name="Usuário 2">
                            <strong>Usuário 2</strong><br>
                            <small>ID: 2</small>
                        </div>
                        <div class="test-item" data-id="3" data-name="Usuário 3">
                            <strong>Usuário 3</strong><br>
                            <small>ID: 3</small>
                        </div>
                        <div class="test-item" data-id="0" data-name="Usuário Inválido">
                            <strong>Usuário Inválido (ID: 0)</strong><br>
                            <small>Este deve falhar</small>
                        </div>
                        <div class="test-item" data-id="abc" data-name="Usuário String">
                            <strong>Usuário String (ID: abc)</strong><br>
                            <small>Este deve falhar</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Teste de Envio</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Usuário Selecionado:</strong> <span id="selected-user">Nenhum</span><br>
                            <strong>currentUserId:</strong> <span id="current-user-id">null</span><br>
                            <strong>Tipo:</strong> <span id="user-id-type">undefined</span><br>
                            <strong>Válido:</strong> <span id="user-id-valid">false</span>
                        </div>
                        <div class="input-group mb-3">
                            <input type="text" id="test-message" class="form-control" placeholder="Digite uma mensagem de teste" value="Teste de mensagem">
                            <button id="send-test" class="btn btn-primary" disabled>Enviar Teste</button>
                        </div>
                        <div class="mb-3">
                            <button onclick="testValidation()" class="btn btn-warning btn-sm">Testar Validação</button>
                            <button onclick="clearLogs()" class="btn btn-secondary btn-sm">Limpar Logs</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Logs de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="logs"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUserId = null;
        let logContainer = document.getElementById('logs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#0ff',
                'success': '#0f0',
                'error': '#f00',
                'warning': '#ff0'
            };
            
            logContainer.innerHTML += `<div style="color: ${colors[type] || '#0ff'}">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            logContainer.innerHTML = '';
        }
        
        function updateDisplay() {
            document.getElementById('current-user-id').textContent = currentUserId;
            document.getElementById('user-id-type').textContent = typeof currentUserId;
            document.getElementById('user-id-valid').textContent = (currentUserId > 0 && !isNaN(currentUserId));
            
            const sendButton = document.getElementById('send-test');
            sendButton.disabled = !(currentUserId > 0 && !isNaN(currentUserId));
        }
        
        function selectUser(element) {
            // Remover seleção anterior
            document.querySelectorAll('.test-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Selecionar novo item
            element.classList.add('selected');
            
            // Simular o comportamento do chat admin
            const rawId = element.dataset.id;
            currentUserId = parseInt(rawId);
            
            addLog(`Usuário selecionado: ${element.dataset.name}`, 'info');
            addLog(`Raw ID: "${rawId}" (tipo: ${typeof rawId})`, 'info');
            addLog(`Parsed ID: ${currentUserId} (tipo: ${typeof currentUserId})`, 'info');
            addLog(`É válido: ${currentUserId > 0 && !isNaN(currentUserId)}`, currentUserId > 0 && !isNaN(currentUserId) ? 'success' : 'error');
            
            document.getElementById('selected-user').textContent = element.dataset.name;
            updateDisplay();
        }
        
        function testValidation() {
            const message = document.getElementById('test-message').value.trim();
            
            addLog('=== TESTE DE VALIDAÇÃO ===', 'warning');
            addLog(`Mensagem: "${message}"`, 'info');
            addLog(`currentUserId: ${currentUserId}`, 'info');
            addLog(`Tipo: ${typeof currentUserId}`, 'info');
            addLog(`!message: ${!message}`, 'info');
            addLog(`!currentUserId: ${!currentUserId}`, 'info');
            addLog(`currentUserId <= 0: ${currentUserId <= 0}`, 'info');
            addLog(`isNaN(currentUserId): ${isNaN(currentUserId)}`, 'info');
            
            const isValid = message && currentUserId && currentUserId > 0 && !isNaN(currentUserId);
            addLog(`Resultado final: ${isValid ? 'VÁLIDO' : 'INVÁLIDO'}`, isValid ? 'success' : 'error');
            
            if (!isValid) {
                const reasons = [];
                if (!message) reasons.push('mensagem vazia');
                if (!currentUserId) reasons.push('currentUserId falsy');
                if (currentUserId <= 0) reasons.push('currentUserId <= 0');
                if (isNaN(currentUserId)) reasons.push('currentUserId é NaN');
                
                addLog(`Motivos da invalidação: ${reasons.join(', ')}`, 'error');
            }
        }
        
        function sendTestMessage() {
            const message = document.getElementById('test-message').value.trim();
            
            addLog('=== ENVIANDO MENSAGEM DE TESTE ===', 'warning');
            
            // Validação (igual ao chat admin)
            if (!message || !currentUserId || currentUserId <= 0 || isNaN(currentUserId)) {
                addLog('Validação falhou - parâmetros inválidos', 'error');
                alert('Selecione um usuário válido e digite uma mensagem');
                return;
            }
            
            addLog('Validação passou - enviando mensagem...', 'success');
            
            const formData = new FormData();
            formData.append('action', 'send_message');
            formData.append('user_id', currentUserId);
            formData.append('message', message);
            
            addLog(`Dados enviados: user_id=${currentUserId}, message="${message}"`, 'info');
            
            fetch('admin/chat.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                addLog(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                return response.text();
            })
            .then(data => {
                addLog(`Response: ${data}`, 'info');
                
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success) {
                        addLog('✓ Mensagem enviada com sucesso!', 'success');
                    } else {
                        addLog(`✗ Erro: ${jsonData.error}`, 'error');
                    }
                } catch (e) {
                    addLog(`Erro ao parsear JSON: ${e.message}`, 'error');
                }
            })
            .catch(error => {
                addLog(`Erro na requisição: ${error.message}`, 'error');
            });
        }
        
        // Event listeners
        document.querySelectorAll('.test-item').forEach(item => {
            item.addEventListener('click', () => selectUser(item));
        });
        
        document.getElementById('send-test').addEventListener('click', sendTestMessage);
        
        // Inicialização
        addLog('Sistema de teste inicializado', 'success');
        updateDisplay();
    </script>
</body>
</html>
