<?php
require_once 'connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Ler e executar os arquivos SQL na ordem correta
    $sqlFiles = [
        'create_tables.sql',
        'admin_tables.sql',
        'system_settings.sql', 
        'email_tables.sql',
        'download_tokens.sql'
    ];

    foreach ($sqlFiles as $file) {
        $sql = file_get_contents(__DIR__ . '/' . $file);
        
        // Dividir o SQL em comandos individuais
        $commands = array_filter(
            array_map(
                'trim',
                explode(';', $sql)
            ),
            'strlen'
        );

        // Executar cada comando separadamente
        foreach ($commands as $command) {
            if (trim($command) !== '') {
                try {
                    $pdo->exec($command);
                } catch (PDOException $e) {
                    // Ignorar erros de "tabela já existe"
                    if (strpos($e->getMessage(), '1050') === false) {
                        throw $e;
                    }
                }
            }
        }
    }

    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'");
    if ($stmt->fetchColumn() == 0) {
        // Create admin user
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, 'admin')");
        $stmt->execute([
            'Administrador',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT)
        ]);
    }
    
    // Add some sample products if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("
            INSERT INTO products (name, description, price, stock, status) 
            VALUES 
            ('Curso Completo: Monte Seu Próprio Site', 'Aprenda a criar sites profissionais do zero', 297.00, 999, 'active'),
            ('Script PHP - Modelo China para Casa de Apostas', 'Script completo e otimizado para casa de apostas', 997.00, 10, 'active'),
            ('Curso Facebook Ads Venda Certa', 'Domine o Facebook Ads e aumente suas vendas', 497.00, 999, 'active')
        ");
        $stmt->execute();
    }
    
    echo "Todas as tabelas foram criadas com sucesso!<br>";
    echo "Usuário admin criado: <EMAIL> / admin123<br>";
    echo "Produtos de exemplo adicionados!<br>";
    echo "<a href='../index.php'>Voltar para o painel</a>";
    
} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage();
}
