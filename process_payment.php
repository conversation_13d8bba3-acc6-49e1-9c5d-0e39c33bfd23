<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Configurações de cabeçalho para API
header('Content-Type: application/json');

// Configurações de log
$logFile = 'payment_log.txt';

// Função para log
function logMessage($message) {
    global $logFile;
    $date = date('[d-M-Y H:i:s e]');
    error_log("$date $message" . PHP_EOL, 3, $logFile);
}

try {
    // Log the start of the process
    logMessage("Iniciando processamento de pagamento");
    
    // Incluir arquivos necessários
    logMessage("Carregando arquivos necessários");
    
    // Check if files exist before requiring them
    if (!file_exists('admin/database/connection.php')) {
        throw new Exception("Arquivo não encontrado: admin/database/connection.php");
    }
    require_once 'admin/database/connection.php';
    
    if (!file_exists('admin/config/asaas.php')) {
        throw new Exception("Arquivo não encontrado: admin/config/asaas.php");
    }
    require_once 'admin/config/asaas.php';
    
    // Obter dados do POST
    $postData = file_get_contents('php://input');
    logMessage("Dados recebidos: $postData");
    
    $data = json_decode($postData, true);
    if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Erro ao decodificar JSON: " . json_last_error_msg());
    }
    
    logMessage("Dados decodificados: " . print_r($data, true));
    
    // Validar dados recebidos
    if (!$data) {
        throw new Exception('Dados inválidos');
    }
    
    // Extrair dados
    $productId = isset($data['product_id']) ? $data['product_id'] : '';
    $customerName = isset($data['customer_name']) ? trim($data['customer_name']) : '';
    $customerEmail = isset($data['customer_email']) ? trim($data['customer_email']) : '';
    $customerWhatsapp = isset($data['customer_whatsapp']) ? trim($data['customer_whatsapp']) : '';
    
    // Verificar qual campo de CPF/CNPJ está sendo enviado
    $customerCpfCnpj = '';
    if (isset($data['customer_cpf_cnpj']) && !empty($data['customer_cpf_cnpj'])) {
        $customerCpfCnpj = trim($data['customer_cpf_cnpj']);
        logMessage("CPF/CNPJ encontrado no campo customer_cpf_cnpj: $customerCpfCnpj");
    } elseif (isset($data['customer_document']) && !empty($data['customer_document'])) {
        $customerCpfCnpj = trim($data['customer_document']);
        logMessage("CPF/CNPJ encontrado no campo customer_document: $customerCpfCnpj");
    } else {
        // Usar um CPF padrão para testes se não for fornecido
        $customerCpfCnpj = '12345678909';
        logMessage("CPF/CNPJ não fornecido. Usando valor padrão: $customerCpfCnpj");
    }
    
    // Limpar formatação do CPF/CNPJ
    $customerCpfCnpj = preg_replace('/[^0-9]/', '', $customerCpfCnpj);
    
    // Validar campos obrigatórios
    if (empty($productId)) {
        throw new Exception('ID do produto não fornecido');
    }
    
    if (empty($customerName)) {
        throw new Exception('Nome do cliente não fornecido');
    }
    
    if (empty($customerEmail)) {
        throw new Exception('Email do cliente não fornecido');
    }
    
    if (empty($customerWhatsapp)) {
        throw new Exception('WhatsApp do cliente não fornecido');
    }
    
    // Conectar ao banco de dados
    logMessage("Conectando ao banco de dados");
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Consultar produto
    logMessage("Consultando produto ID: $productId");
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        throw new Exception('Produto não encontrado');
    }
    
    logMessage("Produto encontrado: " . print_r($product, true));
    
    // Criar pedido no banco de dados
    logMessage("Criando pedido no banco de dados");
    $stmt = $pdo->prepare("INSERT INTO orders (product_id, customer_name, customer_email, customer_phone, customer_cpf_cnpj, amount, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())");
    $stmt->execute([
        $productId,
        $customerName,
        $customerEmail,
        $customerWhatsapp,
        $customerCpfCnpj,
        $product['price']
    ]);
    
    $orderId = $pdo->lastInsertId();
    logMessage("Pedido criado: ID=$orderId");
    
    // Referência externa para o Asaas
    $externalReference = "PIX_" . uniqid() . "_" . $productId;
    
    // Verificar se as constantes ASAAS_API_URL e ASAAS_API_KEY estão definidas
    if (!defined('ASAAS_API_URL') || !defined('ASAAS_API_KEY')) {
        throw new Exception('Configurações do Asaas não definidas');
    }
    
    logMessage("Usando API Asaas: " . ASAAS_API_URL);
    
    // Preparar dados para a API Asaas
    $asaasData = [
        'customer' => isset($data['customer_id']) ? $data['customer_id'] : null,
        'billingType' => 'PIX',
        'value' => $product['price'],
        'dueDate' => date('Y-m-d'),
        'description' => $product['name'],
        'externalReference' => $externalReference
    ];
    
    // Se não tiver customer_id, criar cliente
    if (!isset($data['customer_id'])) {
        // Criar cliente no Asaas
        logMessage("Criando cliente no Asaas");
        $customerData = [
            'name' => $customerName,
            'email' => $customerEmail,
            'phone' => preg_replace('/[^0-9]/', '', $customerWhatsapp),
            'cpfCnpj' => $customerCpfCnpj,
            'notificationDisabled' => false
        ];
        
        $ch = curl_init(ASAAS_API_URL . '/customers');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($customerData));
        
        // Verificar se a função getAsaasHeaders existe
        if (function_exists('getAsaasHeaders')) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
        } else {
            // Fallback para headers diretos
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'access_token: ' . ASAAS_API_KEY
            ]);
        }
        
        $customerResponse = curl_exec($ch);
        $customerHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('Erro cURL ao criar cliente: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        logMessage("Resposta da criação do cliente - Status: $customerHttpCode, Resposta: $customerResponse");
        
        $customerResponseData = json_decode($customerResponse, true);
        
        if ($customerHttpCode >= 400 || !isset($customerResponseData['id'])) {
            $errorMsg = isset($customerResponseData['errors']) ? json_encode($customerResponseData['errors']) : 'Erro desconhecido ao criar cliente';
            throw new Exception("Erro ao criar cliente: $errorMsg");
        }
        
        $asaasData['customer'] = $customerResponseData['id'];
    }
    
    logMessage("Dados do pagamento: " . print_r($asaasData, true));
    
    // Criar cobrança no Asaas
    logMessage("Criando cobrança no Asaas");
    $ch = curl_init(ASAAS_API_URL . '/payments');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($asaasData));
    
    // Verificar se a função getAsaasHeaders existe
    if (function_exists('getAsaasHeaders')) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
    } else {
        // Fallback para headers diretos
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'access_token: ' . ASAAS_API_KEY
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL ao criar cobrança: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    logMessage("Resposta da criação da cobrança - Status: $httpCode, Resposta: $response");
    
    $responseData = json_decode($response, true);
    
    if ($httpCode >= 400 || !isset($responseData['id'])) {
        $errorMsg = isset($responseData['errors']) ? json_encode($responseData['errors']) : 'Erro desconhecido';
        throw new Exception("Erro ao gerar pagamento: $errorMsg");
    }
    
    // Atualizar pedido com ID do pagamento
    logMessage("Atualizando pedido com ID do pagamento");
    $stmt = $pdo->prepare("UPDATE orders SET payment_id = ?, external_reference = ? WHERE id = ?");
    $stmt->execute([$responseData['id'], $externalReference, $orderId]);
    
    // Obter QR Code do PIX
    logMessage("Obtendo QR Code do PIX");
    $ch = curl_init(ASAAS_API_URL . "/payments/" . $responseData['id'] . "/pixQrCode");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // Verificar se a função getAsaasHeaders existe
    if (function_exists('getAsaasHeaders')) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, getAsaasHeaders());
    } else {
        // Fallback para headers diretos
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'access_token: ' . ASAAS_API_KEY
        ]);
    }
    
    $qrResponse = curl_exec($ch);
    $qrHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Erro cURL ao obter QR Code: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    logMessage("Resposta do QR Code - Status: $qrHttpCode, Resposta: $qrResponse");
    
    $qrData = json_decode($qrResponse, true);
    
    if ($qrHttpCode >= 400 || !isset($qrData['encodedImage'])) {
        throw new Exception("Erro ao gerar QR Code PIX: " . $qrResponse);
    }
    
    // Retornar dados para o cliente
    logMessage("Processamento concluído com sucesso");
    echo json_encode([
        'success' => true,
        'payment_id' => $responseData['id'],
        'amount' => number_format($product['price'], 2, ',', '.'),
        'qr_code' => $qrData['encodedImage'],
        'pix_code' => $qrData['payload']
    ]);
    
} catch (Exception $e) {
    logMessage("ERRO: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
    
    // Garantir que a resposta seja sempre um JSON válido
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>

