<?php
function showAlert($message, $type = 'error') {
    $icon = ($type === 'error') ? '×' : '✓';
    $title = ($type === 'error') ? 'Erro' : 'Sucesso';
    $iconClass = ($type === 'error') ? 'error-icon' : 'success-icon';
    
    echo '<div class="alert-modal">
            <div class="modal-content">
                <div class="' . $iconClass . '">' . $icon . '</div>
                <h3>' . $title . '</h3>
                <p>' . $message . '</p>
                <button onclick="this.parentElement.parentElement.style.display=\'none\'" class="ok-button">OK</button>
            </div>
        </div>
        <style>
            .alert-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1050;
            }
            .alert-modal .modal-content {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                width: 300px;
            }
            .error-icon, .success-icon {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 15px;
                font-size: 30px;
            }
            .error-icon {
                border: 2px solid #ff4444;
                color: #ff4444;
            }
            .success-icon {
                border: 2px solid #4CAF50;
                color: #4CAF50;
            }
            .alert-modal h3 {
                margin: 0 0 10px;
                color: #333;
                font-size: 20px;
            }
            .alert-modal p {
                margin: 0 0 20px;
                color: #666;
            }
            .ok-button {
                background: #6610f2;
                color: white;
                border: none;
                padding: 8px 30px;
                border-radius: 4px;
                cursor: pointer;
            }
            .ok-button:hover {
                background: #520dc2;
            }
        </style>';
}
?>
