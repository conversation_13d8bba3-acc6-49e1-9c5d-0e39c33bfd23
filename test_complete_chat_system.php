<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Completo do Sistema de Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background: #101010; color: #fff; }
        .card { background: #2a2a2a; border: 1px solid #444; }
        .card-header { background: #333; border-bottom: 1px solid #444; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-success { background: #1a4a1a; border-left: 4px solid #28a745; }
        .test-error { background: #4a1a1a; border-left: 4px solid #dc3545; }
        .test-warning { background: #4a4a1a; border-left: 4px solid #ffc107; }
        .code-block { background: #000; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .btn-test { margin: 5px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-vial"></i> Teste Completo do Sistema de Chat</h2>
                        <p class="mb-0">Verificação de todas as funcionalidades após correções</p>
                    </div>
                    <div class="card-body">
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5><i class="fas fa-database"></i> Testes de Banco de Dados</h5>
                                <button onclick="testDatabase()" class="btn btn-primary btn-test">
                                    <i class="fas fa-database"></i> Testar Conexão
                                </button>
                                <button onclick="testTables()" class="btn btn-info btn-test">
                                    <i class="fas fa-table"></i> Verificar Tabelas
                                </button>
                                <button onclick="testUsers()" class="btn btn-success btn-test">
                                    <i class="fas fa-users"></i> Listar Usuários
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-cogs"></i> Testes de API</h5>
                                <button onclick="testCreateUser()" class="btn btn-warning btn-test">
                                    <i class="fas fa-user-plus"></i> Criar Usuário
                                </button>
                                <button onclick="testSendMessage()" class="btn btn-danger btn-test">
                                    <i class="fas fa-paper-plane"></i> Enviar Mensagem
                                </button>
                                <button onclick="testGetMessages()" class="btn btn-secondary btn-test">
                                    <i class="fas fa-comments"></i> Buscar Mensagens
                                </button>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5><i class="fas fa-link"></i> Links de Teste</h5>
                                <a href="chat.php" class="btn btn-primary btn-test" target="_blank">
                                    <i class="fas fa-comment"></i> Chat Público
                                </a>
                                <a href="admin/chat.php" class="btn btn-success btn-test" target="_blank">
                                    <i class="fas fa-user-shield"></i> Chat Admin
                                </a>
                                <a href="admin/dashboard.php" class="btn btn-info btn-test" target="_blank">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a>
                                <button onclick="clearResults()" class="btn btn-dark btn-test">
                                    <i class="fas fa-trash"></i> Limpar Resultados
                                </button>
                            </div>
                        </div>
                        
                        <div id="test-results">
                            <h5><i class="fas fa-clipboard-list"></i> Resultados dos Testes</h5>
                            <div id="results-container">
                                <p class="text-muted">Clique nos botões acima para executar os testes</p>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testUserId = null;
        
        function addResult(message, type = 'info', details = null) {
            const container = document.getElementById('results-container');
            const timestamp = new Date().toLocaleTimeString();
            
            const icons = {
                'success': 'fas fa-check-circle text-success',
                'error': 'fas fa-times-circle text-danger',
                'warning': 'fas fa-exclamation-triangle text-warning',
                'info': 'fas fa-info-circle text-info'
            };
            
            const classes = {
                'success': 'test-success',
                'error': 'test-error',
                'warning': 'test-warning',
                'info': 'test-result'
            };
            
            let html = `
                <div class="${classes[type] || 'test-result'}">
                    <i class="${icons[type] || icons.info}"></i>
                    <strong>[${timestamp}]</strong> ${message}
            `;
            
            if (details) {
                html += `<div class="code-block mt-2">${JSON.stringify(details, null, 2)}</div>`;
            }
            
            html += '</div>';
            
            container.innerHTML += html;
            container.scrollTop = container.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results-container').innerHTML = '<p class="text-muted">Resultados limpos</p>';
        }
        
        async function testDatabase() {
            addResult('Testando conexão com banco de dados...', 'info');
            
            try {
                const response = await fetch('test_db_connection.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult('✅ Conexão com banco estabelecida', 'success', data);
                } else {
                    addResult('❌ Erro na conexão com banco', 'error', data);
                }
            } catch (error) {
                addResult('❌ Erro ao testar conexão', 'error', {error: error.message});
            }
        }
        
        async function testTables() {
            addResult('Verificando estrutura das tabelas...', 'info');
            
            try {
                const response = await fetch('check_chat_tables.php');
                const text = await response.text();
                
                if (text.includes('✅')) {
                    addResult('✅ Tabelas verificadas com sucesso', 'success');
                } else {
                    addResult('⚠️ Problemas encontrados nas tabelas', 'warning');
                }
            } catch (error) {
                addResult('❌ Erro ao verificar tabelas', 'error', {error: error.message});
            }
        }
        
        async function testUsers() {
            addResult('Listando usuários do sistema...', 'info');
            
            try {
                const response = await fetch('list_users.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ ${data.users.length} usuários encontrados`, 'success', data.users);
                } else {
                    addResult('❌ Erro ao listar usuários', 'error', data);
                }
            } catch (error) {
                addResult('❌ Erro ao buscar usuários', 'error', {error: error.message});
            }
        }
        
        async function testCreateUser() {
            addResult('Testando criação de usuário...', 'info');
            
            const testData = {
                name: 'Teste_' + Date.now(),
                email: 'teste_' + Date.now() + '@test.com',
                whatsapp: '11999999999'
            };
            
            try {
                const formData = new FormData();
                formData.append('name', testData.name);
                formData.append('email', testData.email);
                formData.append('whatsapp', testData.whatsapp);
                
                const response = await fetch('api/create_user.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success && data.user_id) {
                    testUserId = data.user_id;
                    addResult(`✅ Usuário criado com ID: ${testUserId}`, 'success', data);
                } else {
                    addResult('❌ Erro ao criar usuário', 'error', data);
                }
            } catch (error) {
                addResult('❌ Erro na requisição de criação', 'error', {error: error.message});
            }
        }
        
        async function testSendMessage() {
            if (!testUserId) {
                addResult('⚠️ Execute "Criar Usuário" primeiro', 'warning');
                return;
            }
            
            addResult('Testando envio de mensagem...', 'info');
            
            const testMessage = 'Mensagem de teste - ' + new Date().toLocaleString();
            
            try {
                const formData = new FormData();
                formData.append('user_id', testUserId);
                formData.append('message', testMessage);
                
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addResult('✅ Mensagem enviada com sucesso', 'success', data);
                } else {
                    addResult('❌ Erro ao enviar mensagem', 'error', data);
                }
            } catch (error) {
                addResult('❌ Erro na requisição de envio', 'error', {error: error.message});
            }
        }
        
        async function testGetMessages() {
            if (!testUserId) {
                addResult('⚠️ Execute "Criar Usuário" primeiro', 'warning');
                return;
            }
            
            addResult('Testando busca de mensagens...', 'info');
            
            try {
                const response = await fetch(`api/get_messages.php?user_id=${testUserId}&last_time=0`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ ${data.messages.length} mensagens encontradas`, 'success', data.messages);
                } else {
                    addResult('❌ Erro ao buscar mensagens', 'error', data);
                }
            } catch (error) {
                addResult('❌ Erro na requisição de busca', 'error', {error: error.message});
            }
        }
        
        // Inicialização
        addResult('Sistema de testes inicializado', 'info');
        addResult('Banco configurado: u276254152_banco_loja', 'info');
        addResult('Clique nos botões para executar os testes', 'info');
    </script>
</body>
</html>
