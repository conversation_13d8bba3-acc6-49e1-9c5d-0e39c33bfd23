<?php
header('Content-Type: application/json');

try {
    require_once 'database/connection.php';
    
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    // Buscar usuários
    $stmt = $pdo->query("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.balance,
            u.is_admin,
            u.is_online,
            u.last_activity,
            u.created_at,
            COUNT(cm.id) as message_count
        FROM users u
        LEFT JOIN chat_messages cm ON u.id = cm.user_id
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'users' => $users,
        'total' => count($users)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao listar usuários: ' . $e->getMessage()
    ]);
}
?>
