<?php
session_start();
require_once 'includes/auth_check.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Método inválido.";
    echo "<script>window.location.href = 'products.php';</script>";
    exit;
}

try {
    if (!isset($_POST['id'])) {
        $_SESSION['error'] = "ID do produto não especificado.";
        echo "<script>window.location.href = 'products.php';</script>";
        exit;
    }
    
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // First get the product details
    $stmt = $pdo->prepare("SELECT id, name, image FROM products WHERE id = ?");
    $stmt->execute([$_POST['id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        $_SESSION['error'] = "Produto não encontrado.";
        echo "<script>window.location.href = 'products.php';</script>";
        exit;
    }
    
    // Then check if product has any orders
    $stmt = $pdo->prepare("SELECT COUNT(*) as order_count FROM orders WHERE product_id = ?");
    $stmt->execute([$_POST['id']]);
    $result = $stmt->fetch();
    
    if ($result['order_count'] > 0) {
        $_SESSION['error'] = sprintf(
            '<strong>Não é possível excluir "%s"</strong><br>
            Este produto possui pedidos associados e não pode ser excluído.<br>
            <small class="text-muted">Sugestão: Você pode desativar o produto ao invés de excluí-lo.</small>',
            htmlspecialchars($product['name'])
        );
        echo "<script>window.location.href = 'products.php';</script>";
        exit;
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Delete product
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$_POST['id']]);
        
        // Delete product image if exists
        if ($product['image']) {
            $imagePath = '../uploads/products/' . $product['image'];
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
        
        $pdo->commit();
        $_SESSION['success'] = sprintf(
            '<strong>Produto excluído com sucesso!</strong><br>
            O produto "%s" foi removido permanentemente.',
            htmlspecialchars($product['name'])
        );
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    $_SESSION['error'] = sprintf(
        '<strong>Erro ao excluir o produto</strong><br>
        %s',
        $e->getMessage()
    );
}

echo "<script>window.location.href = 'products.php';</script>";
exit;
