<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE email_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_email VARCHAR(255) NOT NULL,
                customer_name VARCHAR(255),
                subject VARCHAR(255),
                message TEXT,
                status ENUM('success', 'error') DEFAULT 'error',
                error_message TEXT,
                payment_id VARCHAR(255),
                order_id INT,
                product_name VARCHAR(255),
                amount DECIMAL(10,2),
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);

        // Inserir dados de exemplo
        $sampleLogs = [
            ['<EMAIL>', '<PERSON>', 'Confirmação de Pagamento PIX', 'Seu pagamento foi confirmado com sucesso!', 'success', null, 'PIX_001', 1, 'Produto Digital Premium', 29.90],
            ['<EMAIL>', 'Maria Santos', 'Link de Download - Produto Adquirido', 'Acesse seu produto através do link...', 'success', null, 'PIX_002', 2, 'Curso Online', 49.90],
            ['<EMAIL>', 'Pedro Costa', 'Pagamento Aprovado', 'Parabéns! Seu pagamento foi aprovado.', 'success', null, 'PIX_003', 3, 'E-book Completo', 19.90],
            ['<EMAIL>', 'Ana Oliveira', 'Falha no Envio', 'Tentativa de envio de email...', 'error', 'SMTP Error: Could not connect to server', 'PIX_004', 4, 'Software Premium', 35.50],
            ['<EMAIL>', 'Carlos Ferreira', 'Produto Liberado', 'Seu produto foi liberado para download.', 'success', null, 'PIX_005', 5, 'Template Design', 89.90]
        ];

        $stmt = $pdo->prepare("
            INSERT INTO email_logs (customer_email, customer_name, subject, message, status, error_message, payment_id, order_id, product_name, amount, sent_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW() - INTERVAL FLOOR(RAND() * 24) HOUR)
        ");

        foreach ($sampleLogs as $log) {
            $stmt->execute($log);
        }
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

// Configuração de paginação
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    // Buscar estatísticas
    $totalSuccess = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE status = 'success'")->fetchColumn();
    $totalError = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE status != 'success'")->fetchColumn();
    $todayEmails = $pdo->query("SELECT COUNT(*) FROM email_logs WHERE DATE(sent_at) = CURDATE()")->fetchColumn();

    // Buscar os logs completos
    $stmt = $pdo->prepare("
        SELECT
            el.*
        FROM email_logs el
        ORDER BY el.sent_at DESC
        LIMIT ? OFFSET ?
    ");

    $stmt->execute([$limit, $offset]);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Buscar total de registros para paginação
    $total = $pdo->query("SELECT COUNT(*) FROM email_logs")->fetchColumn();
    $totalPages = ceil($total / $limit);

} catch (PDOException $e) {
    error_log("Erro ao buscar logs: " . $e->getMessage());
    $logs = [];
    $totalPages = 0;
    $totalSuccess = 0;
    $totalError = 0;
    $todayEmails = 0;
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div class="header-content">
            <h1 class="h3 mb-0 text-gray-800">Logs de Email</h1>
            <p class="mb-0 text-gray-600">Histórico completo de emails enviados pelo sistema</p>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Emails Enviados com Sucesso</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalSuccess; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Erros de Envio</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalError; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Emails Hoje</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $todayEmails; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Taxa de Sucesso</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                $total = $totalSuccess + $totalError;
                                echo $total > 0 ? round(($totalSuccess / $total) * 100) : 0;
                                ?>%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percent fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Histórico de Emails Enviados</h6>
            <a href="email_settings.php" class="btn btn-sm btn-primary">
                <i class="fas fa-cog fa-sm"></i> Configurações de Email
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Data/Hora</th>
                            <th>Cliente</th>
                            <th>Email</th>
                            <th>Assunto</th>
                            <th>Produto</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Erro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($log['sent_at'])); ?></td>
                                <td><?php echo htmlspecialchars($log['customer_name'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($log['customer_email']); ?></td>
                                <td><?php echo htmlspecialchars($log['subject'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($log['product_name'] ?? '-'); ?></td>
                                <td>
                                    <?php if ($log['amount']): ?>
                                        <strong>R$ <?php echo number_format($log['amount'], 2, ',', '.'); ?></strong>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['status'] === 'success'): ?>
                                        <span class="badge badge-success">
                                            <i class="fas fa-check-circle"></i> Sucesso
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle"></i> Erro
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($log['error_message'])): ?>
                                        <span class="text-danger" title="<?php echo htmlspecialchars($log['error_message']); ?>">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <?php echo strlen($log['error_message']) > 30 ? substr($log['error_message'], 0, 30) . '...' : $log['error_message']; ?>
                                        </span>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (empty($logs)): ?>
                            <tr>
                                <td colspan="8" class="text-center">Nenhum log encontrado</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($totalPages > 1): ?>
                <nav aria-label="Navegação de página" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page - 1); ?>" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo ($page + 1); ?>" aria-label="Próximo">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
