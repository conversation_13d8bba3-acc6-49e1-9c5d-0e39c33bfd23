<div class="bg-white" id="sidebar-wrapper">
    <style>
    .nav-sidebar .nav-item {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .nav-sidebar .nav-link {
        cursor: pointer;
    }

    .nav-sidebar .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .nav-sidebar .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2);
    }
    </style>
    <div class="sidebar-heading text-center py-4">
        <i class="fas fa-user-shield fa-2x text-primary"></i>
        <div class="mt-2">Admin</div>
    </div>
    <div class="list-group list-group-flush my-3">
        <a href="dashboard.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
        <a href="products.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : ''; ?>">
            <i class="fas fa-box"></i>
            Produtos
        </a>
        <a href="orders.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'orders.php' ? 'active' : ''; ?>">
            <i class="fas fa-shopping-cart"></i>
            Pedidos
        </a>
        <a href="customers.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : ''; ?>">
            <i class="fas fa-users"></i>
            Clientes
        </a>
        <a href="reports.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>">
            <i class="fas fa-chart-bar"></i>
            Relatórios
        </a>
        <a href="settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i>
            Configurações
        </a>
        <a href="chat.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'chat.php' ? 'active' : ''; ?>">
            <i class="fas fa-comments"></i>
            Atendimento
        </a>
        <a href="payment_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'payment_settings.php' ? 'active' : ''; ?>">
            <i class="fas fa-money-bill"></i>
            Config. Pagamento
        </a>
        <a href="email_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_settings.php' ? 'active' : ''; ?>">
            <i class="fas fa-envelope-open-text"></i>
            Configurações de Email
        </a>
        <a href="email_templates.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_templates.php' ? 'active' : ''; ?>">
            <i class="fas fa-envelope"></i>
            Templates de Email
        </a>
        <a href="email_logs.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_logs.php' ? 'active' : ''; ?>">
            <i class="fas fa-history"></i>
            Logs de Email
        </a>
        <a href="visitors.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'visitors.php' ? 'active' : ''; ?>">
            <i class="fas fa-eye"></i>
            Logs de Visitantes
        </a>
        <a href="app_management.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'app_management.php' ? 'active' : ''; ?>">
            <i class="fas fa-mobile-alt"></i>
            Gerenciar App PWA
        </a>
        <a href="vpn_scanner.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'vpn_scanner.php' ? 'active' : ''; ?>">
            <i class="fas fa-shield-alt"></i>
            Scanner de VPN
        </a>
        <a href="api_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'api_settings.php' ? 'active' : ''; ?>">
            <i class="fas fa-key"></i>
            Config. API
        </a>
        <a href="logout.php" class="list-group-item list-group-item-action text-danger">
            <i class="fas fa-sign-out-alt"></i>
            Sair
        </a>
    </div>
</div>
