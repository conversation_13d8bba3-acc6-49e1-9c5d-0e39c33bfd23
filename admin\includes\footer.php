            </div> <!-- container-fluid -->
        </div> <!-- page-content-wrapper -->
    </div> <!-- wrapper -->

    <!-- Bootstrap Bundle with <PERSON><PERSON> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom Script -->
    <script>
        // Toggle do menu lateral
        document.getElementById('sidebarToggle').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('wrapper').classList.toggle('toggled');
        });
        
        // Ativar tooltips do Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Ativar menu baseado na URL atual
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.nav-sidebar .nav-link');
            
            // Função para remover active de todos os itens
            function removeAllActive() {
                menuItems.forEach(item => {
                    item.classList.remove('active');
                });
            }

            // Adiciona click listener em cada item do menu
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    removeAllActive();
                    this.classList.add('active');
                });
            });

            // Define o item ativo inicial baseado na URL
            const currentPath = window.location.pathname;
            menuItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.endsWith(href)) {
                    removeAllActive();
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
