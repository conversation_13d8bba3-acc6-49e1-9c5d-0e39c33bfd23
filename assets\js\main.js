// Variáveis globais
let currentProductId = null;

// Função para mostrar o modal de termos
function showTermsModal(productId) {
    // Armazenar o ID do produto
    currentProductId = productId;
    
    // Limpar campos do formulário
    document.getElementById('customer_name').value = '';
    document.getElementById('customer_email').value = '';
    
    // Mostrar modal
    const termsModal = new bootstrap.Modal(document.getElementById('termsModal'));
    termsModal.show();
}

// Aguardar o DOM estar pronto
document.addEventListener('DOMContentLoaded', function() {
    // Listener para o botão de confirmação
    document.getElementById('confirmPurchase').addEventListener('click', function() {
        // Validar formulário
        const customerName = document.getElementById('customer_name').value.trim();
        const customerEmail = document.getElementById('customer_email').value.trim();

        if (!customerName || !customerEmail) {
            Swal.fire({
                icon: 'error',
                title: 'Campos Obrigatórios',
                text: 'Por favor, preencha seu nome e email.'
            });
            return;
        }

        // Validar email
        if (!isValidEmail(customerEmail)) {
            Swal.fire({
                icon: 'error',
                title: 'Email Inválido',
                text: 'Por favor, insira um email válido.'
            });
            return;
        }

        // Fechar modal de termos
        const termsModal = bootstrap.Modal.getInstance(document.getElementById('termsModal'));
        termsModal.hide();

        // Iniciar processamento do pagamento
        processPayment(currentProductId);
    });
});

// Função para validar email
function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}
