<?php
require_once '../session.php';
require_once '../database/connection.php';

header('Content-Type: application/json');

try {
    // Pega o JSON do corpo da requisição
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['id']) || !isset($data['status'])) {
        throw new Exception('ID e status são obrigatórios');
    }
    
    $valid_statuses = ['pending', 'completed', 'cancelled'];
    if (!in_array($data['status'], $valid_statuses)) {
        throw new Exception('Status inválido');
    }
    
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    $stmt = $pdo->prepare("UPDATE vendas SET status = ?, updated_at = NOW() WHERE id = ?");
    $result = $stmt->execute([$data['status'], $data['id']]);
    
    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        throw new Exception('Erro ao atualizar status');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
