<?php
require_once 'database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2><PERSON><PERSON>do Usuá<PERSON> de Teste para Chat...</h2>";
    
    // Verificar se já existem usuários
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_users WHERE is_admin = 0");
    $userCount = $stmt->fetch()['count'];
    
    echo "<p>Usuários existentes: $userCount</p>";
    
    // Criar usuários de teste se não existirem
    $testUsers = [
        ['<PERSON><PERSON>', 'gab<PERSON><EMAIL>', '11987654321'],
        ['<PERSON>', '<EMAIL>', '11976543210'],
        ['<PERSON>', '<EMAIL>', '11965432109'],
        ['<PERSON>', '<EMAIL>', '11954321098'],
        ['<PERSON>', '<EMAIL>', '11943210987']
    ];
    
    foreach ($testUsers as $user) {
        // Verificar se o usuário já existe
        $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE email = ? AND is_admin = 0");
        $stmt->execute([$user[1]]);
        
        if (!$stmt->fetch()) {
            // Inserir usuário
            $stmt = $pdo->prepare("
                INSERT INTO chat_users (name, email, whatsapp, is_admin, created_at, last_activity) 
                VALUES (?, ?, ?, 0, NOW(), NOW())
            ");
            $stmt->execute($user);
            
            $userId = $pdo->lastInsertId();
            echo "<p>✓ Usuário criado: {$user[0]} (ID: $userId)</p>";
            
            // Criar algumas mensagens de exemplo
            $sampleMessages = [
                "Olá, preciso de ajuda com meu pedido",
                "Quando meu produto será entregue?",
                "Obrigado pelo atendimento!"
            ];
            
            foreach ($sampleMessages as $index => $msg) {
                $stmt = $pdo->prepare("
                    INSERT INTO chat_messages (user_id, message, is_admin, timestamp) 
                    VALUES (?, ?, 0, UNIX_TIMESTAMP() - ?)
                ");
                $stmt->execute([$userId, $msg, (count($sampleMessages) - $index) * 300]); // 5 min de diferença
            }
            
            // Criar sessão de chat
            $stmt = $pdo->prepare("
                INSERT INTO chat_sessions (user_id, status) 
                VALUES (?, 'active')
            ");
            $stmt->execute([$userId]);
            
        } else {
            echo "<p>- Usuário já existe: {$user[0]}</p>";
        }
    }
    
    // Verificar admin na tabela chat_users
    echo "<h3>Verificando Admin...</h3>";
    
    $stmt = $pdo->query("SELECT id, name, email FROM admins LIMIT 1");
    $admin = $stmt->fetch();
    
    if ($admin) {
        // Verificar se admin existe na tabela chat_users
        $stmt = $pdo->prepare("SELECT id FROM chat_users WHERE id = ? AND is_admin = 1");
        $stmt->execute([$admin['id']]);
        
        if (!$stmt->fetch()) {
            // Inserir admin na tabela chat_users
            $stmt = $pdo->prepare("
                INSERT INTO chat_users (id, name, email, is_admin, created_at, last_activity) 
                VALUES (?, ?, ?, 1, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                    name = VALUES(name), 
                    email = VALUES(email),
                    is_admin = 1
            ");
            $stmt->execute([$admin['id'], $admin['name'], $admin['email']]);
            echo "<p>✓ Admin sincronizado: {$admin['name']} (ID: {$admin['id']})</p>";
        } else {
            echo "<p>✓ Admin já existe na tabela chat_users</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Nenhum admin encontrado na tabela admins</p>";
    }
    
    // Estatísticas finais
    echo "<h3>Estatísticas:</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_users WHERE is_admin = 0");
    $userCount = $stmt->fetch()['count'];
    echo "<p>Total de usuários: $userCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_users WHERE is_admin = 1");
    $adminCount = $stmt->fetch()['count'];
    echo "<p>Total de admins: $adminCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_messages");
    $messageCount = $stmt->fetch()['count'];
    echo "<p>Total de mensagens: $messageCount</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_sessions");
    $sessionCount = $stmt->fetch()['count'];
    echo "<p>Total de sessões: $sessionCount</p>";
    
    echo "<h3 style='color: green;'>✓ Usuários de teste criados com sucesso!</h3>";
    echo "<p><a href='admin/chat.php' class='btn btn-primary'>Testar Chat Admin</a></p>";
    echo "<p><a href='chat.php' class='btn btn-success'>Testar Chat Público</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
    margin: 5px;
}
.btn:hover { background: #0056b3; }
.btn-success { background: #28a745; }
.btn-success:hover { background: #1e7e34; }
</style>
