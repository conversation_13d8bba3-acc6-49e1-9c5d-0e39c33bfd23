<?php
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailManager {
    private $mailer;
    private $pdo;
    private $lastError;

    public function __construct($pdo) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['smtp_log'] = '';
        
        $this->pdo = $pdo;
        $this->lastError = null;
        
        // Configurar PHPMailer
        $this->mailer = new PHPMailer(true);
        $this->mailer->SMTPDebug = SMTP::DEBUG_SERVER;
        
        // Capturar output do debug
        $this->mailer->Debugoutput = function($str, $level) {
            $_SESSION['smtp_log'] .= $str . "\n";
            error_log("SMTP Debug: " . $str);
        };

        try {
            // Buscar configurações de email do banco de dados
            $stmt = $this->pdo->query("SELECT * FROM email_settings LIMIT 1");
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$settings) {
                throw new Exception("Configurações de email não encontradas");
            }

            //Configurações do servidor
            $this->mailer->isSMTP();
            $this->mailer->Host = $settings['smtp_host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $settings['smtp_username'];
            $this->mailer->Password = $settings['smtp_password'];
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            $this->mailer->Port = $settings['smtp_port'];
            $this->mailer->CharSet = 'UTF-8';

            //Remetente padrão
            $this->mailer->setFrom($settings['from_email'], $settings['from_name']);
            
            error_log("Email settings loaded successfully: " . json_encode([
                'host' => $settings['smtp_host'],
                'port' => $settings['smtp_port'],
                'username' => $settings['smtp_username'],
                'secure' => PHPMailer::ENCRYPTION_SMTPS
            ]));
        } catch (Exception $e) {
            $this->lastError = $e->getMessage();
            error_log("Erro ao configurar email: " . $e->getMessage());
            throw $e;
        }
    }

    public function getLastError() {
        return $this->lastError;
    }

    public function sendTestEmail($to) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to);
            $this->mailer->Subject = 'Teste de Configuração SMTP';
            $this->mailer->Body = 'Este é um email de teste para verificar as configurações SMTP.';
            $this->mailer->send();
            
            // Registrar o envio no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (customer_email, subject, status)
                VALUES (?, ?, 'success')
            ");
            $stmt->execute([$to, 'Teste de Configuração SMTP']);
            
            return true;
        } catch (Exception $e) {
            // Registrar erro no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (customer_email, subject, status, error_message)
                VALUES (?, ?, 'error', ?)
            ");
            $stmt->execute([$to, 'Teste de Configuração SMTP', $e->getMessage()]);
            
            throw $e;
        }
    }

    private function getTemplate($templateName) {
        $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE name = ? AND is_active = 1 LIMIT 1");
        $stmt->execute([$templateName]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function replaceVariables($content, $variables) {
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        return $content;
    }

    private function generateDownloadLink($paymentId, $productId) {
        // Gerar um token único para o download
        $token = bin2hex(random_bytes(32));
        
        // Salvar o token no banco de dados
        $stmt = $this->pdo->prepare("
            INSERT INTO download_tokens (payment_id, product_id, token, expires_at)
            VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))
        ");
        $stmt->execute([$paymentId, $productId, $token]);
        
        // Retornar o link de download
        return "http://localhost/download.php?token=" . $token;
    }

    public function sendPurchaseEmail($paymentId) {
        try {
            // Buscar informações da compra
            $stmt = $this->pdo->prepare("
                SELECT 
                    p.*,
                    c.name as customer_name,
                    c.email as customer_email,
                    pr.name as product_name,
                    pr.id as product_id
                FROM payments p
                JOIN customers c ON p.customer_id = c.id
                JOIN products pr ON p.product_id = pr.id
                WHERE p.id = ?
            ");
            $stmt->execute([$paymentId]);
            $purchase = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$purchase) {
                throw new Exception("Compra não encontrada");
            }

            // Gerar link de download
            $downloadLink = $this->generateDownloadLink($paymentId, $purchase['product_id']);

            // Buscar template
            $template = $this->getTemplate('download_produto');
            if (!$template) {
                throw new Exception("Template de email não encontrado");
            }

            // Preparar variáveis para o template
            $variables = [
                'customer_name' => $purchase['customer_name'],
                'product_name' => $purchase['product_name'],
                'amount' => number_format($purchase['amount'], 2, ',', '.'),
                'purchase_date' => date('d/m/Y H:i', strtotime($purchase['created_at'])),
                'download_link' => $downloadLink
            ];

            // Substituir variáveis no template
            $subject = $this->replaceVariables($template['subject'], $variables);
            $body = $this->replaceVariables($template['body'], $variables);

            // Enviar email
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($purchase['customer_email']);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            $this->mailer->send();

            // Registrar envio no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (payment_id, customer_email, subject, status)
                VALUES (?, ?, ?, 'success')
            ");
            $stmt->execute([$paymentId, $purchase['customer_email'], $subject]);

            // Atualizar status do email na tabela de pagamentos
            $stmt = $this->pdo->prepare("
                UPDATE payments 
                SET email_sent = 1, email_sent_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$paymentId]);

            return true;
        } catch (Exception $e) {
            // Registrar erro no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (payment_id, customer_email, subject, status, error_message)
                VALUES (?, ?, ?, 'error', ?)
            ");
            $stmt->execute([
                $paymentId, 
                $purchase['customer_email'] ?? null,
                $template['subject'] ?? 'Erro ao enviar email de compra',
                $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    public function sendPaymentConfirmation($customerEmail, $customerName, $productInfo, $paymentInfo) {
        try {
            // Buscar template
            $template = $this->getTemplate('payment_confirmation');
            if (!$template) {
                throw new Exception("Template de email não encontrado");
            }

            // Preparar variáveis para o template
            $variables = [
                'customer_name' => $customerName,
                'product_name' => $productInfo['name'],
                'product_price' => $productInfo['price'],
                'payment_id' => $paymentInfo['id'],
                'payment_date' => $paymentInfo['date'],
                'access_url' => $productInfo['access_url'] ?? '#'
            ];

            // Substituir variáveis no template
            $subject = $this->replaceVariables($template['subject'], $variables);
            $body = $this->replaceVariables($template['body'], $variables);

            // Enviar email
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($customerEmail, $customerName);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            error_log("Enviando email para: " . $customerEmail);
            error_log("Subject: " . $subject);
            
            $this->mailer->send();
            
            // Registrar envio no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (customer_email, subject, status)
                VALUES (?, ?, 'success')
            ");
            $stmt->execute([$customerEmail, $subject]);
            
            return true;
        } catch (Exception $e) {
            error_log("Erro ao enviar email de confirmação: " . $e->getMessage());
            
            // Registrar erro no log
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs (customer_email, subject, status, error_message)
                VALUES (?, ?, 'error', ?)
            ");
            $stmt->execute([$customerEmail, $subject ?? 'N/A', $e->getMessage()]);
            
            throw $e;
        }
    }
}
