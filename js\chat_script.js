document.addEventListener('DOMContentLoaded', () => {
    // Verifica se o botão de abrir chat está presente
    const chatButton = document.getElementById('openChat');

    if (chatButton) {
        // Função para abrir o popup do chat
        chatButton.addEventListener('click', () => {
            console.log('Botão de chat clicado!');
            window.open('chat.php', '_blank', 'width=400,height=600');
        });
    } else {
        console.log('Botão não encontrado.');
    }
});
