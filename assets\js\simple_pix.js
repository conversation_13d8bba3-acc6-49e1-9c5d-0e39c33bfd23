/**
 * Sistema PIX Simplificado - KESUNG SITE
 * Versão corrigida e otimizada
 */

// Variáveis globais
let currentProductId = null;
let paymentCheckInterval = null;

// Função principal para comprar produto
async function buyProduct(productId) {
    try {
        console.log('Iniciando compra do produto:', productId);
        currentProductId = productId;
        
        // Buscar dados do produto
        const productData = await fetchProductData(productId);
        if (!productData) {
            throw new Error('Produto não encontrado');
        }
        
        // Mostrar modal de confirmação
        showConfirmationModal(productData);
        
    } catch (error) {
        console.error('Erro ao iniciar compra:', error);
        Swal.fire({
            icon: 'error',
            title: 'Erro',
            text: error.message || 'Erro ao carregar produto'
        });
    }
}

// Buscar dados do produto
async function fetchProductData(productId) {
    try {
        const response = await fetch(`get_product.php?id=${productId}`);
        const data = await response.json();
        
        if (data.success) {
            return data.data;
        } else {
            throw new Error(data.message || 'Erro ao buscar produto');
        }
    } catch (error) {
        console.error('Erro ao buscar produto:', error);
        return null;
    }
}

// Mostrar modal de confirmação
function showConfirmationModal(product) {
    Swal.fire({
        title: 'Confirmar Compra',
        html: `
            <div class="text-center">
                <h4>${product.name}</h4>
                <p class="text-muted">${product.description}</p>
                <h3 class="text-primary">${product.price_formatted}</h3>

                <div class="alert alert-warning mt-3 text-start" style="font-size: 12px;">
                    <strong>📋 Termos de Compra:</strong><br>
                    • Produto digital - Não haverá estorno após confirmação do pagamento<br>
                    • Manual e suporte técnico são vendidos separadamente<br>
                    • Acesso será enviado por e-mail após confirmação do pagamento<br>
                    • Ao continuar, você concorda com estes termos<br>
                    <a href="#" onclick="showTermsModal(); return false;" class="text-primary">
                        <i class="fas fa-file-contract me-1"></i>Ver termos completos
                    </a>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Aceito os Termos - Continuar',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#4318FF',
        cancelButtonColor: '#6c757d',
        width: '500px'
    }).then((result) => {
        if (result.isConfirmed) {
            showCustomerForm(product);
        }
    });
}

// Mostrar formulário do cliente
function showCustomerForm(product) {
    Swal.fire({
        title: 'Dados para Pagamento',
        html: `
            <form id="customer-form" class="text-start">
                <div class="mb-3">
                    <label for="customer_name" class="form-label">Nome Completo *</label>
                    <input type="text" class="form-control" id="customer_name" required>
                </div>
                <div class="mb-3">
                    <label for="customer_email" class="form-label">E-mail *</label>
                    <input type="email" class="form-control" id="customer_email" required>
                </div>
                <div class="mb-3">
                    <label for="customer_whatsapp" class="form-label">WhatsApp *</label>
                    <input type="tel" class="form-control" id="customer_whatsapp" placeholder="(11) 99999-9999" required>
                </div>
                <div class="mb-3">
                    <label for="customer_document" class="form-label">CPF/CNPJ *</label>
                    <input type="text" class="form-control" id="customer_document" placeholder="000.000.000-00" required>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="accept_terms" required>
                    <label class="form-check-label text-start" for="accept_terms" style="font-size: 11px;">
                        Concordo que este é um <strong>produto digital</strong> sem direito a estorno.
                        Manual e suporte são vendidos separadamente.
                    </label>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'Gerar PIX',
        cancelButtonText: 'Voltar',
        confirmButtonColor: '#4318FF',
        cancelButtonColor: '#6c757d',
        preConfirm: () => {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve(validateAndSubmitForm(product));
                }, 100);
            });
        }
    });
    
    // Adicionar máscaras aos campos
    setTimeout(() => {
        addInputMasks();
    }, 100);
}

// Validar e enviar formulário
function validateAndSubmitForm(product) {
    try {
        // Usar uma referência ao document para evitar conflitos
        const doc = window.document;

        // Verificar se os elementos existem antes de acessar
        const nameElement = doc.getElementById('customer_name');
        const emailElement = doc.getElementById('customer_email');
        const whatsappElement = doc.getElementById('customer_whatsapp');
        const documentElement = doc.getElementById('customer_document');
        const termsElement = doc.getElementById('accept_terms');

        if (!nameElement || !emailElement || !whatsappElement || !documentElement || !termsElement) {
            Swal.showValidationMessage('Erro: Formulário não encontrado. Tente novamente.');
            return false;
        }

        const customerName = nameElement.value.trim();
        const customerEmail = emailElement.value.trim();
        const customerWhatsapp = whatsappElement.value.trim();
        const customerDocument = documentElement.value.trim();

        // Validações
        if (!customerName || !customerEmail || !customerWhatsapp || !customerDocument) {
            Swal.showValidationMessage('Por favor, preencha todos os campos');
            return false;
        }

        if (!termsElement.checked) {
            Swal.showValidationMessage('Por favor, aceite os termos de compra para continuar');
            return false;
        }

        if (!isValidEmail(customerEmail)) {
            Swal.showValidationMessage('Por favor, insira um e-mail válido');
            return false;
        }

        const cleanWhatsapp = customerWhatsapp.replace(/\D/g, '');
        if (cleanWhatsapp.length < 10 || cleanWhatsapp.length > 11) {
            Swal.showValidationMessage('Por favor, insira um WhatsApp válido');
            return false;
        }

        const cleanDocument = customerDocument.replace(/\D/g, '');
        if (cleanDocument.length !== 11 && cleanDocument.length !== 14) {
            Swal.showValidationMessage('Por favor, insira um CPF ou CNPJ válido');
            return false;
        }

        // Processar pagamento
        return processPayment({
            product_id: product.id,
            customer_name: customerName,
            customer_email: customerEmail,
            customer_whatsapp: customerWhatsapp,
            customer_document: cleanDocument
        });

    } catch (error) {
        console.error('Erro na validação:', error);
        Swal.showValidationMessage('Erro interno. Tente novamente.');
        return false;
    }
}

// Processar pagamento
async function processPayment(paymentData) {
    try {
        // Mostrar loading
        Swal.fire({
            title: 'Processando...',
            text: 'Gerando seu PIX, aguarde...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        const response = await fetch('process_payment_v2.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });
        
        const responseText = await response.text();
        console.log('Resposta do servidor:', responseText);
        
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            throw new Error('Erro ao processar resposta do servidor');
        }
        
        if (!data.success) {
            throw new Error(data.error || 'Erro ao processar pagamento');
        }
        
        // Mostrar PIX
        showPixModal(data);
        
        return true;
        
    } catch (error) {
        console.error('Erro no pagamento:', error);
        Swal.fire({
            icon: 'error',
            title: 'Erro no Pagamento',
            text: error.message || 'Erro ao processar pagamento'
        });
        return false;
    }
}

// Mostrar modal do PIX
function showPixModal(paymentData) {
    Swal.fire({
        title: 'Pagamento PIX',
        html: `
            <div class="pix-modal-content">
                <div class="pix-amount">
                    <h4>Valor: R$ ${paymentData.amount}</h4>
                </div>

                <div class="pix-qr-container">
                    <img src="data:image/png;base64,${paymentData.qr_code}"
                         alt="QR Code PIX"
                         class="pix-qr-code">
                </div>

                <div class="pix-code-section">
                    <label class="pix-label">Código PIX (Copia e Cola):</label>
                    <div class="pix-input-group">
                        <input type="text" class="pix-input" id="pix-code"
                               value="${paymentData.pix_code}" readonly>
                        <button class="pix-copy-btn" type="button" onclick="copyPixCode()">
                            <i class="fas fa-copy"></i> Copiar
                        </button>
                    </div>
                </div>

                <div class="pix-info">
                    <i class="fas fa-info-circle"></i>
                    Escaneie o QR Code ou copie o código PIX para fazer o pagamento
                </div>

                <div id="payment-status" class="pix-status">
                    <div class="pix-spinner"></div>
                    Aguardando pagamento...
                </div>
            </div>

            <style>
                .pix-modal-content {
                    text-align: center;
                    padding: 10px;
                }

                .pix-amount {
                    margin-bottom: 20px;
                    color: #333;
                }

                .pix-qr-container {
                    margin: 20px 0;
                    display: flex;
                    justify-content: center;
                }

                .pix-qr-code {
                    width: 200px;
                    height: 200px;
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 10px;
                    background: white;
                }

                .pix-code-section {
                    margin: 20px 0;
                }

                .pix-label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 500;
                    color: #555;
                    font-size: 14px;
                }

                .pix-input-group {
                    display: flex;
                    gap: 8px;
                    align-items: stretch;
                }

                .pix-input {
                    flex: 1;
                    padding: 8px 12px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    font-size: 12px;
                    font-family: monospace;
                    background: #f8f9fa;
                }

                .pix-copy-btn {
                    padding: 8px 16px;
                    background: #4318FF;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 12px;
                    white-space: nowrap;
                }

                .pix-copy-btn:hover {
                    background: #3614CC;
                }

                .pix-info {
                    background: #e7f3ff;
                    color: #0c5460;
                    padding: 12px;
                    border-radius: 6px;
                    font-size: 13px;
                    margin: 15px 0;
                }

                .pix-status {
                    margin-top: 20px;
                    padding: 10px;
                    font-size: 14px;
                    color: #666;
                }

                .pix-spinner {
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #4318FF;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-right: 8px;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                @media (max-width: 480px) {
                    .pix-modal-content {
                        padding: 5px;
                    }

                    .pix-qr-code {
                        width: 160px;
                        height: 160px;
                    }

                    .pix-input-group {
                        flex-direction: column;
                        gap: 10px;
                    }

                    .pix-copy-btn {
                        width: 100%;
                    }

                    .pix-input {
                        font-size: 11px;
                    }
                }
            </style>
        `,
        showConfirmButton: false,
        allowOutsideClick: false,
        width: 'auto',
        customClass: {
            popup: 'pix-modal-popup'
        }
    });

    // Iniciar verificação de pagamento
    startPaymentCheck(paymentData.payment_id);
}

// Copiar código PIX
function copyPixCode() {
    const pixCodeInput = window.document.getElementById('pix-code');
    if (pixCodeInput && pixCodeInput.value) {
        // Usar API moderna de clipboard se disponível
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(pixCodeInput.value).then(() => {
                showToast('Código PIX copiado!', 'success');
            }).catch((err) => {
                console.error('Erro ao copiar:', err);
                fallbackCopyMethod(pixCodeInput);
            });
        } else {
            fallbackCopyMethod(pixCodeInput);
        }
    }
}

// Método de fallback para copiar
function fallbackCopyMethod(input) {
    try {
        input.select();
        input.setSelectionRange(0, 99999);
        window.document.execCommand('copy');
        showToast('Código PIX copiado!', 'success');
    } catch (err) {
        console.error('Erro ao copiar:', err);
        showToast('Erro ao copiar código', 'error');
    }
}

// Iniciar verificação de pagamento
function startPaymentCheck(paymentId) {
    if (paymentCheckInterval) {
        clearInterval(paymentCheckInterval);
    }
    
    paymentCheckInterval = setInterval(() => {
        checkPaymentStatus(paymentId);
    }, 3000);
    
    // Parar verificação após 10 minutos
    setTimeout(() => {
        if (paymentCheckInterval) {
            clearInterval(paymentCheckInterval);
            updatePaymentStatus('Tempo esgotado. Tente novamente.', 'warning');
        }
    }, 600000);
}

// Verificar status do pagamento
async function checkPaymentStatus(paymentId) {
    try {
        const response = await fetch(`check_payment_status.php?payment_id=${paymentId}`);
        const data = await response.json();
        
        if (data.success && data.status === 'paid') {
            clearInterval(paymentCheckInterval);
            showPaymentSuccess();
        }
    } catch (error) {
        console.error('Erro ao verificar pagamento:', error);
    }
}

// Mostrar sucesso do pagamento
function showPaymentSuccess() {
    Swal.fire({
        icon: 'success',
        title: 'Pagamento Aprovado!',
        text: 'Seu pagamento foi confirmado. Você receberá o acesso por e-mail.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745'
    }).then(() => {
        window.location.reload();
    });
}

// Atualizar status do pagamento
function updatePaymentStatus(message, type = 'info') {
    const statusElement = window.document.getElementById('payment-status');
    if (statusElement) {
        const icon = type === 'success' ? 'check-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 'info-circle';
        const color = type === 'success' ? 'text-success' : 
                     type === 'warning' ? 'text-warning' : 'text-info';
        
        statusElement.innerHTML = `
            <i class="fas fa-${icon} ${color}"></i>
            ${message}
        `;
    }
}

// Adicionar máscaras aos campos
function addInputMasks() {
    // Máscara para WhatsApp
    const whatsappInput = window.document.getElementById('customer_whatsapp');
    if (whatsappInput) {
        whatsappInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                if (value.length > 2) {
                    value = `(${value.substring(0, 2)}) ${value.substring(2)}`;
                }
                if (value.length > 10) {
                    value = value.substring(0, 10) + '-' + value.substring(10);
                }
            }
            e.target.value = value;
        });
    }
    
    // Máscara para CPF/CNPJ
    const documentInput = window.document.getElementById('customer_document');
    if (documentInput) {
        documentInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            
            if (value.length <= 11) {
                // CPF: 000.000.000-00
                if (value.length > 9) {
                    value = value.substring(0, 3) + '.' + value.substring(3, 6) + '.' + 
                           value.substring(6, 9) + '-' + value.substring(9);
                } else if (value.length > 6) {
                    value = value.substring(0, 3) + '.' + value.substring(3, 6) + '.' + value.substring(6);
                } else if (value.length > 3) {
                    value = value.substring(0, 3) + '.' + value.substring(3);
                }
            } else {
                // CNPJ: 00.000.000/0000-00
                if (value.length > 12) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + 
                           value.substring(5, 8) + '/' + value.substring(8, 12) + '-' + value.substring(12);
                } else if (value.length > 8) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + 
                           value.substring(5, 8) + '/' + value.substring(8);
                } else if (value.length > 5) {
                    value = value.substring(0, 2) + '.' + value.substring(2, 5) + '.' + value.substring(5);
                } else if (value.length > 2) {
                    value = value.substring(0, 2) + '.' + value.substring(2);
                }
            }
            
            e.target.value = value;
        });
    }
}

// Validar e-mail
function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Mostrar toast
function showToast(message, type = 'info') {
    const toast = window.document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
        ${message}
    `;
    
    window.document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Função para mostrar termos em modal
function showTermsModal() {
    Swal.fire({
        title: '<i class="fas fa-file-contract me-2"></i>Termos de Compra',
        html: `
            <div class="terms-modal-content">
                <div class="terms-section">
                    <h6><i class="fas fa-laptop-code me-2"></i>Natureza do Produto</h6>
                    <p>Os produtos oferecidos são <strong>exclusivamente digitais</strong> (scripts, sistemas, cursos).</p>
                </div>

                <div class="terms-section">
                    <h6><i class="fas fa-ban me-2"></i>Política de Estorno</h6>
                    <p><strong class="text-danger">NÃO HÁ ESTORNO</strong> para produtos digitais após confirmação do pagamento.</p>
                </div>

                <div class="terms-section">
                    <h6><i class="fas fa-tools me-2"></i>Suporte e Manual</h6>
                    <p>Suporte técnico e manuais detalhados são <strong>vendidos separadamente</strong>.</p>
                </div>

                <div class="terms-section">
                    <h6><i class="fas fa-envelope me-2"></i>Entrega</h6>
                    <p>Acesso enviado por e-mail em até <strong>24 horas úteis</strong>.</p>
                </div>

                <div class="terms-section">
                    <h6><i class="fas fa-shield-alt me-2"></i>Licença de Uso</h6>
                    <p>Licença pessoal/comercial conforme especificado.</p>
                </div>

                <div class="alert alert-warning">
                    <small><strong>Importante:</strong> Ao continuar, você declara ter lido e concordado com todos os termos.</small>
                </div>
            </div>

            <style>
                .terms-modal-content {
                    text-align: left;
                    max-height: 400px;
                    overflow-y: auto;
                }

                .terms-section {
                    margin-bottom: 15px;
                    padding: 10px;
                    border-left: 3px solid #4318FF;
                    background: #f8f9fa;
                    border-radius: 0 5px 5px 0;
                }

                .terms-section h6 {
                    color: #4318FF;
                    margin-bottom: 8px;
                    font-size: 14px;
                }

                .terms-section p {
                    margin: 0;
                    font-size: 13px;
                    line-height: 1.4;
                }
            </style>
        `,
        width: '600px',
        confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
        confirmButtonColor: '#4318FF',
        customClass: {
            popup: 'terms-modal-popup'
        }
    });
}

// Log de inicialização
console.log('Sistema PIX Simplificado carregado');
