<?php
require_once 'database/connection.php';

echo "<h2>🔍 Verificação da Tabela Orders</h2>";

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    // Verificar estrutura da tabela orders
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Colunas Existentes na Tabela 'orders':</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🔧 Colunas Necessárias para o Sistema de Pagamento:</h3>";
    
    $requiredColumns = [
        'customer_id' => 'INT',
        'customer_phone' => 'VARCHAR(20)',
        'expires_at' => 'TIMESTAMP',
        'pix_qr_code' => 'TEXT',
        'pix_code' => 'TEXT'
    ];
    
    $missingColumns = [];
    
    echo "<ul>";
    foreach ($requiredColumns as $column => $type) {
        if (in_array($column, $existingColumns)) {
            echo "<li>✅ <strong>$column</strong> ($type) - EXISTE</li>";
        } else {
            echo "<li>❌ <strong>$column</strong> ($type) - FALTANDO</li>";
            $missingColumns[$column] = $type;
        }
    }
    echo "</ul>";
    
    if (!empty($missingColumns)) {
        echo "<h3>🛠️ Adicionando Colunas Faltantes:</h3>";
        
        foreach ($missingColumns as $column => $type) {
            try {
                $sql = "ALTER TABLE orders ADD COLUMN $column $type";
                if ($column === 'expires_at') {
                    $sql .= " NULL";
                } elseif ($column === 'customer_phone') {
                    $sql .= " DEFAULT NULL";
                } elseif (in_array($column, ['pix_qr_code', 'pix_code'])) {
                    $sql .= " NULL";
                }
                
                $pdo->exec($sql);
                echo "<p>✅ Coluna <strong>$column</strong> adicionada com sucesso</p>";
            } catch (Exception $e) {
                echo "<p>❌ Erro ao adicionar coluna <strong>$column</strong>: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<h3>📋 Estrutura Atualizada:</h3>";
        $stmt = $pdo->query("DESCRIBE orders");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            $isNew = in_array($column['Field'], array_keys($missingColumns));
            $style = $isNew ? "background-color: #d4edda;" : "";
            
            echo "<tr style='$style'>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🧪 Teste de Inserção:</h3>";
    
    // Testar inserção com dados de exemplo
    try {
        $testData = [
            'customer_id' => 1,
            'product_id' => 1,
            'customer_name' => 'Teste Cliente',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '11999999999',
            'customer_document' => '12345678901',
            'amount' => 100.00,
            'payment_method' => 'pix',
            'payment_status' => 'pending',
            'external_reference' => 'TEST_' . time(),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ];
        
        $sql = "INSERT INTO orders (
            customer_id, product_id, customer_name, customer_email, 
            customer_phone, customer_document, amount, payment_method, 
            payment_status, external_reference, expires_at, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $testData['customer_id'],
            $testData['product_id'],
            $testData['customer_name'],
            $testData['customer_email'],
            $testData['customer_phone'],
            $testData['customer_document'],
            $testData['amount'],
            $testData['payment_method'],
            $testData['payment_status'],
            $testData['external_reference'],
            $testData['expires_at']
        ]);
        
        $testOrderId = $pdo->lastInsertId();
        echo "<p>✅ Teste de inserção bem-sucedido! ID do pedido: $testOrderId</p>";
        
        // Remover dados de teste
        $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
        echo "<p>🧹 Dados de teste removidos</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Erro no teste de inserção: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724; margin-top: 20px;'>";
    echo "<h4>✅ Verificação Concluída!</h4>";
    echo "<p>A tabela orders agora deve estar compatível com o sistema de pagamento.</p>";
    echo "<p><strong>Próximo passo:</strong> Teste o pagamento novamente</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante a verificação:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background: #e9ecef;
    font-weight: bold;
}

h1, h2 {
    color: #495057;
}

h1 {
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
    margin-top: 30px;
}
</style>
