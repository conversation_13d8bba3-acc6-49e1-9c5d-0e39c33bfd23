<?php
require_once 'database/connection.php';

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>Verificando tabelas de chat...</h2>";
    
    // Verificar tabelas existentes
    $stmt = $pdo->query("SHOW TABLES LIKE 'chat_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>Tabelas de chat encontradas:</h3>";
    if (empty($tables)) {
        echo "<p style='color: red;'>Nenhuma tabela de chat encontrada!</p>";
    } else {
        foreach ($tables as $table) {
            echo "<p>✓ $table</p>";
        }
    }
    
    // Criar tabelas necessárias se não existirem
    echo "<h3>Criando tabelas necessárias...</h3>";
    
    // Tabela chat_users
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            whatsapp VARCHAR(20),
            is_admin TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_is_admin (is_admin),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_users criada/verificada</p>";
    
    // Tabela chat_messages
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL,
            is_admin TINYINT(1) DEFAULT 0,
            timestamp INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_timestamp (timestamp),
            INDEX idx_is_admin (is_admin),
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_messages criada/verificada</p>";
    
    // Tabela chat_sessions
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            status ENUM('pending', 'active', 'closed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            FOREIGN KEY (user_id) REFERENCES chat_users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "<p>✓ Tabela chat_sessions criada/verificada</p>";
    
    // Verificar estrutura das tabelas
    echo "<h3>Estrutura das tabelas:</h3>";
    
    $tables_to_check = ['chat_users', 'chat_messages', 'chat_sessions'];
    foreach ($tables_to_check as $table) {
        echo "<h4>$table:</h4>";
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll();
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Verificar dados existentes
    echo "<h3>Dados existentes:</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_users");
    $count = $stmt->fetch()['count'];
    echo "<p>Usuários de chat: $count</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_messages");
    $count = $stmt->fetch()['count'];
    echo "<p>Mensagens de chat: $count</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_sessions");
    $count = $stmt->fetch()['count'];
    echo "<p>Sessões de chat: $count</p>";
    
    echo "<h3 style='color: green;'>✓ Verificação concluída com sucesso!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}
?>
