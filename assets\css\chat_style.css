:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --background-color: #f1f5f9;
    --border-color: #e2e8f0;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.chat-wrapper {
    width: 100%;
    max-width: 1200px;
    height: calc(100vh - 2rem);
}

.chat-container {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    height: 100%;
    display: flex;
    overflow: hidden;
}

/* Users List */
.users-list {
    width: 300px;
    border-right: 1px solid var(--border-color);
    background-color: white;
    display: flex;
    flex-direction: column;
}

.list-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
}

.list-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.users-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.chat-header {
    padding: 1.25rem;
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--success-color);
}

.status-indicator.online {
    background-color: var(--success-color);
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background-color: var(--background-color);
}

/* Chat Input Area */
.chat-input-wrapper {
    padding: 1rem;
    background-color: white;
    border-top: 1px solid var(--border-color);
}

.chat-tools {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.tool-btn {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    color: var(--secondary-color);
    transition: all 0.2s ease;
}

.tool-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.message-input-container {
    display: flex;
    gap: 0.5rem;
}

#message-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

#message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.send-btn {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.send-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.send-btn:active {
    transform: translateY(0);
}

/* Emoji Picker */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
}

.emoji-btn {
    padding: 0.5rem;
    font-size: 1.25rem;
    cursor: pointer;
    text-align: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.emoji-btn:hover {
    background-color: var(--background-color);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 0;
    }

    .chat-wrapper {
        height: 100vh;
    }

    .chat-container {
        border-radius: 0;
    }

    .users-list {
        width: 100%;
        position: absolute;
        height: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .users-list.active {
        transform: translateX(0);
    }

    .chat-area {
        width: 100%;
    }
}
