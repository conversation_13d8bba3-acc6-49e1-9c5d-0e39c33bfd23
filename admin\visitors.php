<?php
require_once 'includes/auth_check.php';
require_once 'includes/header.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Criar tabelas se não existirem
try {
    // Verificar e criar tabela visitors
    $stmt = $pdo->query("SHOW TABLES LIKE 'visitors'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE visitors (
                id int(11) NOT NULL AUTO_INCREMENT,
                ip varchar(45) NOT NULL,
                ip_address varchar(45) NOT NULL,
                user_agent text,
                country varchar(100) DEFAULT 'Brasil',
                city varchar(100) DEFAULT 'Desconhecido',
                is_vpn tinyint(1) DEFAULT 0,
                visit_count int(11) DEFAULT 1,
                first_visit timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                last_visit timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                visit_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY ip_unique (ip),
                KEY ip_address (ip_address),
                KEY visit_time (visit_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    // Verificar e criar tabela visitor_access
    $stmt = $pdo->query("SHOW TABLES LIKE 'visitor_access'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE visitor_access (
                id int(11) NOT NULL AUTO_INCREMENT,
                ip_address varchar(45) NOT NULL,
                city varchar(100) DEFAULT NULL,
                state varchar(100) DEFAULT NULL,
                referrer_source varchar(100) DEFAULT NULL,
                referrer_url varchar(500) DEFAULT NULL,
                user_agent text,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY ip_address (ip_address),
                KEY referrer_source (referrer_source)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    // Verificar e criar tabela visitor_logs
    $stmt = $pdo->query("SHOW TABLES LIKE 'visitor_logs'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("
            CREATE TABLE visitor_logs (
                id int(11) NOT NULL AUTO_INCREMENT,
                visitor_id int(11) DEFAULT NULL,
                ip_address varchar(45) NOT NULL,
                user_agent text,
                page_url varchar(500) DEFAULT NULL,
                referrer_url varchar(500) DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY visitor_id (visitor_id),
                KEY ip_address (ip_address)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
} catch (Exception $e) {
    // Ignorar erros de criação de tabela
}

// Função para detectar fonte do referrer
function detectReferrerSource($referrer) {
    if (empty($referrer)) return 'Direto';
    
    $referrer = strtolower($referrer);
    
    if (strpos($referrer, 'whatsapp') !== false || strpos($referrer, 'wa.me') !== false) {
        return 'WhatsApp';
    } elseif (strpos($referrer, 'instagram') !== false) {
        return 'Instagram';
    } elseif (strpos($referrer, 'facebook') !== false) {
        return 'Facebook';
    } elseif (strpos($referrer, 'youtube') !== false) {
        return 'YouTube';
    } elseif (strpos($referrer, 'telegram') !== false || strpos($referrer, 't.me') !== false) {
        return 'Telegram';
    } elseif (strpos($referrer, 'twitter') !== false || strpos($referrer, 'x.com') !== false) {
        return 'Twitter/X';
    } elseif (strpos($referrer, 'tiktok') !== false) {
        return 'TikTok';
    } elseif (strpos($referrer, 'linkedin') !== false) {
        return 'LinkedIn';
    } elseif (strpos($referrer, 'google') !== false) {
        return 'Google';
    } elseif (strpos($referrer, 'bing') !== false) {
        return 'Bing';
    } else {
        return 'Outro Site';
    }
}

// Inserir dados de exemplo se as tabelas estiverem vazias
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM visitors");
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        // Dados de exemplo para visitors
        $sampleVisitors = [
            ['*************', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'Brasil', 'São Paulo', 0, 5],
            ['************', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', 'Brasil', 'Rio de Janeiro', 0, 3],
            ['*********', '*********', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0', 'Brasil', 'Belo Horizonte', 1, 1]
        ];

        $stmt = $pdo->prepare("
            INSERT INTO visitors (ip, ip_address, user_agent, country, city, is_vpn, visit_count, first_visit, last_visit, visit_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW() - INTERVAL FLOOR(RAND() * 24) HOUR, NOW() - INTERVAL FLOOR(RAND() * 2) HOUR, NOW() - INTERVAL FLOOR(RAND() * 24) HOUR)
        ");

        foreach ($sampleVisitors as $visitor) {
            $stmt->execute($visitor);
        }

        // Dados de exemplo para visitor_access
        $sampleAccess = [
            ['*************', 'São Paulo', 'SP', 'WhatsApp', 'https://wa.me/5511999999999', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'],
            ['************', 'Rio de Janeiro', 'RJ', 'Instagram', 'https://instagram.com/profile', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)'],
            ['*********', 'Belo Horizonte', 'MG', 'Google', 'https://google.com/search?q=kesung', 'Mozilla/5.0 (Android 11; Mobile)']
        ];

        $stmt = $pdo->prepare("
            INSERT INTO visitor_access (ip_address, city, state, referrer_source, referrer_url, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW() - INTERVAL FLOOR(RAND() * 12) HOUR)
        ");

        foreach ($sampleAccess as $access) {
            $stmt->execute($access);
        }

        // Dados de exemplo para visitor_logs
        $sampleLogs = [
            [1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '/', 'https://wa.me/5511999999999'],
            [1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '/produto/1', '/'],
            [2, '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)', '/', 'https://instagram.com/profile'],
            [3, '*********', 'Mozilla/5.0 (Android 11; Mobile)', '/', 'https://google.com/search?q=kesung']
        ];

        $stmt = $pdo->prepare("
            INSERT INTO visitor_logs (visitor_id, ip_address, user_agent, page_url, referrer_url, created_at)
            VALUES (?, ?, ?, ?, ?, NOW() - INTERVAL FLOOR(RAND() * 6) HOUR)
        ");

        foreach ($sampleLogs as $log) {
            $stmt->execute($log);
        }
    }
} catch (Exception $e) {
    // Ignorar erros de inserção de dados de exemplo
}

// Buscar dados das tabelas
try {
    // Visitors
    $stmt = $pdo->query("SELECT * FROM visitors ORDER BY last_visit DESC LIMIT 50");
    $visitors = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Visitor Access
    $stmt = $pdo->query("SELECT * FROM visitor_access ORDER BY created_at DESC LIMIT 50");
    $visitorAccess = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Visitor Logs
    $stmt = $pdo->query("SELECT * FROM visitor_logs ORDER BY created_at DESC LIMIT 50");
    $visitorLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Estatísticas de referrers
    $stmt = $pdo->query("
        SELECT referrer_source, COUNT(*) as count 
        FROM visitor_access 
        WHERE referrer_source IS NOT NULL 
        GROUP BY referrer_source 
        ORDER BY count DESC
    ");
    $referrerStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $visitors = [];
    $visitorAccess = [];
    $visitorLogs = [];
    $referrerStats = [];
    $error = $e->getMessage();
}
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Logs de Visitantes</h2>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- Estatísticas de Referrers -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📊 Fontes de Tráfego Mais Acessadas</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($referrerStats as $stat): ?>
                            <div class="col-md-2 col-sm-4 col-6 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <div class="fs-4 mb-2">
                                        <?php
                                        $icons = [
                                            'WhatsApp' => '💬',
                                            'Instagram' => '📷',
                                            'Facebook' => '👥',
                                            'YouTube' => '📺',
                                            'Telegram' => '✈️',
                                            'Twitter/X' => '🐦',
                                            'TikTok' => '🎵',
                                            'Google' => '🔍',
                                            'Direto' => '🌐'
                                        ];
                                        echo $icons[$stat['referrer_source']] ?? '🔗';
                                        ?>
                                    </div>
                                    <div class="fw-bold"><?php echo $stat['count']; ?></div>
                                    <small class="text-muted"><?php echo $stat['referrer_source']; ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabela Visitors -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">👥 Visitantes (<?php echo count($visitors); ?> registros)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>IP</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>País</th>
                            <th>Cidade</th>
                            <th>VPN</th>
                            <th>Visitas</th>
                            <th>Primeira Visita</th>
                            <th>Última Visita</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($visitors as $visitor): ?>
                            <tr>
                                <td><?php echo $visitor['id']; ?></td>
                                <td><code><?php echo htmlspecialchars($visitor['ip']); ?></code></td>
                                <td><code><?php echo htmlspecialchars($visitor['ip_address']); ?></code></td>
                                <td><small><?php echo htmlspecialchars(substr($visitor['user_agent'], 0, 50)) . '...'; ?></small></td>
                                <td><?php echo htmlspecialchars($visitor['country']); ?></td>
                                <td><?php echo htmlspecialchars($visitor['city']); ?></td>
                                <td>
                                    <?php if ($visitor['is_vpn']): ?>
                                        <span class="badge bg-warning">⚠️ VPN</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">✅ Normal</span>
                                    <?php endif; ?>
                                </td>
                                <td><span class="badge bg-info"><?php echo $visitor['visit_count']; ?></span></td>
                                <td><small><?php echo date('d/m/Y H:i', strtotime($visitor['first_visit'])); ?></small></td>
                                <td><small><?php echo date('d/m/Y H:i', strtotime($visitor['last_visit'])); ?></small></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Tabela Visitor Access -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">🌐 Acessos Detalhados (<?php echo count($visitorAccess); ?> registros)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>IP Address</th>
                            <th>Cidade</th>
                            <th>Estado</th>
                            <th>Fonte</th>
                            <th>URL de Referência</th>
                            <th>User Agent</th>
                            <th>Data/Hora</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($visitorAccess as $access): ?>
                            <tr>
                                <td><?php echo $access['id']; ?></td>
                                <td><code><?php echo htmlspecialchars($access['ip_address']); ?></code></td>
                                <td><?php echo htmlspecialchars($access['city'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($access['state'] ?? 'N/A'); ?></td>
                                <td>
                                    <?php if ($access['referrer_source']): ?>
                                        <span class="badge bg-primary">
                                            <?php
                                            $icons = [
                                                'WhatsApp' => '💬',
                                                'Instagram' => '📷',
                                                'Facebook' => '👥',
                                                'YouTube' => '📺',
                                                'Telegram' => '✈️',
                                                'Twitter/X' => '🐦',
                                                'Google' => '🔍'
                                            ];
                                            echo ($icons[$access['referrer_source']] ?? '🔗') . ' ' . $access['referrer_source'];
                                            ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Direto</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($access['referrer_url']): ?>
                                        <small><a href="<?php echo htmlspecialchars($access['referrer_url']); ?>" target="_blank" class="text-decoration-none">
                                            <?php echo htmlspecialchars(substr($access['referrer_url'], 0, 40)) . '...'; ?>
                                        </a></small>
                                    <?php else: ?>
                                        <small class="text-muted">N/A</small>
                                    <?php endif; ?>
                                </td>
                                <td><small><?php echo htmlspecialchars(substr($access['user_agent'], 0, 30)) . '...'; ?></small></td>
                                <td><small><?php echo date('d/m/Y H:i', strtotime($access['created_at'])); ?></small></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Tabela Visitor Logs -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">📋 Logs de Navegação (<?php echo count($visitorLogs); ?> registros)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Visitor ID</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>Página Visitada</th>
                            <th>URL de Referência</th>
                            <th>Data/Hora</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($visitorLogs as $log): ?>
                            <tr>
                                <td><?php echo $log['id']; ?></td>
                                <td>
                                    <?php if ($log['visitor_id']): ?>
                                        <span class="badge bg-info">#<?php echo $log['visitor_id']; ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td><code><?php echo htmlspecialchars($log['ip_address']); ?></code></td>
                                <td><small><?php echo htmlspecialchars(substr($log['user_agent'], 0, 30)) . '...'; ?></small></td>
                                <td>
                                    <?php if ($log['page_url']): ?>
                                        <code><?php echo htmlspecialchars($log['page_url']); ?></code>
                                    <?php else: ?>
                                        <small class="text-muted">N/A</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['referrer_url']): ?>
                                        <small><a href="<?php echo htmlspecialchars($log['referrer_url']); ?>" target="_blank" class="text-decoration-none">
                                            <?php echo htmlspecialchars(substr($log['referrer_url'], 0, 30)) . '...'; ?>
                                        </a></small>
                                    <?php else: ?>
                                        <small class="text-muted">N/A</small>
                                    <?php endif; ?>
                                </td>
                                <td><small><?php echo date('d/m/Y H:i', strtotime($log['created_at'])); ?></small></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Resumo das Informações -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">👥 Total de Visitantes</h5>
                    <h2 class="text-primary"><?php echo count($visitors); ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">🌐 Total de Acessos</h5>
                    <h2 class="text-success"><?php echo count($visitorAccess); ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">📋 Total de Logs</h5>
                    <h2 class="text-info"><?php echo count($visitorLogs); ?></h2>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    font-size: 0.875rem;
}
.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}
.badge {
    font-size: 0.75rem;
}
code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.8rem;
}
</style>

<?php require_once 'includes/footer.php'; ?>
