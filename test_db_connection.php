<?php
header('Content-Type: application/json');

try {
    require_once 'database/connection.php';
    
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    // Testar conexão
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    // Obter informações do banco
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $dbInfo = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT VERSION() as version");
    $versionInfo = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'message' => 'Conexão estabelecida com sucesso',
        'database' => $dbInfo['db_name'],
        'version' => $versionInfo['version'],
        'test_result' => $result['test']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro na conexão: ' . $e->getMessage()
    ]);
}
?>
