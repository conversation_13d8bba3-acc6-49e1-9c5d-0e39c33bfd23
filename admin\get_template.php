<?php
require_once 'includes/auth_check.php';
header('Content-Type: application/json');

try {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID do template não fornecido');
    }

    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$template) {
        throw new Exception('Template não encontrado');
    }

    // Simular substituição de variáveis com valores de exemplo
    $variables = json_decode($template['variables'], true);
    $body = $template['body'];
    
    foreach ($variables as $variable) {
        $body = str_replace(
            '{' . $variable . '}',
            '<span class="text-primary">[Exemplo: ' . ucwords(str_replace('_', ' ', $variable)) . ']</span>',
            $body
        );
    }

    $template['body'] = $body;

    echo json_encode([
        'success' => true,
        'template' => $template
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
