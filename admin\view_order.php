<?php
require_once 'database/connection.php';

// Check for ID first, before any output
if (!isset($_GET['id'])) {
    header('Location: dashboard.php');
    exit;
}

$orderId = $_GET['id'];
$db = Database::getInstance();
$pdo = $db->getConnection();

// Buscar informações do pedido
$stmt = $pdo->prepare("
    SELECT o.*, p.name as product_name, p.description as product_description, p.price as product_price 
    FROM orders o 
    LEFT JOIN products p ON o.product_id = p.id 
    WHERE o.id = ?
");
$stmt->execute([$orderId]);
$order = $stmt->fetch();

if (!$order) {
    header('Location: dashboard.php');
    exit;
}

// Formatar status para exibição
$statusClasses = [
    'approved' => 'success',
    'pending' => 'warning',
    'cancelled' => 'danger',
    'refunded' => 'info'
];
$statusClass = $statusClasses[$order['payment_status']] ?? 'secondary';

// Now include the header and sidebar
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

?>

<div class="content-wrapper" style="min-height: 100vh;">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Detalhes do Pedido #<?php echo htmlspecialchars($order['id']); ?></h1>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Informações do Pedido</h3>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th style="width: 150px;">Status:</th>
                                    <td>
                                        <span class="badge badge-<?php echo $statusClass; ?>">
                                            <?php echo htmlspecialchars(ucfirst($order['payment_status'])); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Data:</th>
                                    <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <th>Método:</th>
                                    <td><?php echo htmlspecialchars(strtoupper($order['payment_method'])); ?></td>
                                </tr>
                                <tr>
                                    <th>ID Pagamento:</th>
                                    <td><?php echo htmlspecialchars($order['payment_id']); ?></td>
                                </tr>
                                <tr>
                                    <th>Valor Total:</th>
                                    <td>R$ <?php echo number_format($order['total_amount'], 2, ',', '.'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Informações do Cliente</h3>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th style="width: 150px;">Nome:</th>
                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>
                                        <a href="view_customer.php?email=<?php echo urlencode($order['customer_email']); ?>">
                                            <?php echo htmlspecialchars($order['customer_email']); ?>
                                        </a>
                                    </td>
                                </tr>
                                <?php if (!empty($order['customer_whatsapp'])): ?>
                                <tr>
                                    <th>WhatsApp:</th>
                                    <td>
                                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $order['customer_whatsapp']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($order['customer_whatsapp']); ?>
                                        </a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3 class="card-title">Produto</h3>
                            <button class="btn btn-primary" onclick="resendEmail(<?php echo $order['id']; ?>)">
                                Reenviar Email
                            </button>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th style="width: 150px;">Nome:</th>
                                    <td><?php echo htmlspecialchars($order['product_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Descrição:</th>
                                    <td><?php echo nl2br(htmlspecialchars($order['product_description'])); ?></td>
                                </tr>
                                <tr>
                                    <th>Preço Unitário:</th>
                                    <td>R$ <?php echo number_format($order['product_price'], 2, ',', '.'); ?></td>
                                </tr>
                                <tr>
                                    <th>Quantidade:</th>
                                    <td>1</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($order['payment_status'] === 'pending'): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="button" class="btn btn-success" onclick="approvePayment(<?php echo $order['id']; ?>)">
                                <i class="fas fa-check"></i> Aprovar Pagamento
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<script>
function approvePayment(orderId) {
    if (confirm('Tem certeza que deseja aprovar este pagamento?')) {
        fetch('update_payment_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: orderId,
                status: 'approved'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Pagamento aprovado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao aprovar pagamento: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao processar a requisição');
        });
    }
}

function resendEmail(orderId) {
    if (!confirm('Deseja reenviar o email para este pedido?')) {
        return;
    }
    
    fetch('resend_order_email.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ order_id: orderId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Email reenviado com sucesso!');
            location.reload();
        } else {
            alert('Erro ao reenviar email: ' + data.message);
        }
    })
    .catch(error => {
        alert('Erro ao reenviar email: ' + error);
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>
